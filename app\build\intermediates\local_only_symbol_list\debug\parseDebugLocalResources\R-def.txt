R_DEF: Internal format may change without notice
local
anim slide_in_left
anim slide_in_left_smooth
anim slide_in_right
anim slide_in_right_smooth
anim slide_out_left
anim slide_out_left_smooth
anim slide_out_right
anim slide_out_right_smooth
anim slide_parallel_left
anim slide_parallel_right
animator no_elevation
color black
color white
drawable gradient_separator
drawable ic_apk
drawable ic_arrow_upward
drawable ic_bookmark_border
drawable ic_chevron_right
drawable ic_file
drawable ic_folder
drawable ic_home
drawable ic_info
drawable ic_launcher_background
drawable ic_launcher_foreground
drawable ic_navigation
id animationContainer
id appBarLayout
id arrowIcon
id bottomNavigation
id cancelButton
id currentPathDisplay
id currentPathText
id favoriteButton
id fileBrowserLayout
id fileDate
id fileIcon
id fileListContainer
id fileName
id fileRecyclerView
id fileRecyclerViewNext
id grantPermissionButton
id loadingIndicator
id main
id nav_about
id nav_home
id navigateButton
id permissionRequestLayout
id swipeRefreshLayout
id targetPathEditText
id targetPathInputLayout
id toolbar
id topSection
id upButton
id viewPager
layout activity_main
layout dialog_path_navigation
layout fragment_about
layout fragment_home
layout item_file
layout layout_permission_request
menu bottom_navigation_menu
mipmap ic_launcher
mipmap ic_launcher_round
string app_name
style Base.Theme.ApkInjector
style Theme.ApkInjector
style Widget.Material3.AppBarLayout.NoElevation
style Widget.Material3.Toolbar.NoElevation
xml backup_rules
xml data_extraction_rules
