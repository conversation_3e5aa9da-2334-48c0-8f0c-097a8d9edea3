package com.iapp.leochen.apkinjector;

import android.content.Context;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.util.Log;

import androidx.core.content.ContextCompat;

/**
 * APK图标提取器
 * 用于从APK文件中提取应用图标
 */
public class ApkIconExtractor {
    
    private static final String TAG = "ApkIconExtractor";
    private static final int ICON_SIZE_DP = 48; // 统一图标大小 48dp
    
    /**
     * 从APK文件路径获取应用图标
     * @param context 上下文对象
     * @param apkPath APK文件路径
     * @return 统一大小的图标Drawable，获取失败返回默认图标
     */
    public static Drawable getApkIcon(Context context, String apkPath) {
        try {
            PackageManager packageManager = context.getPackageManager();
            PackageInfo packageInfo = getPackageInfo(packageManager, apkPath);
            
            if (packageInfo != null && packageInfo.applicationInfo != null) {
                // 获取APK图标
                Drawable icon = packageInfo.applicationInfo.loadIcon(packageManager);
                if (icon != null) {
                    // 统一图标大小
                    return resizeDrawable(context, icon, ICON_SIZE_DP);
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "获取APK图标失败: " + apkPath, e);
        }
        
        // 获取失败，返回默认APK图标
        return getDefaultApkIcon(context);
    }
    
    /**
     * 获取APK文件的PackageInfo
     * @param packageManager PackageManager对象
     * @param apkPath APK文件路径
     * @return PackageInfo对象，失败返回null
     */
    private static PackageInfo getPackageInfo(PackageManager packageManager, String apkPath) {
        try {
            PackageInfo packageInfo = packageManager.getPackageArchiveInfo(apkPath, 0);
            if (packageInfo != null) {
                // 设置源路径，这是加载图标的关键
                packageInfo.applicationInfo.sourceDir = apkPath;
                packageInfo.applicationInfo.publicSourceDir = apkPath;
            }
            return packageInfo;
        } catch (Exception e) {
            Log.e(TAG, "解析APK包信息失败: " + apkPath, e);
            return null;
        }
    }
    
    /**
     * 将Drawable调整为统一大小
     * @param context 上下文对象
     * @param drawable 原始Drawable
     * @param sizeDp 目标大小(dp)
     * @return 调整后的Drawable
     */
    private static Drawable resizeDrawable(Context context, Drawable drawable, int sizeDp) {
        try {
            // 将dp转换为px
            float density = context.getResources().getDisplayMetrics().density;
            int sizePx = (int) (sizeDp * density);
            
            // 将Drawable转换为Bitmap
            Bitmap bitmap = drawableToBitmap(drawable, sizePx, sizePx);
            
            // 将Bitmap转换回Drawable
            return new BitmapDrawable(context.getResources(), bitmap);
        } catch (Exception e) {
            Log.e(TAG, "调整图标大小失败", e);
            return drawable; // 调整失败，返回原图标
        }
    }
    
    /**
     * 将Drawable转换为指定大小的Bitmap
     * @param drawable 原始Drawable
     * @param width 目标宽度
     * @param height 目标高度
     * @return 转换后的Bitmap
     */
    private static Bitmap drawableToBitmap(Drawable drawable, int width, int height) {
        if (drawable instanceof BitmapDrawable) {
            BitmapDrawable bitmapDrawable = (BitmapDrawable) drawable;
            if (bitmapDrawable.getBitmap() != null) {
                return Bitmap.createScaledBitmap(bitmapDrawable.getBitmap(), width, height, true);
            }
        }
        
        // 创建新的Bitmap
        Bitmap bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(bitmap);
        drawable.setBounds(0, 0, canvas.getWidth(), canvas.getHeight());
        drawable.draw(canvas);
        
        return bitmap;
    }
    
    /**
     * 获取默认APK图标
     * @param context 上下文对象
     * @return 默认APK图标
     */
    private static Drawable getDefaultApkIcon(Context context) {
        try {
            // 使用我们自定义的APK图标
            Drawable defaultIcon = ContextCompat.getDrawable(context, R.drawable.ic_apk);
            if (defaultIcon != null) {
                return resizeDrawable(context, defaultIcon, ICON_SIZE_DP);
            }
        } catch (Exception e) {
            Log.e(TAG, "获取默认APK图标失败", e);
        }

        // 最后的备用方案，使用文件图标
        try {
            Drawable fallbackIcon = ContextCompat.getDrawable(context, R.drawable.ic_file);
            if (fallbackIcon != null) {
                return resizeDrawable(context, fallbackIcon, ICON_SIZE_DP);
            }
        } catch (Exception e) {
            Log.e(TAG, "获取备用图标失败", e);
        }

        // 最终备用方案
        return ContextCompat.getDrawable(context, android.R.drawable.ic_menu_info_details);
    }
    
    /**
     * 检查文件是否为APK文件
     * @param fileName 文件名
     * @return 是否为APK文件
     */
    public static boolean isApkFile(String fileName) {
        return fileName != null && fileName.toLowerCase().endsWith(".apk");
    }
    
    /**
     * 获取APK文件的包名
     * @param context 上下文对象
     * @param apkPath APK文件路径
     * @return 包名，获取失败返回null
     */
    public static String getApkPackageName(Context context, String apkPath) {
        try {
            PackageManager packageManager = context.getPackageManager();
            PackageInfo packageInfo = getPackageInfo(packageManager, apkPath);
            
            if (packageInfo != null) {
                return packageInfo.packageName;
            }
        } catch (Exception e) {
            Log.e(TAG, "获取APK包名失败: " + apkPath, e);
        }
        return null;
    }
    
    /**
     * 获取APK文件的应用名称
     * @param context 上下文对象
     * @param apkPath APK文件路径
     * @return 应用名称，获取失败返回文件名
     */
    public static String getApkAppName(Context context, String apkPath) {
        try {
            PackageManager packageManager = context.getPackageManager();
            PackageInfo packageInfo = getPackageInfo(packageManager, apkPath);
            
            if (packageInfo != null && packageInfo.applicationInfo != null) {
                CharSequence appName = packageInfo.applicationInfo.loadLabel(packageManager);
                if (appName != null) {
                    return appName.toString();
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "获取APK应用名失败: " + apkPath, e);
        }
        
        // 获取失败，返回文件名
        return new java.io.File(apkPath).getName();
    }
}
