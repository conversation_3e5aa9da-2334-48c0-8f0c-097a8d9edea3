#Wed Jul 23 18:38:38 HKT 2025
com.iapp.leochen.apkinjector.app-main-5\:/anim/slide_in_left.xml=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\slide_in_left.xml
com.iapp.leochen.apkinjector.app-main-5\:/anim/slide_in_left_smooth.xml=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\slide_in_left_smooth.xml
com.iapp.leochen.apkinjector.app-main-5\:/anim/slide_in_right.xml=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\slide_in_right.xml
com.iapp.leochen.apkinjector.app-main-5\:/anim/slide_in_right_smooth.xml=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\slide_in_right_smooth.xml
com.iapp.leochen.apkinjector.app-main-5\:/anim/slide_out_left.xml=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\slide_out_left.xml
com.iapp.leochen.apkinjector.app-main-5\:/anim/slide_out_left_smooth.xml=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\slide_out_left_smooth.xml
com.iapp.leochen.apkinjector.app-main-5\:/anim/slide_out_right.xml=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\slide_out_right.xml
com.iapp.leochen.apkinjector.app-main-5\:/anim/slide_out_right_smooth.xml=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\slide_out_right_smooth.xml
com.iapp.leochen.apkinjector.app-main-5\:/anim/slide_parallel_left.xml=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\slide_parallel_left.xml
com.iapp.leochen.apkinjector.app-main-5\:/anim/slide_parallel_right.xml=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\anim\\slide_parallel_right.xml
com.iapp.leochen.apkinjector.app-main-5\:/animator/no_elevation.xml=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\animator\\no_elevation.xml
com.iapp.leochen.apkinjector.app-main-5\:/drawable/gradient_separator.xml=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\gradient_separator.xml
com.iapp.leochen.apkinjector.app-main-5\:/drawable/ic_arrow_upward.xml=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_arrow_upward.xml
com.iapp.leochen.apkinjector.app-main-5\:/drawable/ic_bookmark_border.xml=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_bookmark_border.xml
com.iapp.leochen.apkinjector.app-main-5\:/drawable/ic_chevron_right.xml=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_chevron_right.xml
com.iapp.leochen.apkinjector.app-main-5\:/drawable/ic_file.xml=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_file.xml
com.iapp.leochen.apkinjector.app-main-5\:/drawable/ic_folder.xml=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_folder.xml
com.iapp.leochen.apkinjector.app-main-5\:/drawable/ic_home.xml=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_home.xml
com.iapp.leochen.apkinjector.app-main-5\:/drawable/ic_info.xml=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_info.xml
com.iapp.leochen.apkinjector.app-main-5\:/drawable/ic_launcher_background.xml=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_launcher_background.xml
com.iapp.leochen.apkinjector.app-main-5\:/drawable/ic_launcher_foreground.xml=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_launcher_foreground.xml
com.iapp.leochen.apkinjector.app-main-5\:/drawable/ic_navigation.xml=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_navigation.xml
com.iapp.leochen.apkinjector.app-main-5\:/drawable/path_display_background.xml=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\path_display_background.xml
com.iapp.leochen.apkinjector.app-main-5\:/layout/activity_main.xml=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_main.xml
com.iapp.leochen.apkinjector.app-main-5\:/layout/dialog_path_navigation.xml=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_path_navigation.xml
com.iapp.leochen.apkinjector.app-main-5\:/layout/fragment_about.xml=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_about.xml
com.iapp.leochen.apkinjector.app-main-5\:/layout/fragment_home.xml=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\fragment_home.xml
com.iapp.leochen.apkinjector.app-main-5\:/layout/item_file.xml=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_file.xml
com.iapp.leochen.apkinjector.app-main-5\:/layout/layout_permission_request.xml=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\layout_permission_request.xml
com.iapp.leochen.apkinjector.app-main-5\:/menu/bottom_navigation_menu.xml=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\menu\\bottom_navigation_menu.xml
com.iapp.leochen.apkinjector.app-main-5\:/mipmap-anydpi/ic_launcher.xml=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-anydpi-v4\\ic_launcher.xml
com.iapp.leochen.apkinjector.app-main-5\:/mipmap-anydpi/ic_launcher_round.xml=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-anydpi-v4\\ic_launcher_round.xml
com.iapp.leochen.apkinjector.app-main-5\:/mipmap-hdpi/ic_launcher.webp=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-hdpi-v4\\ic_launcher.webp
com.iapp.leochen.apkinjector.app-main-5\:/mipmap-hdpi/ic_launcher_round.webp=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-hdpi-v4\\ic_launcher_round.webp
com.iapp.leochen.apkinjector.app-main-5\:/mipmap-mdpi/ic_launcher.webp=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-mdpi-v4\\ic_launcher.webp
com.iapp.leochen.apkinjector.app-main-5\:/mipmap-mdpi/ic_launcher_round.webp=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-mdpi-v4\\ic_launcher_round.webp
com.iapp.leochen.apkinjector.app-main-5\:/mipmap-xhdpi/ic_launcher.webp=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xhdpi-v4\\ic_launcher.webp
com.iapp.leochen.apkinjector.app-main-5\:/mipmap-xhdpi/ic_launcher_round.webp=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xhdpi-v4\\ic_launcher_round.webp
com.iapp.leochen.apkinjector.app-main-5\:/mipmap-xxhdpi/ic_launcher.webp=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxhdpi-v4\\ic_launcher.webp
com.iapp.leochen.apkinjector.app-main-5\:/mipmap-xxhdpi/ic_launcher_round.webp=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxhdpi-v4\\ic_launcher_round.webp
com.iapp.leochen.apkinjector.app-main-5\:/mipmap-xxxhdpi/ic_launcher.webp=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxxhdpi-v4\\ic_launcher.webp
com.iapp.leochen.apkinjector.app-main-5\:/mipmap-xxxhdpi/ic_launcher_round.webp=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxxhdpi-v4\\ic_launcher_round.webp
com.iapp.leochen.apkinjector.app-main-5\:/xml/backup_rules.xml=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\xml\\backup_rules.xml
com.iapp.leochen.apkinjector.app-main-5\:/xml/data_extraction_rules.xml=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\xml\\data_extraction_rules.xml
