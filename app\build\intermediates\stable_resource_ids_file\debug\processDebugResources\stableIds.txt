com.iapp.leochen.apkinjector:xml/data_extraction_rules = 0x7f130001
com.iapp.leochen.apkinjector:xml/backup_rules = 0x7f130000
com.iapp.leochen.apkinjector:styleable/ViewTransition = 0x7f120099
com.iapp.leochen.apkinjector:styleable/ViewStubCompat = 0x7f120098
com.iapp.leochen.apkinjector:styleable/ViewBackgroundHelper = 0x7f120096
com.iapp.leochen.apkinjector:styleable/View = 0x7f120095
com.iapp.leochen.apkinjector:styleable/Transition = 0x7f120093
com.iapp.leochen.apkinjector:styleable/Toolbar = 0x7f120090
com.iapp.leochen.apkinjector:styleable/ThemeEnforcement = 0x7f12008f
com.iapp.leochen.apkinjector:styleable/TextInputEditText = 0x7f12008d
com.iapp.leochen.apkinjector:styleable/TabLayout = 0x7f12008a
com.iapp.leochen.apkinjector:styleable/TabItem = 0x7f120089
com.iapp.leochen.apkinjector:styleable/SwitchCompat = 0x7f120087
com.iapp.leochen.apkinjector:styleable/SwipeRefreshLayout = 0x7f120086
com.iapp.leochen.apkinjector:styleable/StateListDrawable = 0x7f120083
com.iapp.leochen.apkinjector:styleable/SnackbarLayout = 0x7f120080
com.iapp.leochen.apkinjector:styleable/Snackbar = 0x7f12007f
com.iapp.leochen.apkinjector:styleable/ShapeableImageView = 0x7f12007c
com.iapp.leochen.apkinjector:styleable/SearchView = 0x7f12007a
com.iapp.leochen.apkinjector:styleable/ScrimInsetsFrameLayout = 0x7f120077
com.iapp.leochen.apkinjector:styleable/RecyclerView = 0x7f120076
com.iapp.leochen.apkinjector:styleable/RecycleListView = 0x7f120075
com.iapp.leochen.apkinjector:styleable/RangeSlider = 0x7f120074
com.iapp.leochen.apkinjector:styleable/RadialViewGroup = 0x7f120073
com.iapp.leochen.apkinjector:styleable/OnSwipe = 0x7f12006f
com.iapp.leochen.apkinjector:styleable/NavigationBarActiveIndicator = 0x7f12006a
com.iapp.leochen.apkinjector:styleable/MotionTelltales = 0x7f120069
com.iapp.leochen.apkinjector:styleable/MotionHelper = 0x7f120065
com.iapp.leochen.apkinjector:styleable/MockView = 0x7f120062
com.iapp.leochen.apkinjector:styleable/MenuView = 0x7f120061
com.iapp.leochen.apkinjector:styleable/MenuItem = 0x7f120060
com.iapp.leochen.apkinjector:styleable/MaterialTextAppearance = 0x7f12005b
com.iapp.leochen.apkinjector:styleable/MaterialShape = 0x7f120059
com.iapp.leochen.apkinjector:styleable/MaterialCheckBoxStates = 0x7f120056
com.iapp.leochen.apkinjector:styleable/MaterialCardView = 0x7f120054
com.iapp.leochen.apkinjector:styleable/MaterialButton = 0x7f120050
com.iapp.leochen.apkinjector:styleable/MaterialAlertDialogTheme = 0x7f12004e
com.iapp.leochen.apkinjector:styleable/KeyFramesVelocity = 0x7f120044
com.iapp.leochen.apkinjector:styleable/KeyFramesAcceleration = 0x7f120043
com.iapp.leochen.apkinjector:styleable/KeyFrame = 0x7f120042
com.iapp.leochen.apkinjector:styleable/KeyAttribute = 0x7f120040
com.iapp.leochen.apkinjector:styleable/ImageFilterView = 0x7f12003e
com.iapp.leochen.apkinjector:styleable/GradientColorItem = 0x7f12003c
com.iapp.leochen.apkinjector:styleable/MenuGroup = 0x7f12005f
com.iapp.leochen.apkinjector:styleable/FragmentContainerView = 0x7f12003a
com.iapp.leochen.apkinjector:styleable/Fragment = 0x7f120039
com.iapp.leochen.apkinjector:styleable/FontFamily = 0x7f120036
com.iapp.leochen.apkinjector:styleable/FloatingActionButton_Behavior_Layout = 0x7f120034
com.iapp.leochen.apkinjector:styleable/FloatingActionButton = 0x7f120033
com.iapp.leochen.apkinjector:styleable/ExtendedFloatingActionButton_Behavior_Layout = 0x7f120032
com.iapp.leochen.apkinjector:styleable/ExtendedFloatingActionButton = 0x7f120031
com.iapp.leochen.apkinjector:styleable/DrawerLayout = 0x7f120030
com.iapp.leochen.apkinjector:styleable/CustomAttribute = 0x7f12002e
com.iapp.leochen.apkinjector:styleable/CoordinatorLayout_Layout = 0x7f12002d
com.iapp.leochen.apkinjector:styleable/ConstraintSet = 0x7f12002b
com.iapp.leochen.apkinjector:styleable/CompoundButton = 0x7f120025
com.iapp.leochen.apkinjector:styleable/ColorStateListItem = 0x7f120024
com.iapp.leochen.apkinjector:styleable/CollapsingToolbarLayout_Layout = 0x7f120023
com.iapp.leochen.apkinjector:styleable/CollapsingToolbarLayout = 0x7f120022
com.iapp.leochen.apkinjector:styleable/ClockHandView = 0x7f120021
com.iapp.leochen.apkinjector:styleable/ChipGroup = 0x7f12001e
com.iapp.leochen.apkinjector:styleable/Carousel = 0x7f12001b
com.iapp.leochen.apkinjector:styleable/BottomAppBar = 0x7f120015
com.iapp.leochen.apkinjector:styleable/BaseProgressIndicator = 0x7f120014
com.iapp.leochen.apkinjector:styleable/Badge = 0x7f120013
com.iapp.leochen.apkinjector:styleable/AppCompatSeekBar = 0x7f12000f
com.iapp.leochen.apkinjector:styleable/AnimatedStateListDrawableTransition = 0x7f120009
com.iapp.leochen.apkinjector:styleable/ActivityChooserView = 0x7f120005
com.iapp.leochen.apkinjector:styleable/ActionMode = 0x7f120004
com.iapp.leochen.apkinjector:styleable/ActionBar = 0x7f120000
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.Tooltip = 0x7f110468
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.Toolbar.PrimarySurface = 0x7f110466
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.Toolbar = 0x7f110464
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.TimePicker.ImageButton = 0x7f110462
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.TimePicker.Display.TextInputLayout = 0x7f110461
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.TimePicker.Display.TextInputEditText = 0x7f110460
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.TimePicker.Display.Divider = 0x7f11045e
com.iapp.leochen.apkinjector:styleable/CheckedTextView = 0x7f12001c
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.TimePicker.Display = 0x7f11045d
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.TimePicker = 0x7f11045a
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.TabLayout.PrimarySurface = 0x7f11044c
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.Snackbar.TextView = 0x7f110449
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.Snackbar.FullWidth = 0x7f110448
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.Snackbar = 0x7f110447
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.Slider = 0x7f110446
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.ShapeableImageView = 0x7f110445
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.PopupMenu = 0x7f110440
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.NavigationView = 0x7f11043f
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.NavigationRailView.PrimarySurface = 0x7f11043e
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.NavigationRailView.Compact = 0x7f11043d
com.iapp.leochen.apkinjector:styleable/TextAppearance = 0x7f12008b
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.NavigationRailView.Colored.Compact = 0x7f11043c
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.MaterialDivider = 0x7f110439
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.MaterialCalendar.MonthTextView = 0x7f110434
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.MaterialCalendar.Item = 0x7f110432
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.MaterialCalendar.HeaderToggleButton = 0x7f110431
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.MaterialCalendar.HeaderSelection.Fullscreen = 0x7f11042f
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.MaterialCalendar.HeaderSelection = 0x7f11042e
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.MaterialCalendar.HeaderLayout = 0x7f11042c
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.MaterialCalendar.HeaderCancelButton = 0x7f110429
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.MaterialCalendar.Fullscreen = 0x7f110428
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.MaterialCalendar.DayTextView = 0x7f110427
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.MaterialCalendar.DayOfWeekLabel = 0x7f110426
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.MaterialCalendar = 0x7f110421
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.ExtendedFloatingActionButton.Icon = 0x7f11041c
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.CompoundButton.Switch = 0x7f11041a
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.CompoundButton.RadioButton = 0x7f110419
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.ChipGroup = 0x7f110412
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.Chip.Entry = 0x7f110410
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.CardView = 0x7f11040c
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.Button.TextButton.Snackbar = 0x7f110409
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.Button.TextButton.Dialog.Icon = 0x7f110407
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.Button.TextButton.Dialog.Flush = 0x7f110406
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.Button.TextButton.Dialog = 0x7f110405
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.Button.OutlinedButton.Icon = 0x7f110403
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.Button = 0x7f110400
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.NavigationRailView.Colored = 0x7f11043b
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.BottomSheet = 0x7f1103fe
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.BottomAppBar.Colored = 0x7f1103f9
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.Badge = 0x7f1103f7
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense = 0x7f1103f6
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox = 0x7f1103f5
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.AutoCompleteTextView.FilledBox.Dense = 0x7f1103f4
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.AppBarLayout.Surface = 0x7f1103f2
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.ActionMode = 0x7f1103ef
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.ActionBar.Solid = 0x7f1103ed
com.iapp.leochen.apkinjector:style/Widget.Material3.Tooltip = 0x7f1103ea
com.iapp.leochen.apkinjector:style/Widget.Material3.Toolbar = 0x7f1103e6
com.iapp.leochen.apkinjector:style/Widget.Material3.TextInputLayout.OutlinedBox.ExposedDropdownMenu = 0x7f1103e5
com.iapp.leochen.apkinjector:styleable/CircularProgressIndicator = 0x7f12001f
com.iapp.leochen.apkinjector:style/Widget.Material3.TextInputLayout.OutlinedBox.Dense.ExposedDropdownMenu = 0x7f1103e4
com.iapp.leochen.apkinjector:style/Widget.Material3.TextInputLayout.OutlinedBox = 0x7f1103e2
com.iapp.leochen.apkinjector:style/Widget.Material3.TextInputLayout.FilledBox.ExposedDropdownMenu = 0x7f1103e1
com.iapp.leochen.apkinjector:styleable/AppBarLayoutStates = 0x7f12000b
com.iapp.leochen.apkinjector:style/Widget.Material3.TextInputLayout.FilledBox.Dense = 0x7f1103df
com.iapp.leochen.apkinjector:style/Widget.Material3.TextInputEditText.OutlinedBox.Dense = 0x7f1103dd
com.iapp.leochen.apkinjector:style/Widget.Material3.Snackbar.FullWidth = 0x7f1103d5
com.iapp.leochen.apkinjector:style/Widget.Material3.Snackbar = 0x7f1103d4
com.iapp.leochen.apkinjector:style/Widget.Material3.Slider.Legacy.Label = 0x7f1103d3
com.iapp.leochen.apkinjector:style/Widget.Material3.Slider.Legacy = 0x7f1103d2
com.iapp.leochen.apkinjector:style/Widget.Material3.SearchView.Toolbar = 0x7f1103cb
com.iapp.leochen.apkinjector:style/Widget.Material3.SearchView = 0x7f1103c9
com.iapp.leochen.apkinjector:style/Widget.Material3.Search.ActionButton.Overflow = 0x7f1103c5
com.iapp.leochen.apkinjector:style/Widget.Material3.PopupMenu.Overflow = 0x7f1103c4
com.iapp.leochen.apkinjector:style/Widget.Material3.PopupMenu.ListPopupWindow = 0x7f1103c3
com.iapp.leochen.apkinjector:style/Widget.Material3.PopupMenu = 0x7f1103c1
com.iapp.leochen.apkinjector:style/Widget.Material3.NavigationView = 0x7f1103c0
com.iapp.leochen.apkinjector:style/Widget.Material3.MaterialTimePicker.Display.HelperText = 0x7f1103b9
com.iapp.leochen.apkinjector:style/Widget.Material3.MaterialTimePicker.Display.Divider = 0x7f1103b8
com.iapp.leochen.apkinjector:style/Widget.Material3.MaterialTimePicker.Display = 0x7f1103b7
com.iapp.leochen.apkinjector:style/Widget.Material3.MaterialTimePicker = 0x7f1103b4
com.iapp.leochen.apkinjector:style/Widget.Material3.MaterialCalendar.YearNavigationButton = 0x7f1103b1
com.iapp.leochen.apkinjector:style/Widget.Material3.MaterialCalendar.Year.Selected = 0x7f1103af
com.iapp.leochen.apkinjector:style/Widget.Material3.MaterialCalendar.HeaderToggleButton = 0x7f1103aa
com.iapp.leochen.apkinjector:style/Widget.Material3.MaterialCalendar.HeaderSelection.Fullscreen = 0x7f1103a8
com.iapp.leochen.apkinjector:style/Widget.Material3.MaterialCalendar.HeaderSelection = 0x7f1103a7
com.iapp.leochen.apkinjector:style/Widget.Material3.MaterialCalendar.Fullscreen = 0x7f1103a2
com.iapp.leochen.apkinjector:style/Widget.Material3.MaterialCalendar.Day.Invalid = 0x7f11039d
com.iapp.leochen.apkinjector:styleable/AnimatedStateListDrawableItem = 0x7f120008
com.iapp.leochen.apkinjector:style/Widget.Material3.MaterialCalendar = 0x7f11039b
com.iapp.leochen.apkinjector:style/Widget.Material3.MaterialButtonToggleGroup = 0x7f11039a
com.iapp.leochen.apkinjector:style/Widget.Material3.LinearProgressIndicator.Legacy = 0x7f110399
com.iapp.leochen.apkinjector:style/Widget.Material3.LinearProgressIndicator = 0x7f110398
com.iapp.leochen.apkinjector:style/Widget.Material3.Light.ActionBar.Solid = 0x7f110397
com.iapp.leochen.apkinjector:style/Widget.Material3.FloatingActionButton.Tertiary = 0x7f110396
com.iapp.leochen.apkinjector:style/Widget.Material3.FloatingActionButton.Small.Tertiary = 0x7f110394
com.iapp.leochen.apkinjector:style/Widget.Material3.FloatingActionButton.Small.Surface = 0x7f110393
com.iapp.leochen.apkinjector:style/Widget.Material3.FloatingActionButton.Secondary = 0x7f110390
com.iapp.leochen.apkinjector:style/Widget.Material3.FloatingActionButton.Primary = 0x7f11038f
com.iapp.leochen.apkinjector:style/Widget.Material3.FloatingActionButton.Large.Tertiary = 0x7f11038e
com.iapp.leochen.apkinjector:style/Widget.Material3.FloatingActionButton.Large.Secondary = 0x7f11038c
com.iapp.leochen.apkinjector:style/Widget.Material3.ExtendedFloatingActionButton.Secondary = 0x7f110388
com.iapp.leochen.apkinjector:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Primary = 0x7f110383
com.iapp.leochen.apkinjector:style/Widget.Material3.CompoundButton.CheckBox = 0x7f11037e
com.iapp.leochen.apkinjector:style/Widget.Material3.CollapsingToolbar.Medium = 0x7f11037d
com.iapp.leochen.apkinjector:style/Widget.Material3.CollapsingToolbar = 0x7f11037b
com.iapp.leochen.apkinjector:style/Widget.Material3.CircularProgressIndicator.Medium = 0x7f110379
com.iapp.leochen.apkinjector:style/Widget.Material3.CircularProgressIndicator.Legacy.Medium = 0x7f110377
com.iapp.leochen.apkinjector:style/Widget.Material3.CircularProgressIndicator.Legacy.ExtraSmall = 0x7f110376
com.iapp.leochen.apkinjector:style/Widget.Material3.CircularProgressIndicator.Legacy = 0x7f110375
com.iapp.leochen.apkinjector:style/Widget.Material3.CircularProgressIndicator = 0x7f110373
com.iapp.leochen.apkinjector:style/Widget.Material3.Chip.Suggestion = 0x7f110370
com.iapp.leochen.apkinjector:style/Widget.Material3.Chip.Input.Elevated = 0x7f11036d
com.iapp.leochen.apkinjector:style/Widget.Material3.CardView.Filled = 0x7f110365
com.iapp.leochen.apkinjector:style/Widget.Material3.CardView.Elevated = 0x7f110364
com.iapp.leochen.apkinjector:style/Widget.Material3.Button.UnelevatedButton = 0x7f110363
com.iapp.leochen.apkinjector:style/Widget.Material3.Button.IconButton.Outlined = 0x7f110358
com.iapp.leochen.apkinjector:style/Widget.Material3.Button.TextButton.Snackbar = 0x7f110360
com.iapp.leochen.apkinjector:style/Widget.Material3.Button.IconButton.Filled.Tonal = 0x7f110357
com.iapp.leochen.apkinjector:style/Widget.Material3.Button.IconButton = 0x7f110355
com.iapp.leochen.apkinjector:style/Widget.Material3.Button = 0x7f110351
com.iapp.leochen.apkinjector:style/Widget.Material3.BottomSheet.Modal = 0x7f110350
com.iapp.leochen.apkinjector:style/Widget.Material3.BottomSheet.DragHandle = 0x7f11034f
com.iapp.leochen.apkinjector:style/Widget.Material3.BottomSheet = 0x7f11034e
com.iapp.leochen.apkinjector:style/Widget.Material3.BottomAppBar.Button.Navigation = 0x7f110349
com.iapp.leochen.apkinjector:style/Widget.Material3.BottomAppBar = 0x7f110348
com.iapp.leochen.apkinjector:style/Widget.Material3.AutoCompleteTextView.OutlinedBox = 0x7f110344
com.iapp.leochen.apkinjector:style/Widget.Material3.AppBarLayout.NoElevation = 0x7f110341
com.iapp.leochen.apkinjector:style/Widget.Material3.ActionMode = 0x7f11033f
com.iapp.leochen.apkinjector:style/Widget.Design.TextInputEditText = 0x7f11033c
com.iapp.leochen.apkinjector:style/Widget.Design.CollapsingToolbar = 0x7f110336
com.iapp.leochen.apkinjector:style/Widget.Design.BottomSheet.Modal = 0x7f110335
com.iapp.leochen.apkinjector:style/Widget.Design.AppBarLayout = 0x7f110333
com.iapp.leochen.apkinjector:style/Widget.Compat.NotificationActionText = 0x7f110332
com.iapp.leochen.apkinjector:style/Widget.Compat.NotificationActionContainer = 0x7f110331
com.iapp.leochen.apkinjector:style/Widget.AppCompat.Toolbar = 0x7f11032f
com.iapp.leochen.apkinjector:style/Widget.AppCompat.TextView.SpinnerItem = 0x7f11032e
com.iapp.leochen.apkinjector:style/Widget.AppCompat.Spinner.Underlined = 0x7f11032c
com.iapp.leochen.apkinjector:style/Widget.AppCompat.Spinner.DropDown.ActionBar = 0x7f11032b
com.iapp.leochen.apkinjector:style/Widget.AppCompat.Spinner.DropDown = 0x7f11032a
com.iapp.leochen.apkinjector:style/Widget.AppCompat.SeekBar.Discrete = 0x7f110328
com.iapp.leochen.apkinjector:style/Widget.AppCompat.SearchView.ActionBar = 0x7f110326
com.iapp.leochen.apkinjector:style/Widget.AppCompat.SearchView = 0x7f110325
com.iapp.leochen.apkinjector:style/Widget.AppCompat.RatingBar.Small = 0x7f110324
com.iapp.leochen.apkinjector:style/Widget.AppCompat.RatingBar.Indicator = 0x7f110323
com.iapp.leochen.apkinjector:style/Widget.AppCompat.ProgressBar.Horizontal = 0x7f110321
com.iapp.leochen.apkinjector:style/Widget.AppCompat.PopupWindow = 0x7f11031f
com.iapp.leochen.apkinjector:style/Widget.AppCompat.PopupMenu = 0x7f11031d
com.iapp.leochen.apkinjector:style/Widget.Material3.ExtendedFloatingActionButton.Primary = 0x7f110387
com.iapp.leochen.apkinjector:style/Widget.AppCompat.ListView.DropDown = 0x7f11031b
com.iapp.leochen.apkinjector:style/Widget.AppCompat.Light.Spinner.DropDown.ActionBar = 0x7f110317
com.iapp.leochen.apkinjector:style/Widget.AppCompat.Light.SearchView = 0x7f110316
com.iapp.leochen.apkinjector:style/Widget.AppCompat.Light.PopupMenu.Overflow = 0x7f110315
com.iapp.leochen.apkinjector:style/Widget.Material3.Chip.Input.Icon.Elevated = 0x7f11036f
com.iapp.leochen.apkinjector:style/Widget.AppCompat.Light.PopupMenu = 0x7f110314
com.iapp.leochen.apkinjector:style/Widget.AppCompat.Light.ListView.DropDown = 0x7f110313
com.iapp.leochen.apkinjector:style/Widget.AppCompat.Light.DropDownItem.Spinner = 0x7f110311
com.iapp.leochen.apkinjector:style/Widget.AppCompat.Light.ActionMode.Inverse = 0x7f11030e
com.iapp.leochen.apkinjector:style/Widget.AppCompat.Light.ActionButton.CloseMode = 0x7f11030c
com.iapp.leochen.apkinjector:style/Widget.AppCompat.Light.ActionButton = 0x7f11030b
com.iapp.leochen.apkinjector:style/Widget.AppCompat.Light.ActionBar.TabView = 0x7f110309
com.iapp.leochen.apkinjector:style/Widget.AppCompat.Light.ActionBar.TabBar = 0x7f110305
com.iapp.leochen.apkinjector:style/Widget.AppCompat.Light.ActionBar.Solid = 0x7f110303
com.iapp.leochen.apkinjector:style/Widget.AppCompat.DropDownItem.Spinner = 0x7f1102ff
com.iapp.leochen.apkinjector:style/Widget.AppCompat.CompoundButton.CheckBox = 0x7f1102fb
com.iapp.leochen.apkinjector:style/Widget.Material3.ActionBar.Solid = 0x7f11033e
com.iapp.leochen.apkinjector:style/Widget.AppCompat.Button.ButtonBar.AlertDialog = 0x7f1102f6
com.iapp.leochen.apkinjector:style/Widget.AppCompat.Button.Borderless.Colored = 0x7f1102f5
com.iapp.leochen.apkinjector:style/Widget.AppCompat.Button.Borderless = 0x7f1102f4
com.iapp.leochen.apkinjector:style/Widget.AppCompat.AutoCompleteTextView = 0x7f1102f2
com.iapp.leochen.apkinjector:style/Widget.AppCompat.ActionButton.CloseMode = 0x7f1102ee
com.iapp.leochen.apkinjector:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Tertiary = 0x7f110386
com.iapp.leochen.apkinjector:style/Widget.AppCompat.ActionButton = 0x7f1102ed
com.iapp.leochen.apkinjector:style/Widget.AppCompat.ActionBar.TabText = 0x7f1102eb
com.iapp.leochen.apkinjector:style/ThemeOverlay.MaterialComponents.TimePicker.Display.TextInputEditText = 0x7f1102e4
com.iapp.leochen.apkinjector:style/ThemeOverlay.MaterialComponents.TimePicker.Display = 0x7f1102e3
com.iapp.leochen.apkinjector:style/ThemeOverlay.MaterialComponents.TimePicker = 0x7f1102e2
com.iapp.leochen.apkinjector:style/ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox = 0x7f1102e0
com.iapp.leochen.apkinjector:style/ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox = 0x7f1102de
com.iapp.leochen.apkinjector:style/ThemeOverlay.MaterialComponents.MaterialCalendar.Fullscreen = 0x7f1102dc
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.AppBarLayout.PrimarySurface = 0x7f1103f1
com.iapp.leochen.apkinjector:style/ThemeOverlay.MaterialComponents.MaterialCalendar = 0x7f1102db
com.iapp.leochen.apkinjector:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Calendar = 0x7f1102d7
com.iapp.leochen.apkinjector:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Centered = 0x7f1102d5
com.iapp.leochen.apkinjector:style/ThemeOverlay.MaterialComponents.Light.Dialog.Alert.Framework = 0x7f1102d3
com.iapp.leochen.apkinjector:style/ThemeOverlay.MaterialComponents.Dialog.Alert.Framework = 0x7f1102d1
com.iapp.leochen.apkinjector:style/ThemeOverlay.MaterialComponents.Dialog.Alert = 0x7f1102d0
com.iapp.leochen.apkinjector:style/ThemeOverlay.MaterialComponents.BottomAppBar.Surface = 0x7f1102ca
com.iapp.leochen.apkinjector:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox = 0x7f1102c7
com.iapp.leochen.apkinjector:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox = 0x7f1102c5
com.iapp.leochen.apkinjector:style/Widget.Material3.Toolbar.NoElevation = 0x7f1103e7
com.iapp.leochen.apkinjector:style/ThemeOverlay.MaterialComponents.ActionBar.Surface = 0x7f1102c3
com.iapp.leochen.apkinjector:style/ThemeOverlay.MaterialComponents.ActionBar.Primary = 0x7f1102c2
com.iapp.leochen.apkinjector:style/ThemeOverlay.MaterialComponents = 0x7f1102c0
com.iapp.leochen.apkinjector:style/ThemeOverlay.Material3.Toolbar.Surface = 0x7f1102be
com.iapp.leochen.apkinjector:style/ThemeOverlay.Material3.TextInputEditText.OutlinedBox.Dense = 0x7f1102bd
com.iapp.leochen.apkinjector:style/ThemeOverlay.Material3.TextInputEditText.OutlinedBox = 0x7f1102bc
com.iapp.leochen.apkinjector:style/ThemeOverlay.Material3.TextInputEditText.FilledBox.Dense = 0x7f1102bb
com.iapp.leochen.apkinjector:style/ThemeOverlay.Material3.MaterialTimePicker = 0x7f1102b0
com.iapp.leochen.apkinjector:style/ThemeOverlay.Material3.MaterialCalendar.Fullscreen = 0x7f1102ae
com.iapp.leochen.apkinjector:style/ThemeOverlay.Material3.MaterialCalendar = 0x7f1102ad
com.iapp.leochen.apkinjector:style/ThemeOverlay.Material3.MaterialAlertDialog.Centered = 0x7f1102ac
com.iapp.leochen.apkinjector:style/ThemeOverlay.Material3.MaterialAlertDialog = 0x7f1102ab
com.iapp.leochen.apkinjector:styleable/KeyTimeCycle = 0x7f120046
com.iapp.leochen.apkinjector:style/ThemeOverlay.Material3.FloatingActionButton.Tertiary = 0x7f1102a6
com.iapp.leochen.apkinjector:style/ThemeOverlay.Material3.FloatingActionButton.Surface = 0x7f1102a5
com.iapp.leochen.apkinjector:style/ThemeOverlay.Material3.FloatingActionButton.Primary = 0x7f1102a3
com.iapp.leochen.apkinjector:style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Tertiary = 0x7f1102a2
com.iapp.leochen.apkinjector:style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Surface = 0x7f1102a1
com.iapp.leochen.apkinjector:style/ThemeOverlay.Material3.DynamicColors.DayNight = 0x7f11029d
com.iapp.leochen.apkinjector:style/ThemeOverlay.Material3.Dialog.Alert = 0x7f11029a
com.iapp.leochen.apkinjector:style/ThemeOverlay.Material3.Dialog = 0x7f110299
com.iapp.leochen.apkinjector:style/Widget.Material3.AppBarLayout = 0x7f110340
com.iapp.leochen.apkinjector:style/ThemeOverlay.Material3.DayNight.SideSheetDialog = 0x7f110298
com.iapp.leochen.apkinjector:style/ThemeOverlay.Material3.Dark.ActionBar = 0x7f110296
com.iapp.leochen.apkinjector:style/ThemeOverlay.Material3.Dark = 0x7f110295
com.iapp.leochen.apkinjector:style/ThemeOverlay.Material3.Chip = 0x7f110293
com.iapp.leochen.apkinjector:style/ThemeOverlay.Material3.Button.TextButton.Snackbar = 0x7f110291
com.iapp.leochen.apkinjector:style/ThemeOverlay.Material3.Button.TextButton = 0x7f110290
com.iapp.leochen.apkinjector:style/ThemeOverlay.Material3.Button.IconButton.Filled = 0x7f11028e
com.iapp.leochen.apkinjector:style/ThemeOverlay.Material3.Button.IconButton = 0x7f11028d
com.iapp.leochen.apkinjector:style/ThemeOverlay.Material3.Button.ElevatedButton = 0x7f11028c
com.iapp.leochen.apkinjector:style/ThemeOverlay.Material3.BottomSheetDialog = 0x7f11028a
com.iapp.leochen.apkinjector:style/ThemeOverlay.Material3.BottomNavigationView = 0x7f110289
com.iapp.leochen.apkinjector:styleable/LinearLayoutCompat_Layout = 0x7f12004a
com.iapp.leochen.apkinjector:style/ThemeOverlay.Material3.AutoCompleteTextView.OutlinedBox = 0x7f110285
com.iapp.leochen.apkinjector:style/ThemeOverlay.Material3.AutoCompleteTextView.FilledBox.Dense = 0x7f110284
com.iapp.leochen.apkinjector:style/ThemeOverlay.Material3.AutoCompleteTextView.FilledBox = 0x7f110283
com.iapp.leochen.apkinjector:style/ThemeOverlay.AppCompat.Light = 0x7f11027e
com.iapp.leochen.apkinjector:style/ThemeOverlay.AppCompat.Dark.ActionBar = 0x7f110279
com.iapp.leochen.apkinjector:style/ThemeOverlay.AppCompat = 0x7f110276
com.iapp.leochen.apkinjector:style/Theme.MaterialComponents.NoActionBar.Bridge = 0x7f110275
com.iapp.leochen.apkinjector:style/Theme.MaterialComponents.NoActionBar = 0x7f110274
com.iapp.leochen.apkinjector:style/Theme.MaterialComponents.Light.Dialog.MinWidth = 0x7f11026f
com.iapp.leochen.apkinjector:style/Theme.MaterialComponents.Light.Dialog.FixedSize.Bridge = 0x7f11026e
com.iapp.leochen.apkinjector:style/Theme.MaterialComponents.Light.Dialog.Bridge = 0x7f11026c
com.iapp.leochen.apkinjector:style/Theme.MaterialComponents.Light.Dialog.Alert.Bridge = 0x7f11026b
com.iapp.leochen.apkinjector:style/Theme.MaterialComponents.Light.DarkActionBar.Bridge = 0x7f110268
com.iapp.leochen.apkinjector:style/Theme.MaterialComponents.Light.BottomSheetDialog = 0x7f110265
com.iapp.leochen.apkinjector:style/Theme.MaterialComponents.Dialog.MinWidth = 0x7f110261
com.iapp.leochen.apkinjector:style/Theme.MaterialComponents.Dialog.FixedSize.Bridge = 0x7f110260
com.iapp.leochen.apkinjector:style/Theme.MaterialComponents.Dialog.Alert = 0x7f11025c
com.iapp.leochen.apkinjector:style/Theme.MaterialComponents.DayNight.NoActionBar.Bridge = 0x7f11025a
com.iapp.leochen.apkinjector:style/Theme.MaterialComponents.DayNight.NoActionBar = 0x7f110259
com.iapp.leochen.apkinjector:style/Widget.Material3.Button.Icon = 0x7f110354
com.iapp.leochen.apkinjector:style/Theme.MaterialComponents.DayNight.Dialog.MinWidth.Bridge = 0x7f110257
com.iapp.leochen.apkinjector:style/Theme.MaterialComponents.DayNight.Dialog.MinWidth = 0x7f110256
com.iapp.leochen.apkinjector:style/ThemeOverlay.Material3.MaterialCalendar.HeaderCancelButton = 0x7f1102af
com.iapp.leochen.apkinjector:style/Theme.MaterialComponents.DayNight.Dialog.FixedSize.Bridge = 0x7f110255
com.iapp.leochen.apkinjector:style/Theme.MaterialComponents.DayNight.Dialog.Bridge = 0x7f110253
com.iapp.leochen.apkinjector:style/Theme.MaterialComponents.DayNight.Dialog.Alert = 0x7f110251
com.iapp.leochen.apkinjector:style/Theme.MaterialComponents.DayNight.Dialog = 0x7f110250
com.iapp.leochen.apkinjector:style/Theme.MaterialComponents.Bridge = 0x7f110249
com.iapp.leochen.apkinjector:style/Theme.MaterialComponents.BottomSheetDialog = 0x7f110248
com.iapp.leochen.apkinjector:style/Theme.MaterialComponents = 0x7f110247
com.iapp.leochen.apkinjector:style/Theme.Material3.Light.SideSheetDialog = 0x7f110246
com.iapp.leochen.apkinjector:style/Theme.Material3.Light.DialogWhenLarge = 0x7f110244
com.iapp.leochen.apkinjector:style/Theme.Material3.Light.Dialog.Alert = 0x7f110242
com.iapp.leochen.apkinjector:style/Theme.Material3.Light.BottomSheetDialog = 0x7f110240
com.iapp.leochen.apkinjector:style/Theme.Material3.DynamicColors.Light = 0x7f11023d
com.iapp.leochen.apkinjector:style/Theme.Material3.DynamicColors.DayNight = 0x7f11023b
com.iapp.leochen.apkinjector:style/Theme.Material3.DynamicColors.Dark = 0x7f110239
com.iapp.leochen.apkinjector:style/Theme.Material3.DayNight.NoActionBar = 0x7f110237
com.iapp.leochen.apkinjector:style/Theme.Material3.DayNight.DialogWhenLarge = 0x7f110236
com.iapp.leochen.apkinjector:style/Theme.Material3.DayNight.Dialog.Alert = 0x7f110234
com.iapp.leochen.apkinjector:style/Theme.Material3.DayNight.BottomSheetDialog = 0x7f110232
com.iapp.leochen.apkinjector:style/Theme.Material3.DayNight = 0x7f110231
com.iapp.leochen.apkinjector:style/ThemeOverlay.AppCompat.DayNight = 0x7f11027a
com.iapp.leochen.apkinjector:style/Theme.Material3.Dark.SideSheetDialog = 0x7f110230
com.iapp.leochen.apkinjector:style/Theme.Material3.Dark.Dialog.Alert = 0x7f11022c
com.iapp.leochen.apkinjector:style/Theme.Material3.Dark.BottomSheetDialog = 0x7f11022a
com.iapp.leochen.apkinjector:styleable/MaterialTimePicker = 0x7f12005d
com.iapp.leochen.apkinjector:style/Theme.Design = 0x7f110223
com.iapp.leochen.apkinjector:style/Theme.AppCompat.Dialog.MinWidth = 0x7f110218
com.iapp.leochen.apkinjector:style/Theme.AppCompat.DayNight.NoActionBar = 0x7f110215
com.iapp.leochen.apkinjector:style/Theme.AppCompat.DayNight.DialogWhenLarge = 0x7f110214
com.iapp.leochen.apkinjector:style/Theme.AppCompat.DayNight.Dialog.MinWidth = 0x7f110213
com.iapp.leochen.apkinjector:style/Theme.AppCompat.DayNight.Dialog = 0x7f110211
com.iapp.leochen.apkinjector:style/Theme.AppCompat.DayNight.DarkActionBar = 0x7f110210
com.iapp.leochen.apkinjector:style/Theme.AppCompat.DayNight = 0x7f11020f
com.iapp.leochen.apkinjector:style/Theme.AppCompat.CompactMenu = 0x7f11020e
com.iapp.leochen.apkinjector:style/Theme.AppCompat = 0x7f11020d
com.iapp.leochen.apkinjector:style/ThemeOverlay.Material3.Light = 0x7f1102a9
com.iapp.leochen.apkinjector:style/TextAppearance.Widget.AppCompat.ExpandedMenu.Item = 0x7f110209
com.iapp.leochen.apkinjector:style/TextAppearance.MaterialComponents.Tooltip = 0x7f110208
com.iapp.leochen.apkinjector:style/TextAppearance.MaterialComponents.Subtitle1 = 0x7f110205
com.iapp.leochen.apkinjector:style/TextAppearance.MaterialComponents.Headline6 = 0x7f110203
com.iapp.leochen.apkinjector:style/TextAppearance.MaterialComponents.Headline4 = 0x7f110201
com.iapp.leochen.apkinjector:style/TextAppearance.MaterialComponents.Headline2 = 0x7f1101ff
com.iapp.leochen.apkinjector:style/TextAppearance.MaterialComponents.Body2 = 0x7f1101fa
com.iapp.leochen.apkinjector:style/TextAppearance.MaterialComponents.Badge = 0x7f1101f8
com.iapp.leochen.apkinjector:style/TextAppearance.Material3.TitleSmall = 0x7f1101f7
com.iapp.leochen.apkinjector:style/TextAppearance.Material3.TitleLarge = 0x7f1101f5
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.FloatingActionButton = 0x7f11041d
com.iapp.leochen.apkinjector:style/TextAppearance.Material3.SearchBar = 0x7f1101f2
com.iapp.leochen.apkinjector:style/TextAppearance.Material3.MaterialTimePicker.Title = 0x7f1101f1
com.iapp.leochen.apkinjector:style/TextAppearance.Material3.LabelSmall = 0x7f1101f0
com.iapp.leochen.apkinjector:style/TextAppearance.Material3.LabelLarge = 0x7f1101ee
com.iapp.leochen.apkinjector:style/TextAppearance.Material3.HeadlineMedium = 0x7f1101ec
com.iapp.leochen.apkinjector:style/TextAppearance.Material3.DisplayMedium = 0x7f1101e9
com.iapp.leochen.apkinjector:style/TextAppearance.Material3.BodySmall = 0x7f1101e7
com.iapp.leochen.apkinjector:style/TextAppearance.Material3.ActionBar.Title = 0x7f1101e4
com.iapp.leochen.apkinjector:style/TextAppearance.Material3.ActionBar.Subtitle = 0x7f1101e3
com.iapp.leochen.apkinjector:style/TextAppearance.M3.Sys.Typescale.TitleSmall = 0x7f1101e2
com.iapp.leochen.apkinjector:style/TextAppearance.M3.Sys.Typescale.TitleMedium = 0x7f1101e1
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.CheckedTextView = 0x7f11040d
com.iapp.leochen.apkinjector:style/TextAppearance.M3.Sys.Typescale.TitleLarge = 0x7f1101e0
com.iapp.leochen.apkinjector:style/TextAppearance.M3.Sys.Typescale.HeadlineMedium = 0x7f1101db
com.iapp.leochen.apkinjector:style/TextAppearance.M3.Sys.Typescale.DisplaySmall = 0x7f1101d9
com.iapp.leochen.apkinjector:style/TextAppearance.M3.Sys.Typescale.DisplayMedium = 0x7f1101d8
com.iapp.leochen.apkinjector:style/TextAppearance.M3.Sys.Typescale.DisplayLarge = 0x7f1101d7
com.iapp.leochen.apkinjector:styleable/PopupWindowBackgroundState = 0x7f120071
com.iapp.leochen.apkinjector:style/TextAppearance.M3.Sys.Typescale.BodySmall = 0x7f1101d6
com.iapp.leochen.apkinjector:style/TextAppearance.M3.Sys.Typescale.BodyLarge = 0x7f1101d4
com.iapp.leochen.apkinjector:style/TextAppearance.Design.Suffix = 0x7f1101d2
com.iapp.leochen.apkinjector:style/TextAppearance.Design.Prefix = 0x7f1101d0
com.iapp.leochen.apkinjector:style/TextAppearance.Design.Error = 0x7f1101cc
com.iapp.leochen.apkinjector:style/TextAppearance.Design.Counter = 0x7f1101ca
com.iapp.leochen.apkinjector:style/TextAppearance.Compat.Notification.Title = 0x7f1101c8
com.iapp.leochen.apkinjector:style/TextAppearance.Compat.Notification.Time = 0x7f1101c7
com.iapp.leochen.apkinjector:style/TextAppearance.Compat.Notification.Line2 = 0x7f1101c6
com.iapp.leochen.apkinjector:style/TextAppearance.Compat.Notification.Info = 0x7f1101c5
com.iapp.leochen.apkinjector:style/TextAppearance.Compat.Notification = 0x7f1101c4
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.PopupMenu.Overflow = 0x7f110443
com.iapp.leochen.apkinjector:style/TextAppearance.AppCompat.Widget.TextView.SpinnerItem = 0x7f1101c3
com.iapp.leochen.apkinjector:style/TextAppearance.AppCompat.Widget.Switch = 0x7f1101c2
com.iapp.leochen.apkinjector:style/TextAppearance.AppCompat.Widget.PopupMenu.Large = 0x7f1101c0
com.iapp.leochen.apkinjector:styleable/Slider = 0x7f12007e
com.iapp.leochen.apkinjector:style/TextAppearance.AppCompat.Widget.PopupMenu.Header = 0x7f1101bf
com.iapp.leochen.apkinjector:style/TextAppearance.AppCompat.Widget.DropDownItem = 0x7f1101be
com.iapp.leochen.apkinjector:style/TextAppearance.AppCompat.Widget.ActionMode.Title = 0x7f1101b8
com.iapp.leochen.apkinjector:style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle.Inverse = 0x7f1101b7
com.iapp.leochen.apkinjector:style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle = 0x7f1101b6
com.iapp.leochen.apkinjector:style/TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse = 0x7f1101b5
com.iapp.leochen.apkinjector:style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse = 0x7f1101b3
com.iapp.leochen.apkinjector:style/TextAppearance.AppCompat.Tooltip = 0x7f1101b0
com.iapp.leochen.apkinjector:style/TextAppearance.AppCompat.Title.Inverse = 0x7f1101af
com.iapp.leochen.apkinjector:style/TextAppearance.AppCompat.Subhead.Inverse = 0x7f1101ad
com.iapp.leochen.apkinjector:style/TextAppearance.Material3.HeadlineSmall = 0x7f1101ed
com.iapp.leochen.apkinjector:style/TextAppearance.AppCompat.Subhead = 0x7f1101ac
com.iapp.leochen.apkinjector:style/TextAppearance.AppCompat.Small.Inverse = 0x7f1101ab
com.iapp.leochen.apkinjector:style/TextAppearance.AppCompat.Small = 0x7f1101aa
com.iapp.leochen.apkinjector:style/TextAppearance.AppCompat.SearchResult.Title = 0x7f1101a9
com.iapp.leochen.apkinjector:style/TextAppearance.AppCompat.SearchResult.Subtitle = 0x7f1101a8
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.TextInputLayout.FilledBox.Dense.ExposedDropdownMenu = 0x7f110453
com.iapp.leochen.apkinjector:style/TextAppearance.AppCompat.Menu = 0x7f1101a7
com.iapp.leochen.apkinjector:style/TextAppearance.AppCompat.Medium.Inverse = 0x7f1101a6
com.iapp.leochen.apkinjector:style/TextAppearance.AppCompat.Medium = 0x7f1101a5
com.iapp.leochen.apkinjector:style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Large = 0x7f1101a3
com.iapp.leochen.apkinjector:style/TextAppearance.AppCompat.Large.Inverse = 0x7f1101a0
com.iapp.leochen.apkinjector:style/TextAppearance.AppCompat.Large = 0x7f11019f
com.iapp.leochen.apkinjector:style/TextAppearance.AppCompat.Headline = 0x7f11019d
com.iapp.leochen.apkinjector:style/TextAppearance.AppCompat.Display3 = 0x7f11019b
com.iapp.leochen.apkinjector:style/ThemeOverlay.Material3.Chip.Assist = 0x7f110294
com.iapp.leochen.apkinjector:style/TextAppearance.AppCompat.Button = 0x7f110197
com.iapp.leochen.apkinjector:style/TextAppearance.AppCompat = 0x7f110194
com.iapp.leochen.apkinjector:style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Window.Fullscreen = 0x7f110191
com.iapp.leochen.apkinjector:style/ShapeAppearanceOverlay.MaterialComponents.ExtendedFloatingActionButton = 0x7f11018e
com.iapp.leochen.apkinjector:style/ShapeAppearanceOverlay.Material3.SearchView = 0x7f11018a
com.iapp.leochen.apkinjector:style/ShapeAppearanceOverlay.Material3.NavigationView.Item = 0x7f110188
com.iapp.leochen.apkinjector:style/ShapeAppearanceOverlay.Material3.Corner.Top = 0x7f110186
com.iapp.leochen.apkinjector:style/ShapeAppearanceOverlay.Material3.Corner.Right = 0x7f110185
com.iapp.leochen.apkinjector:style/ShapeAppearanceOverlay.Material3.Corner.Left = 0x7f110184
com.iapp.leochen.apkinjector:style/ShapeAppearanceOverlay.Material3.Corner.Bottom = 0x7f110183
com.iapp.leochen.apkinjector:style/ShapeAppearanceOverlay.Material3.Chip = 0x7f110182
com.iapp.leochen.apkinjector:style/ShapeAppearance.MaterialComponents.Tooltip = 0x7f110180
com.iapp.leochen.apkinjector:style/ShapeAppearance.MaterialComponents.SmallComponent = 0x7f11017f
com.iapp.leochen.apkinjector:style/ShapeAppearance.MaterialComponents.MediumComponent = 0x7f11017e
com.iapp.leochen.apkinjector:style/ShapeAppearance.MaterialComponents.Badge = 0x7f11017c
com.iapp.leochen.apkinjector:style/ShapeAppearance.Material3.Tooltip = 0x7f11017a
com.iapp.leochen.apkinjector:style/ShapeAppearance.Material3.MediumComponent = 0x7f110177
com.iapp.leochen.apkinjector:style/ShapeAppearance.Material3.Corner.Medium = 0x7f110173
com.iapp.leochen.apkinjector:style/ShapeAppearance.Material3.Corner.ExtraSmall = 0x7f110170
com.iapp.leochen.apkinjector:style/ShapeAppearance.Material3.Corner.ExtraLarge = 0x7f11016f
com.iapp.leochen.apkinjector:style/TextAppearance.Design.Snackbar.Message = 0x7f1101d1
com.iapp.leochen.apkinjector:style/ShapeAppearance.M3.Sys.Shape.Corner.Small = 0x7f11016e
com.iapp.leochen.apkinjector:style/ShapeAppearance.M3.Sys.Shape.Corner.None = 0x7f11016d
com.iapp.leochen.apkinjector:style/ShapeAppearance.M3.Comp.TextButton.Container.Shape = 0x7f110167
com.iapp.leochen.apkinjector:style/ShapeAppearance.M3.Comp.Switch.StateLayer.Shape = 0x7f110165
com.iapp.leochen.apkinjector:style/TextAppearance.M3.Sys.Typescale.BodyMedium = 0x7f1101d5
com.iapp.leochen.apkinjector:style/ShapeAppearance.M3.Comp.Switch.Handle.Shape = 0x7f110164
com.iapp.leochen.apkinjector:style/ShapeAppearance.M3.Comp.Sheet.Side.Docked.Container.Shape = 0x7f110163
com.iapp.leochen.apkinjector:style/ShapeAppearance.M3.Comp.SearchBar.Container.Shape = 0x7f110161
com.iapp.leochen.apkinjector:style/ShapeAppearance.M3.Comp.NavigationRail.ActiveIndicator.Shape = 0x7f11015e
com.iapp.leochen.apkinjector:style/ShapeAppearance.M3.Comp.NavigationBar.Container.Shape = 0x7f11015c
com.iapp.leochen.apkinjector:style/ShapeAppearance.M3.Comp.NavigationBar.ActiveIndicator.Shape = 0x7f11015b
com.iapp.leochen.apkinjector:style/ShapeAppearance.M3.Comp.FilledButton.Container.Shape = 0x7f11015a
com.iapp.leochen.apkinjector:style/ThemeOverlay.Material3.MaterialTimePicker.Display.TextInputEditText = 0x7f1102b1
com.iapp.leochen.apkinjector:style/ShapeAppearance.M3.Comp.DatePicker.Modal.Date.Container.Shape = 0x7f110159
com.iapp.leochen.apkinjector:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Secondary = 0x7f110384
com.iapp.leochen.apkinjector:style/ShapeAppearance.M3.Comp.BottomAppBar.Container.Shape = 0x7f110158
com.iapp.leochen.apkinjector:style/ShapeAppearance.M3.Comp.Badge.Shape = 0x7f110157
com.iapp.leochen.apkinjector:style/ShapeAppearance.M3.Comp.Badge.Large.Shape = 0x7f110156
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.CircularProgressIndicator.Medium = 0x7f110415
com.iapp.leochen.apkinjector:style/RtlUnderlay.Widget.AppCompat.ActionButton.Overflow = 0x7f110155
com.iapp.leochen.apkinjector:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Query = 0x7f110151
com.iapp.leochen.apkinjector:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Icon2 = 0x7f110150
com.iapp.leochen.apkinjector:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Icon1 = 0x7f11014f
com.iapp.leochen.apkinjector:style/RtlOverlay.Widget.AppCompat.Search.DropDown = 0x7f11014e
com.iapp.leochen.apkinjector:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Title = 0x7f11014d
com.iapp.leochen.apkinjector:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Shortcut = 0x7f11014a
com.iapp.leochen.apkinjector:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.InternalGroup = 0x7f110149
com.iapp.leochen.apkinjector:style/RtlOverlay.Widget.AppCompat.PopupMenuItem = 0x7f110148
com.iapp.leochen.apkinjector:style/RtlOverlay.Widget.AppCompat.ActionBar.TitleItem = 0x7f110146
com.iapp.leochen.apkinjector:style/Platform.V25.AppCompat.Light = 0x7f110143
com.iapp.leochen.apkinjector:style/Platform.V21.AppCompat.Light = 0x7f110141
com.iapp.leochen.apkinjector:style/Platform.ThemeOverlay.AppCompat.Light = 0x7f11013f
com.iapp.leochen.apkinjector:style/Platform.ThemeOverlay.AppCompat.Dark = 0x7f11013e
com.iapp.leochen.apkinjector:style/Platform.ThemeOverlay.AppCompat = 0x7f11013d
com.iapp.leochen.apkinjector:style/Platform.MaterialComponents.Light.Dialog = 0x7f11013c
com.iapp.leochen.apkinjector:style/Platform.MaterialComponents = 0x7f110139
com.iapp.leochen.apkinjector:style/Platform.AppCompat.Light = 0x7f110138
com.iapp.leochen.apkinjector:style/MaterialAlertDialog.MaterialComponents.Title.Text.CenterStacked = 0x7f110136
com.iapp.leochen.apkinjector:style/MaterialAlertDialog.MaterialComponents.Title.Panel.CenterStacked = 0x7f110134
com.iapp.leochen.apkinjector:style/MaterialAlertDialog.MaterialComponents.Title.Panel = 0x7f110133
com.iapp.leochen.apkinjector:style/MaterialAlertDialog.MaterialComponents.Title.Icon = 0x7f110131
com.iapp.leochen.apkinjector:style/MaterialAlertDialog.MaterialComponents.Picker.Date.Spinner = 0x7f110130
com.iapp.leochen.apkinjector:style/MaterialAlertDialog.MaterialComponents.Body.Text = 0x7f11012e
com.iapp.leochen.apkinjector:style/MaterialAlertDialog.MaterialComponents = 0x7f11012d
com.iapp.leochen.apkinjector:style/MaterialAlertDialog.Material3.Title.Text.CenterStacked = 0x7f11012c
com.iapp.leochen.apkinjector:style/MaterialAlertDialog.Material3.Title.Text = 0x7f11012b
com.iapp.leochen.apkinjector:style/MaterialAlertDialog.Material3.Title.Panel = 0x7f110129
com.iapp.leochen.apkinjector:style/MaterialAlertDialog.Material3.Title.Icon.CenterStacked = 0x7f110128
com.iapp.leochen.apkinjector:style/MaterialAlertDialog.Material3.Body.Text = 0x7f110125
com.iapp.leochen.apkinjector:style/ShapeAppearance.M3.Comp.SearchView.FullScreen.Container.Shape = 0x7f110162
com.iapp.leochen.apkinjector:style/MaterialAlertDialog.Material3.Animation = 0x7f110124
com.iapp.leochen.apkinjector:style/MaterialAlertDialog.Material3 = 0x7f110123
com.iapp.leochen.apkinjector:style/CardView.Light = 0x7f110122
com.iapp.leochen.apkinjector:style/CardView.Dark = 0x7f110121
com.iapp.leochen.apkinjector:style/CardView = 0x7f110120
com.iapp.leochen.apkinjector:style/Base.Widget.MaterialComponents.Slider = 0x7f11011b
com.iapp.leochen.apkinjector:style/ThemeOverlay.Material3.DynamicColors.Light = 0x7f11029e
com.iapp.leochen.apkinjector:style/Base.Widget.MaterialComponents.PopupMenu.ListPopupWindow = 0x7f110119
com.iapp.leochen.apkinjector:style/Base.Widget.MaterialComponents.PopupMenu = 0x7f110117
com.iapp.leochen.apkinjector:style/Base.Widget.MaterialComponents.MaterialCalendar.NavigationButton = 0x7f110116
com.iapp.leochen.apkinjector:style/Base.Widget.Material3.TabLayout.Secondary = 0x7f110111
com.iapp.leochen.apkinjector:style/Base.Widget.Material3.TabLayout = 0x7f11010f
com.iapp.leochen.apkinjector:style/Base.Widget.Material3.Snackbar = 0x7f11010e
com.iapp.leochen.apkinjector:style/Base.Widget.Material3.Light.ActionBar.Solid = 0x7f11010c
com.iapp.leochen.apkinjector:style/Base.Widget.Material3.FloatingActionButton.Large = 0x7f11010a
com.iapp.leochen.apkinjector:style/Base.Widget.Material3.FloatingActionButton = 0x7f110109
com.iapp.leochen.apkinjector:style/Base.Widget.Material3.ExtendedFloatingActionButton = 0x7f110107
com.iapp.leochen.apkinjector:style/Base.Widget.Material3.CompoundButton.RadioButton = 0x7f110105
com.iapp.leochen.apkinjector:style/Base.Widget.Material3.CompoundButton.CheckBox = 0x7f110104
com.iapp.leochen.apkinjector:style/Base.Widget.Material3.Chip = 0x7f110102
com.iapp.leochen.apkinjector:style/Base.Widget.Material3.BottomNavigationView = 0x7f110100
com.iapp.leochen.apkinjector:style/Base.Widget.Material3.ActionMode = 0x7f1100ff
com.iapp.leochen.apkinjector:style/Base.Widget.Material3.ActionBar.Solid = 0x7f1100fe
com.iapp.leochen.apkinjector:style/Widget.Material3.Chip.Suggestion.Elevated = 0x7f110371
com.iapp.leochen.apkinjector:style/Base.Widget.AppCompat.Toolbar.Button.Navigation = 0x7f1100fc
com.iapp.leochen.apkinjector:style/Theme.MaterialComponents.Dialog.FixedSize = 0x7f11025f
com.iapp.leochen.apkinjector:style/Base.Widget.AppCompat.Toolbar = 0x7f1100fb
com.iapp.leochen.apkinjector:style/Base.Widget.AppCompat.TextView = 0x7f1100f9
com.iapp.leochen.apkinjector:style/Base.Widget.AppCompat.SeekBar.Discrete = 0x7f1100f6
com.iapp.leochen.apkinjector:style/Base.Widget.AppCompat.RatingBar.Indicator = 0x7f1100f1
com.iapp.leochen.apkinjector:style/Base.Widget.AppCompat.ProgressBar.Horizontal = 0x7f1100ef
com.iapp.leochen.apkinjector:style/Base.Widget.AppCompat.PopupMenu.Overflow = 0x7f1100ec
com.iapp.leochen.apkinjector:style/Base.Widget.AppCompat.ListView.Menu = 0x7f1100ea
com.iapp.leochen.apkinjector:style/Base.Widget.AppCompat.ListMenuView = 0x7f1100e6
com.iapp.leochen.apkinjector:style/Base.Widget.AppCompat.Light.ActionBar.TabText = 0x7f1100e1
com.iapp.leochen.apkinjector:style/Base.Widget.AppCompat.Light.ActionBar.TabBar = 0x7f1100e0
com.iapp.leochen.apkinjector:style/Base.Widget.AppCompat.Light.ActionBar.Solid = 0x7f1100df
com.iapp.leochen.apkinjector:style/Base.Widget.AppCompat.DropDownItem.Spinner = 0x7f1100db
com.iapp.leochen.apkinjector:style/Base.Widget.AppCompat.ButtonBar.AlertDialog = 0x7f1100d5
com.iapp.leochen.apkinjector:style/Base.Widget.AppCompat.Button.Borderless = 0x7f1100cf
com.iapp.leochen.apkinjector:style/Base.Widget.AppCompat.AutoCompleteTextView = 0x7f1100cd
com.iapp.leochen.apkinjector:style/Base.Widget.AppCompat.ActivityChooserView = 0x7f1100cc
com.iapp.leochen.apkinjector:style/Base.Widget.AppCompat.ActionButton.Overflow = 0x7f1100ca
com.iapp.leochen.apkinjector:style/Base.Widget.AppCompat.ActionButton.CloseMode = 0x7f1100c9
com.iapp.leochen.apkinjector:style/Base.Widget.AppCompat.ActionButton = 0x7f1100c8
com.iapp.leochen.apkinjector:style/Base.Widget.AppCompat.ActionBar = 0x7f1100c3
com.iapp.leochen.apkinjector:style/Base.V7.Widget.AppCompat.Toolbar = 0x7f1100c2
com.iapp.leochen.apkinjector:style/Base.V7.Widget.AppCompat.EditText = 0x7f1100c1
com.iapp.leochen.apkinjector:styleable/ConstraintLayout_placeholder = 0x7f120029
com.iapp.leochen.apkinjector:style/Base.V7.ThemeOverlay.AppCompat.Dialog = 0x7f1100bf
com.iapp.leochen.apkinjector:style/Base.V7.Theme.AppCompat.Dialog = 0x7f1100bc
com.iapp.leochen.apkinjector:style/Base.V28.Theme.AppCompat.Light = 0x7f1100ba
com.iapp.leochen.apkinjector:style/Base.V26.Widget.AppCompat.Toolbar = 0x7f1100b8
com.iapp.leochen.apkinjector:style/Base.V26.Theme.AppCompat.Light = 0x7f1100b7
com.iapp.leochen.apkinjector:style/Base.V26.Theme.AppCompat = 0x7f1100b6
com.iapp.leochen.apkinjector:style/Base.V24.Theme.Material3.Dark.Dialog = 0x7f1100b3
com.iapp.leochen.apkinjector:style/Base.V24.Theme.Material3.Dark = 0x7f1100b2
com.iapp.leochen.apkinjector:style/Base.V23.Theme.AppCompat = 0x7f1100b0
com.iapp.leochen.apkinjector:style/Base.V22.Theme.AppCompat.Light = 0x7f1100af
com.iapp.leochen.apkinjector:styleable/AppCompatTheme = 0x7f120012
com.iapp.leochen.apkinjector:style/Base.V21.ThemeOverlay.MaterialComponents.BottomSheetDialog = 0x7f1100ad
com.iapp.leochen.apkinjector:style/Base.V21.ThemeOverlay.AppCompat.Dialog = 0x7f1100aa
com.iapp.leochen.apkinjector:style/Base.V21.Theme.MaterialComponents.Light.Dialog = 0x7f1100a9
com.iapp.leochen.apkinjector:style/Base.V21.Theme.MaterialComponents.Dialog = 0x7f1100a7
com.iapp.leochen.apkinjector:style/Base.V21.Theme.AppCompat = 0x7f1100a2
com.iapp.leochen.apkinjector:style/Base.V14.ThemeOverlay.MaterialComponents.MaterialAlertDialog = 0x7f1100a0
com.iapp.leochen.apkinjector:style/Base.V14.ThemeOverlay.Material3.SideSheetDialog = 0x7f11009c
com.iapp.leochen.apkinjector:style/Base.V14.ThemeOverlay.Material3.BottomSheetDialog = 0x7f11009b
com.iapp.leochen.apkinjector:style/Base.V14.Theme.MaterialComponents.Light.Dialog = 0x7f110099
com.iapp.leochen.apkinjector:style/TextAppearance.M3.Sys.Typescale.LabelSmall = 0x7f1101df
com.iapp.leochen.apkinjector:style/Base.V14.Theme.MaterialComponents.Light.DarkActionBar.Bridge = 0x7f110098
com.iapp.leochen.apkinjector:style/Base.V14.Theme.MaterialComponents.Dialog = 0x7f110094
com.iapp.leochen.apkinjector:style/Base.V14.Theme.Material3.Light.Dialog = 0x7f110090
com.iapp.leochen.apkinjector:style/Base.V14.Theme.Material3.Light.BottomSheetDialog = 0x7f11008f
com.iapp.leochen.apkinjector:style/Base.V14.Theme.Material3.Light = 0x7f11008e
com.iapp.leochen.apkinjector:styleable/AnimatedStateListDrawableCompat = 0x7f120007
com.iapp.leochen.apkinjector:style/Base.V14.Theme.Material3.Dark.BottomSheetDialog = 0x7f11008b
com.iapp.leochen.apkinjector:style/Widget.Design.FloatingActionButton = 0x7f110337
com.iapp.leochen.apkinjector:style/Base.ThemeOverlay.MaterialComponents.Dialog = 0x7f110085
com.iapp.leochen.apkinjector:style/Base.ThemeOverlay.Material3.Dialog = 0x7f110082
com.iapp.leochen.apkinjector:style/Base.ThemeOverlay.AppCompat.Dialog.Alert = 0x7f11007e
com.iapp.leochen.apkinjector:style/Base.ThemeOverlay.AppCompat.Dialog = 0x7f11007d
com.iapp.leochen.apkinjector:style/Base.Theme.MaterialComponents.Light.DialogWhenLarge = 0x7f110078
com.iapp.leochen.apkinjector:style/Base.Theme.MaterialComponents.Light.Dialog.Bridge = 0x7f110075
com.iapp.leochen.apkinjector:style/Base.Theme.MaterialComponents.Light.Dialog = 0x7f110073
com.iapp.leochen.apkinjector:style/Base.Theme.MaterialComponents.Light.DarkActionBar.Bridge = 0x7f110072
com.iapp.leochen.apkinjector:style/Base.Theme.MaterialComponents.DialogWhenLarge = 0x7f11006e
com.iapp.leochen.apkinjector:style/Base.Theme.MaterialComponents.Dialog.MinWidth = 0x7f11006d
com.iapp.leochen.apkinjector:style/Base.Theme.MaterialComponents.Dialog.FixedSize = 0x7f11006c
com.iapp.leochen.apkinjector:style/Base.Theme.MaterialComponents.Dialog.Alert = 0x7f11006a
com.iapp.leochen.apkinjector:style/Base.Theme.MaterialComponents.CompactMenu = 0x7f110068
com.iapp.leochen.apkinjector:style/Base.Theme.Material3.Light.Dialog.FixedSize = 0x7f110063
com.iapp.leochen.apkinjector:style/ShapeAppearanceOverlay.MaterialComponents.TextInputLayout.FilledBox = 0x7f110193
com.iapp.leochen.apkinjector:style/Base.Theme.Material3.Light.Dialog = 0x7f110062
com.iapp.leochen.apkinjector:style/Base.Theme.Material3.Light.BottomSheetDialog = 0x7f110061
com.iapp.leochen.apkinjector:style/Base.ThemeOverlay.AppCompat.ActionBar = 0x7f11007a
com.iapp.leochen.apkinjector:style/Base.Theme.Material3.Light = 0x7f110060
com.iapp.leochen.apkinjector:style/Base.Theme.Material3.Dark.SideSheetDialog = 0x7f11005f
com.iapp.leochen.apkinjector:style/Base.Theme.Material3.Dark.DialogWhenLarge = 0x7f11005e
com.iapp.leochen.apkinjector:style/Widget.Material3.MaterialDivider.Heavy = 0x7f1103b3
com.iapp.leochen.apkinjector:style/Theme.MaterialComponents.Light.Dialog.FixedSize = 0x7f11026d
com.iapp.leochen.apkinjector:style/Base.Theme.AppCompat.Light.DialogWhenLarge = 0x7f110059
com.iapp.leochen.apkinjector:style/Base.Theme.AppCompat.Light.Dialog.FixedSize = 0x7f110057
com.iapp.leochen.apkinjector:style/Base.Theme.AppCompat.Light.Dialog.Alert = 0x7f110056
com.iapp.leochen.apkinjector:style/Base.Theme.AppCompat.Light.Dialog = 0x7f110055
com.iapp.leochen.apkinjector:style/Base.Theme.AppCompat.Light.DarkActionBar = 0x7f110054
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.BottomNavigationView.PrimarySurface = 0x7f1103fd
com.iapp.leochen.apkinjector:style/Base.Theme.AppCompat.Dialog.FixedSize = 0x7f110050
com.iapp.leochen.apkinjector:style/Base.Theme.AppCompat.Dialog.Alert = 0x7f11004f
com.iapp.leochen.apkinjector:style/Widget.Material3.CircularProgressIndicator.Small = 0x7f11037a
com.iapp.leochen.apkinjector:style/Base.Theme.AppCompat = 0x7f11004c
com.iapp.leochen.apkinjector:style/TextAppearance.Design.Counter.Overflow = 0x7f1101cb
com.iapp.leochen.apkinjector:style/Base.Theme.ApkInjector = 0x7f11004b
com.iapp.leochen.apkinjector:style/Base.V28.Theme.AppCompat = 0x7f1100b9
com.iapp.leochen.apkinjector:style/Base.TextAppearance.MaterialComponents.Subtitle2 = 0x7f110047
com.iapp.leochen.apkinjector:style/Base.TextAppearance.MaterialComponents.Button = 0x7f110045
com.iapp.leochen.apkinjector:style/Base.Widget.Material3.CardView = 0x7f110101
com.iapp.leochen.apkinjector:style/Base.TextAppearance.MaterialComponents.Badge = 0x7f110044
com.iapp.leochen.apkinjector:style/Base.TextAppearance.Material3.Search = 0x7f110043
com.iapp.leochen.apkinjector:style/Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem = 0x7f110042
com.iapp.leochen.apkinjector:style/Base.TextAppearance.AppCompat.Widget.Switch = 0x7f110041
com.iapp.leochen.apkinjector:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Small = 0x7f110040
com.iapp.leochen.apkinjector:style/Base.TextAppearance.AppCompat.Widget.DropDownItem = 0x7f11003d
com.iapp.leochen.apkinjector:style/Base.TextAppearance.AppCompat.Widget.Button.Inverse = 0x7f11003c
com.iapp.leochen.apkinjector:style/Base.TextAppearance.AppCompat.Widget.Button.Borderless.Colored = 0x7f11003a
com.iapp.leochen.apkinjector:style/Base.TextAppearance.AppCompat.Widget.Button = 0x7f110039
com.iapp.leochen.apkinjector:style/Widget.Material3.Slider = 0x7f1103d0
com.iapp.leochen.apkinjector:style/Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle = 0x7f110037
com.iapp.leochen.apkinjector:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title = 0x7f110035
com.iapp.leochen.apkinjector:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView = 0x7f1102c4
com.iapp.leochen.apkinjector:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Menu = 0x7f110032
com.iapp.leochen.apkinjector:style/Base.TextAppearance.AppCompat.Tooltip = 0x7f110031
com.iapp.leochen.apkinjector:style/Base.TextAppearance.AppCompat.Title.Inverse = 0x7f110030
com.iapp.leochen.apkinjector:style/Base.TextAppearance.AppCompat.Subhead.Inverse = 0x7f11002e
com.iapp.leochen.apkinjector:style/ShapeAppearanceOverlay.MaterialComponents.BottomSheet = 0x7f11018c
com.iapp.leochen.apkinjector:style/Base.TextAppearance.AppCompat.Subhead = 0x7f11002d
com.iapp.leochen.apkinjector:style/Base.TextAppearance.AppCompat.Small.Inverse = 0x7f11002c
com.iapp.leochen.apkinjector:style/Widget.Material3.Chip.Input = 0x7f11036c
com.iapp.leochen.apkinjector:style/Base.TextAppearance.AppCompat.SearchResult.Title = 0x7f11002a
com.iapp.leochen.apkinjector:style/Widget.Material3.BottomNavigation.Badge = 0x7f11034b
com.iapp.leochen.apkinjector:style/Base.TextAppearance.AppCompat.SearchResult.Subtitle = 0x7f110029
com.iapp.leochen.apkinjector:style/Base.TextAppearance.AppCompat.Menu = 0x7f110027
com.iapp.leochen.apkinjector:style/Base.TextAppearance.AppCompat.Medium = 0x7f110025
com.iapp.leochen.apkinjector:style/Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Small = 0x7f110024
com.iapp.leochen.apkinjector:style/Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Large = 0x7f110023
com.iapp.leochen.apkinjector:style/Base.TextAppearance.AppCompat.Large.Inverse = 0x7f110022
com.iapp.leochen.apkinjector:style/Base.TextAppearance.AppCompat.Large = 0x7f110021
com.iapp.leochen.apkinjector:style/Base.TextAppearance.AppCompat.Inverse = 0x7f110020
com.iapp.leochen.apkinjector:style/Base.TextAppearance.AppCompat.Button = 0x7f110019
com.iapp.leochen.apkinjector:style/Base.TextAppearance.AppCompat.Body1 = 0x7f110017
com.iapp.leochen.apkinjector:style/Base.TextAppearance.AppCompat = 0x7f110016
com.iapp.leochen.apkinjector:style/Base.MaterialAlertDialog.MaterialComponents.Title.Text = 0x7f110015
com.iapp.leochen.apkinjector:style/Base.MaterialAlertDialog.MaterialComponents.Title.Panel = 0x7f110014
com.iapp.leochen.apkinjector:style/Base.MaterialAlertDialog.MaterialComponents.Title.Icon = 0x7f110013
com.iapp.leochen.apkinjector:style/Base.DialogWindowTitle.AppCompat = 0x7f110011
com.iapp.leochen.apkinjector:style/Base.Animation.AppCompat.Dialog = 0x7f11000d
com.iapp.leochen.apkinjector:style/Base.AlertDialog.AppCompat = 0x7f11000b
com.iapp.leochen.apkinjector:styleable/ConstraintLayout_Layout = 0x7f120027
com.iapp.leochen.apkinjector:style/Animation.Material3.SideSheetDialog.Right = 0x7f110009
com.iapp.leochen.apkinjector:style/Animation.Material3.BottomSheetDialog = 0x7f110006
com.iapp.leochen.apkinjector:style/Animation.Design.BottomSheetDialog = 0x7f110005
com.iapp.leochen.apkinjector:style/Animation.AppCompat.Tooltip = 0x7f110004
com.iapp.leochen.apkinjector:style/AlertDialog.AppCompat = 0x7f110000
com.iapp.leochen.apkinjector:string/status_bar_notification_info_overflow = 0x7f1000a6
com.iapp.leochen.apkinjector:string/side_sheet_accessibility_pane_title = 0x7f1000a4
com.iapp.leochen.apkinjector:string/searchbar_scrolling_view_behavior = 0x7f1000a1
com.iapp.leochen.apkinjector:string/search_menu_title = 0x7f1000a0
com.iapp.leochen.apkinjector:string/path_password_strike_through = 0x7f10009f
com.iapp.leochen.apkinjector:string/path_password_eye_mask_visible = 0x7f10009e
com.iapp.leochen.apkinjector:string/mtrl_switch_track_decoration_path = 0x7f100097
com.iapp.leochen.apkinjector:string/mtrl_switch_thumb_path_unchecked = 0x7f100096
com.iapp.leochen.apkinjector:string/mtrl_switch_thumb_path_pressed = 0x7f100095
com.iapp.leochen.apkinjector:string/mtrl_switch_thumb_path_name = 0x7f100094
com.iapp.leochen.apkinjector:string/mtrl_switch_thumb_path_morphing = 0x7f100093
com.iapp.leochen.apkinjector:style/ThemeOverlay.Material3.Button = 0x7f11028b
com.iapp.leochen.apkinjector:string/mtrl_picker_toggle_to_year_selection = 0x7f100090
com.iapp.leochen.apkinjector:string/mtrl_picker_today_description = 0x7f10008c
com.iapp.leochen.apkinjector:style/ThemeOverlay.Material3.Dialog.Alert.Framework = 0x7f11029b
com.iapp.leochen.apkinjector:string/mtrl_picker_text_input_year_abbr = 0x7f10008b
com.iapp.leochen.apkinjector:string/mtrl_picker_text_input_month_abbr = 0x7f10008a
com.iapp.leochen.apkinjector:string/mtrl_picker_text_input_date_range_start_hint = 0x7f100088
com.iapp.leochen.apkinjector:string/mtrl_picker_text_input_date_range_end_hint = 0x7f100087
com.iapp.leochen.apkinjector:string/mtrl_picker_start_date_description = 0x7f100085
com.iapp.leochen.apkinjector:string/mtrl_picker_save = 0x7f100084
com.iapp.leochen.apkinjector:string/mtrl_picker_range_header_title = 0x7f100082
com.iapp.leochen.apkinjector:string/mtrl_picker_range_header_selected = 0x7f100081
com.iapp.leochen.apkinjector:string/mtrl_picker_range_header_only_end_selected = 0x7f10007f
com.iapp.leochen.apkinjector:string/mtrl_picker_invalid_range = 0x7f10007b
com.iapp.leochen.apkinjector:styleable/MaterialSwitch = 0x7f12005a
com.iapp.leochen.apkinjector:string/mtrl_picker_invalid_format = 0x7f100078
com.iapp.leochen.apkinjector:style/Theme.Material3.Dark = 0x7f110229
com.iapp.leochen.apkinjector:string/mtrl_picker_end_date_description = 0x7f100077
com.iapp.leochen.apkinjector:style/Base.Widget.MaterialComponents.CheckedTextView = 0x7f110113
com.iapp.leochen.apkinjector:string/mtrl_picker_day_of_week_column_header = 0x7f100076
com.iapp.leochen.apkinjector:style/Base.TextAppearance.AppCompat.Display4 = 0x7f11001e
com.iapp.leochen.apkinjector:string/mtrl_picker_date_header_unselected = 0x7f100075
com.iapp.leochen.apkinjector:string/mtrl_picker_date_header_selected = 0x7f100073
com.iapp.leochen.apkinjector:string/mtrl_picker_announce_current_selection_none = 0x7f100070
com.iapp.leochen.apkinjector:string/mtrl_picker_announce_current_range_selection = 0x7f10006e
com.iapp.leochen.apkinjector:string/mtrl_picker_a11y_prev_month = 0x7f10006d
com.iapp.leochen.apkinjector:string/mtrl_picker_a11y_next_month = 0x7f10006c
com.iapp.leochen.apkinjector:string/mtrl_exceed_max_badge_number_suffix = 0x7f10006b
com.iapp.leochen.apkinjector:string/mtrl_exceed_max_badge_number_content_description = 0x7f10006a
com.iapp.leochen.apkinjector:style/TextAppearance.MaterialComponents.Button = 0x7f1101fb
com.iapp.leochen.apkinjector:string/mtrl_checkbox_state_description_unchecked = 0x7f100068
com.iapp.leochen.apkinjector:string/mtrl_checkbox_state_description_indeterminate = 0x7f100067
com.iapp.leochen.apkinjector:string/mtrl_checkbox_button_path_unchecked = 0x7f100065
com.iapp.leochen.apkinjector:string/mtrl_checkbox_button_path_group_name = 0x7f100063
com.iapp.leochen.apkinjector:style/ThemeOverlay.Material3.NavigationView = 0x7f1102b3
com.iapp.leochen.apkinjector:string/mtrl_checkbox_button_path_checked = 0x7f100062
com.iapp.leochen.apkinjector:string/mtrl_checkbox_button_icon_path_checked = 0x7f10005e
com.iapp.leochen.apkinjector:string/mtrl_badge_numberless_content_description = 0x7f10005d
com.iapp.leochen.apkinjector:string/material_timepicker_select_time = 0x7f10005b
com.iapp.leochen.apkinjector:string/material_timepicker_pm = 0x7f10005a
com.iapp.leochen.apkinjector:string/material_timepicker_minute = 0x7f100059
com.iapp.leochen.apkinjector:string/material_timepicker_hour = 0x7f100058
com.iapp.leochen.apkinjector:string/material_slider_range_start = 0x7f100054
com.iapp.leochen.apkinjector:string/material_slider_range_end = 0x7f100053
com.iapp.leochen.apkinjector:string/material_motion_easing_decelerated = 0x7f10004f
com.iapp.leochen.apkinjector:string/material_minute_suffix = 0x7f10004d
com.iapp.leochen.apkinjector:style/ShapeAppearance.Material3.Corner.Full = 0x7f110171
com.iapp.leochen.apkinjector:string/material_minute_selection = 0x7f10004c
com.iapp.leochen.apkinjector:string/material_hour_suffix = 0x7f10004b
com.iapp.leochen.apkinjector:style/ThemeOverlay.MaterialComponents.ActionBar = 0x7f1102c1
com.iapp.leochen.apkinjector:style/Base.Theme.Material3.Light.DialogWhenLarge = 0x7f110064
com.iapp.leochen.apkinjector:string/material_hour_selection = 0x7f10004a
com.iapp.leochen.apkinjector:string/material_hour_24h_suffix = 0x7f100049
com.iapp.leochen.apkinjector:string/m3_sys_motion_easing_standard_decelerate = 0x7f100046
com.iapp.leochen.apkinjector:style/Theme.Material3.DynamicColors.Light.NoActionBar = 0x7f11023e
com.iapp.leochen.apkinjector:string/m3_sys_motion_easing_legacy_decelerate = 0x7f100042
com.iapp.leochen.apkinjector:string/m3_sys_motion_easing_legacy_accelerate = 0x7f100041
com.iapp.leochen.apkinjector:string/m3_sys_motion_easing_legacy = 0x7f100040
com.iapp.leochen.apkinjector:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Surface = 0x7f110385
com.iapp.leochen.apkinjector:string/m3_sys_motion_easing_emphasized_accelerate = 0x7f10003d
com.iapp.leochen.apkinjector:style/Base.V14.ThemeOverlay.MaterialComponents.Dialog.Alert = 0x7f11009f
com.iapp.leochen.apkinjector:string/m3_ref_typeface_plain_regular = 0x7f10003b
com.iapp.leochen.apkinjector:string/m3_ref_typeface_brand_medium = 0x7f100038
com.iapp.leochen.apkinjector:string/m3_exceed_max_badge_text_suffix = 0x7f100037
com.iapp.leochen.apkinjector:string/error_icon_content_description = 0x7f100030
com.iapp.leochen.apkinjector:style/Base.Theme.MaterialComponents.Light.Dialog.MinWidth = 0x7f110077
com.iapp.leochen.apkinjector:string/character_counter_pattern = 0x7f10002d
com.iapp.leochen.apkinjector:string/character_counter_content_description = 0x7f10002b
com.iapp.leochen.apkinjector:string/call_notification_screening_text = 0x7f10002a
com.iapp.leochen.apkinjector:string/call_notification_ongoing_text = 0x7f100029
com.iapp.leochen.apkinjector:string/call_notification_decline_action = 0x7f100026
com.iapp.leochen.apkinjector:string/call_notification_answer_video_action = 0x7f100025
com.iapp.leochen.apkinjector:string/call_notification_answer_action = 0x7f100024
com.iapp.leochen.apkinjector:string/bottomsheet_drag_handle_clicked = 0x7f100022
com.iapp.leochen.apkinjector:string/bottom_sheet_behavior = 0x7f10001e
com.iapp.leochen.apkinjector:style/Base.Widget.AppCompat.ListView = 0x7f1100e8
com.iapp.leochen.apkinjector:string/appbar_scrolling_view_behavior = 0x7f10001d
com.iapp.leochen.apkinjector:style/ThemeOverlay.MaterialComponents.Toolbar.Primary = 0x7f1102e6
com.iapp.leochen.apkinjector:string/app_name = 0x7f10001c
com.iapp.leochen.apkinjector:string/abc_toolbar_collapse_description = 0x7f10001a
com.iapp.leochen.apkinjector:string/abc_shareactionprovider_share_with_application = 0x7f100019
com.iapp.leochen.apkinjector:string/abc_shareactionprovider_share_with = 0x7f100018
com.iapp.leochen.apkinjector:string/abc_searchview_description_submit = 0x7f100016
com.iapp.leochen.apkinjector:string/abc_searchview_description_clear = 0x7f100013
com.iapp.leochen.apkinjector:string/abc_search_hint = 0x7f100012
com.iapp.leochen.apkinjector:style/Widget.AppCompat.ListView.Menu = 0x7f11031c
com.iapp.leochen.apkinjector:string/abc_prepend_shortcut_label = 0x7f100011
com.iapp.leochen.apkinjector:style/Platform.MaterialComponents.Light = 0x7f11013b
com.iapp.leochen.apkinjector:string/abc_menu_sym_shortcut_label = 0x7f100010
com.iapp.leochen.apkinjector:string/abc_menu_function_shortcut_label = 0x7f10000c
com.iapp.leochen.apkinjector:string/abc_searchview_description_search = 0x7f100015
com.iapp.leochen.apkinjector:string/abc_menu_enter_shortcut_label = 0x7f10000b
com.iapp.leochen.apkinjector:style/Widget.Material3.SideSheet = 0x7f1103cc
com.iapp.leochen.apkinjector:string/abc_menu_ctrl_shortcut_label = 0x7f100009
com.iapp.leochen.apkinjector:string/abc_capital_off = 0x7f100006
com.iapp.leochen.apkinjector:string/abc_activitychooserview_choose_application = 0x7f100005
com.iapp.leochen.apkinjector:string/abc_activity_chooser_view_see_all = 0x7f100004
com.iapp.leochen.apkinjector:style/Base.Widget.AppCompat.Button.Small = 0x7f1100d3
com.iapp.leochen.apkinjector:string/abc_action_mode_done = 0x7f100003
com.iapp.leochen.apkinjector:string/abc_action_menu_overflow_description = 0x7f100002
com.iapp.leochen.apkinjector:style/Widget.AppCompat.PopupMenu.Overflow = 0x7f11031e
com.iapp.leochen.apkinjector:string/abc_action_bar_home_description = 0x7f100000
com.iapp.leochen.apkinjector:style/TextAppearance.Design.HelperText = 0x7f1101cd
com.iapp.leochen.apkinjector:macro/m3_sys_color_dark_surface_tint = 0x7f0c0175
com.iapp.leochen.apkinjector:macro/m3_comp_top_app_bar_small_headline_type = 0x7f0c0171
com.iapp.leochen.apkinjector:macro/m3_comp_top_app_bar_small_headline_color = 0x7f0c0170
com.iapp.leochen.apkinjector:macro/m3_comp_time_picker_time_selector_unselected_focus_state_layer_color = 0x7f0c0168
com.iapp.leochen.apkinjector:macro/m3_comp_time_picker_time_selector_unselected_container_color = 0x7f0c0167
com.iapp.leochen.apkinjector:macro/m3_comp_time_picker_time_selector_separator_type = 0x7f0c0166
com.iapp.leochen.apkinjector:macro/m3_comp_time_picker_time_selector_selected_label_text_color = 0x7f0c0163
com.iapp.leochen.apkinjector:macro/m3_comp_time_picker_time_selector_selected_container_color = 0x7f0c0160
com.iapp.leochen.apkinjector:macro/m3_comp_time_picker_time_selector_container_shape = 0x7f0c015e
com.iapp.leochen.apkinjector:macro/m3_comp_time_picker_period_selector_unselected_pressed_state_layer_color = 0x7f0c015d
com.iapp.leochen.apkinjector:macro/m3_comp_time_picker_period_selector_selected_pressed_state_layer_color = 0x7f0c0159
com.iapp.leochen.apkinjector:macro/m3_comp_time_picker_period_selector_selected_hover_state_layer_color = 0x7f0c0157
com.iapp.leochen.apkinjector:macro/m3_comp_time_picker_period_selector_selected_container_color = 0x7f0c0155
com.iapp.leochen.apkinjector:style/Theme.MaterialComponents.Dialog.Alert.Bridge = 0x7f11025d
com.iapp.leochen.apkinjector:macro/m3_comp_time_picker_period_selector_outline_color = 0x7f0c0154
com.iapp.leochen.apkinjector:macro/m3_comp_time_picker_period_selector_label_text_type = 0x7f0c0153
com.iapp.leochen.apkinjector:macro/m3_comp_time_picker_headline_type = 0x7f0c0151
com.iapp.leochen.apkinjector:macro/m3_comp_time_picker_container_shape = 0x7f0c014f
com.iapp.leochen.apkinjector:macro/m3_comp_time_picker_container_color = 0x7f0c014e
com.iapp.leochen.apkinjector:style/Widget.Material3.MaterialTimePicker.Display.TextInputEditText = 0x7f1103ba
com.iapp.leochen.apkinjector:macro/m3_comp_time_picker_clock_dial_selector_handle_container_color = 0x7f0c014d
com.iapp.leochen.apkinjector:macro/m3_comp_time_input_time_input_field_supporting_text_type = 0x7f0c014b
com.iapp.leochen.apkinjector:macro/m3_comp_time_input_time_input_field_supporting_text_color = 0x7f0c014a
com.iapp.leochen.apkinjector:macro/m3_comp_text_button_label_text_type = 0x7f0c0145
com.iapp.leochen.apkinjector:macro/m3_comp_text_button_focus_state_layer_color = 0x7f0c0142
com.iapp.leochen.apkinjector:macro/m3_comp_switch_unselected_pressed_track_outline_color = 0x7f0c013f
com.iapp.leochen.apkinjector:macro/m3_comp_switch_unselected_hover_icon_color = 0x7f0c0136
com.iapp.leochen.apkinjector:macro/m3_comp_switch_unselected_handle_color = 0x7f0c0134
com.iapp.leochen.apkinjector:macro/m3_comp_switch_unselected_focus_state_layer_color = 0x7f0c0131
com.iapp.leochen.apkinjector:macro/m3_comp_switch_unselected_focus_icon_color = 0x7f0c0130
com.iapp.leochen.apkinjector:macro/m3_comp_switch_selected_track_color = 0x7f0c012e
com.iapp.leochen.apkinjector:macro/m3_comp_switch_selected_pressed_track_color = 0x7f0c012d
com.iapp.leochen.apkinjector:macro/m3_comp_switch_selected_pressed_state_layer_color = 0x7f0c012c
com.iapp.leochen.apkinjector:macro/m3_comp_switch_selected_pressed_handle_color = 0x7f0c012a
com.iapp.leochen.apkinjector:macro/m3_comp_switch_selected_hover_icon_color = 0x7f0c0126
com.iapp.leochen.apkinjector:macro/m3_comp_switch_selected_hover_handle_color = 0x7f0c0125
com.iapp.leochen.apkinjector:macro/m3_comp_switch_selected_focus_track_color = 0x7f0c0123
com.iapp.leochen.apkinjector:macro/m3_comp_switch_unselected_icon_color = 0x7f0c013a
com.iapp.leochen.apkinjector:macro/m3_comp_switch_selected_focus_icon_color = 0x7f0c0121
com.iapp.leochen.apkinjector:macro/m3_comp_switch_selected_focus_handle_color = 0x7f0c0120
com.iapp.leochen.apkinjector:macro/m3_comp_switch_disabled_unselected_handle_color = 0x7f0c011c
com.iapp.leochen.apkinjector:macro/m3_comp_switch_disabled_selected_icon_color = 0x7f0c011a
com.iapp.leochen.apkinjector:macro/m3_comp_switch_disabled_selected_handle_color = 0x7f0c0119
com.iapp.leochen.apkinjector:macro/m3_comp_suggestion_chip_label_text_type = 0x7f0c0118
com.iapp.leochen.apkinjector:macro/m3_comp_suggestion_chip_container_shape = 0x7f0c0117
com.iapp.leochen.apkinjector:macro/m3_comp_snackbar_container_shape = 0x7f0c0114
com.iapp.leochen.apkinjector:macro/m3_comp_slider_label_container_color = 0x7f0c0111
com.iapp.leochen.apkinjector:macro/m3_comp_slider_inactive_track_color = 0x7f0c0110
com.iapp.leochen.apkinjector:macro/m3_comp_slider_handle_color = 0x7f0c010f
com.iapp.leochen.apkinjector:macro/m3_comp_slider_disabled_handle_color = 0x7f0c010d
com.iapp.leochen.apkinjector:macro/m3_comp_slider_disabled_active_track_color = 0x7f0c010c
com.iapp.leochen.apkinjector:macro/m3_comp_slider_active_track_color = 0x7f0c010b
com.iapp.leochen.apkinjector:style/Base.ThemeOverlay.AppCompat.Dark = 0x7f11007b
com.iapp.leochen.apkinjector:macro/m3_comp_sheet_side_docked_standard_container_color = 0x7f0c010a
com.iapp.leochen.apkinjector:macro/m3_comp_sheet_side_docked_modal_container_shape = 0x7f0c0109
com.iapp.leochen.apkinjector:macro/m3_comp_sheet_bottom_docked_container_shape = 0x7f0c0105
com.iapp.leochen.apkinjector:styleable/ConstraintLayout_ReactiveGuide = 0x7f120028
com.iapp.leochen.apkinjector:macro/m3_comp_secondary_navigation_tab_with_icon_inactive_icon_color = 0x7f0c0103
com.iapp.leochen.apkinjector:macro/m3_comp_secondary_navigation_tab_pressed_state_layer_color = 0x7f0c0101
com.iapp.leochen.apkinjector:string/mtrl_picker_toggle_to_day_selection = 0x7f10008e
com.iapp.leochen.apkinjector:macro/m3_comp_secondary_navigation_tab_label_text_type = 0x7f0c0100
com.iapp.leochen.apkinjector:macro/m3_comp_secondary_navigation_tab_hover_state_layer_color = 0x7f0c00fe
com.iapp.leochen.apkinjector:style/Platform.MaterialComponents.Dialog = 0x7f11013a
com.iapp.leochen.apkinjector:macro/m3_comp_secondary_navigation_tab_focus_state_layer_color = 0x7f0c00fd
com.iapp.leochen.apkinjector:styleable/FontFamilyFont = 0x7f120037
com.iapp.leochen.apkinjector:macro/m3_comp_secondary_navigation_tab_active_indicator_color = 0x7f0c00fa
com.iapp.leochen.apkinjector:macro/m3_comp_search_view_header_trailing_icon_color = 0x7f0c00f9
com.iapp.leochen.apkinjector:macro/m3_comp_search_view_header_supporting_text_type = 0x7f0c00f8
com.iapp.leochen.apkinjector:macro/m3_comp_search_view_header_input_text_color = 0x7f0c00f4
com.iapp.leochen.apkinjector:macro/m3_comp_search_view_divider_color = 0x7f0c00f2
com.iapp.leochen.apkinjector:style/RtlOverlay.Widget.AppCompat.SearchView.MagIcon = 0x7f110153
com.iapp.leochen.apkinjector:macro/m3_comp_search_view_container_color = 0x7f0c00f1
com.iapp.leochen.apkinjector:macro/m3_comp_search_bar_supporting_text_type = 0x7f0c00ef
com.iapp.leochen.apkinjector:style/ThemeOverlay.Material3.Snackbar = 0x7f1102b7
com.iapp.leochen.apkinjector:macro/m3_comp_search_bar_pressed_supporting_text_color = 0x7f0c00ed
com.iapp.leochen.apkinjector:macro/m3_comp_search_bar_pressed_state_layer_color = 0x7f0c00ec
com.iapp.leochen.apkinjector:macro/m3_comp_search_bar_input_text_type = 0x7f0c00ea
com.iapp.leochen.apkinjector:style/ShapeAppearance.Material3.Corner.Large = 0x7f110172
com.iapp.leochen.apkinjector:macro/m3_comp_search_bar_input_text_color = 0x7f0c00e9
com.iapp.leochen.apkinjector:macro/m3_comp_search_bar_container_color = 0x7f0c00e6
com.iapp.leochen.apkinjector:macro/m3_comp_radio_button_unselected_pressed_state_layer_color = 0x7f0c00e5
com.iapp.leochen.apkinjector:macro/m3_comp_radio_button_unselected_pressed_icon_color = 0x7f0c00e4
com.iapp.leochen.apkinjector:style/Base.V14.ThemeOverlay.MaterialComponents.BottomSheetDialog = 0x7f11009d
com.iapp.leochen.apkinjector:macro/m3_comp_radio_button_unselected_icon_color = 0x7f0c00e3
com.iapp.leochen.apkinjector:macro/m3_comp_radio_button_unselected_hover_state_layer_color = 0x7f0c00e2
com.iapp.leochen.apkinjector:macro/m3_comp_radio_button_unselected_hover_icon_color = 0x7f0c00e1
com.iapp.leochen.apkinjector:styleable/MaterialRadioButton = 0x7f120058
com.iapp.leochen.apkinjector:macro/m3_comp_radio_button_selected_pressed_state_layer_color = 0x7f0c00de
com.iapp.leochen.apkinjector:style/Theme.Material3.DayNight.Dialog = 0x7f110233
com.iapp.leochen.apkinjector:macro/m3_comp_radio_button_selected_icon_color = 0x7f0c00dc
com.iapp.leochen.apkinjector:macro/m3_comp_radio_button_selected_hover_icon_color = 0x7f0c00da
com.iapp.leochen.apkinjector:macro/m3_comp_radio_button_selected_focus_icon_color = 0x7f0c00d8
com.iapp.leochen.apkinjector:macro/m3_comp_radio_button_disabled_unselected_icon_color = 0x7f0c00d7
com.iapp.leochen.apkinjector:macro/m3_comp_progress_indicator_track_color = 0x7f0c00d5
com.iapp.leochen.apkinjector:macro/m3_comp_progress_indicator_active_indicator_color = 0x7f0c00d4
com.iapp.leochen.apkinjector:macro/m3_comp_primary_navigation_tab_with_label_text_label_text_type = 0x7f0c00d3
com.iapp.leochen.apkinjector:macro/m3_comp_primary_navigation_tab_with_label_text_inactive_label_text_color = 0x7f0c00d2
com.iapp.leochen.apkinjector:macro/m3_comp_primary_navigation_tab_with_label_text_active_label_text_color = 0x7f0c00d1
com.iapp.leochen.apkinjector:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Header.Text.Day = 0x7f1102d9
com.iapp.leochen.apkinjector:macro/m3_comp_primary_navigation_tab_inactive_pressed_state_layer_color = 0x7f0c00ce
com.iapp.leochen.apkinjector:macro/m3_comp_primary_navigation_tab_inactive_hover_state_layer_color = 0x7f0c00cd
com.iapp.leochen.apkinjector:style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Year = 0x7f110192
com.iapp.leochen.apkinjector:macro/m3_comp_primary_navigation_tab_inactive_focus_state_layer_color = 0x7f0c00cc
com.iapp.leochen.apkinjector:macro/m3_comp_primary_navigation_tab_active_hover_state_layer_color = 0x7f0c00c8
com.iapp.leochen.apkinjector:macro/m3_comp_plain_tooltip_supporting_text_type = 0x7f0c00c6
com.iapp.leochen.apkinjector:macro/m3_comp_outlined_text_field_input_text_type = 0x7f0c00c1
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.ActionBar.PrimarySurface = 0x7f1103ec
com.iapp.leochen.apkinjector:macro/m3_comp_outlined_text_field_hover_supporting_text_color = 0x7f0c00bf
com.iapp.leochen.apkinjector:style/Widget.Design.BottomNavigationView = 0x7f110334
com.iapp.leochen.apkinjector:macro/m3_comp_outlined_text_field_focus_supporting_text_color = 0x7f0c00bc
com.iapp.leochen.apkinjector:macro/m3_comp_outlined_text_field_focus_label_text_color = 0x7f0c00ba
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.BottomAppBar = 0x7f1103f8
com.iapp.leochen.apkinjector:macro/m3_comp_outlined_text_field_focus_input_text_color = 0x7f0c00b9
com.iapp.leochen.apkinjector:macro/m3_comp_outlined_text_field_disabled_input_text_color = 0x7f0c00b2
com.iapp.leochen.apkinjector:macro/m3_comp_outlined_card_outline_color = 0x7f0c00ae
com.iapp.leochen.apkinjector:macro/m3_comp_outlined_card_hover_outline_color = 0x7f0c00ad
com.iapp.leochen.apkinjector:macro/m3_comp_outlined_card_dragged_outline_color = 0x7f0c00ab
com.iapp.leochen.apkinjector:style/Base.Widget.Material3.MaterialCalendar.NavigationButton = 0x7f11010d
com.iapp.leochen.apkinjector:macro/m3_comp_outlined_button_outline_color = 0x7f0c00a6
com.iapp.leochen.apkinjector:macro/m3_comp_outlined_button_hover_outline_color = 0x7f0c00a5
com.iapp.leochen.apkinjector:macro/m3_comp_outlined_button_focus_outline_color = 0x7f0c00a4
com.iapp.leochen.apkinjector:macro/m3_comp_outlined_autocomplete_text_field_input_text_type = 0x7f0c00a2
com.iapp.leochen.apkinjector:macro/m3_comp_outlined_autocomplete_text_field_caret_color = 0x7f0c00a1
com.iapp.leochen.apkinjector:macro/m3_comp_outlined_autocomplete_menu_container_color = 0x7f0c00a0
com.iapp.leochen.apkinjector:macro/m3_comp_search_view_docked_container_shape = 0x7f0c00f3
com.iapp.leochen.apkinjector:macro/m3_comp_navigation_rail_inactive_pressed_state_layer_color = 0x7f0c009e
com.iapp.leochen.apkinjector:macro/m3_comp_navigation_rail_inactive_icon_color = 0x7f0c009c
com.iapp.leochen.apkinjector:macro/m3_comp_navigation_rail_inactive_hover_state_layer_color = 0x7f0c009b
com.iapp.leochen.apkinjector:macro/m3_comp_navigation_rail_inactive_focus_state_layer_color = 0x7f0c009a
com.iapp.leochen.apkinjector:style/Theme.MaterialComponents.DayNight.DialogWhenLarge = 0x7f110258
com.iapp.leochen.apkinjector:macro/m3_comp_navigation_rail_active_pressed_state_layer_color = 0x7f0c0098
com.iapp.leochen.apkinjector:macro/m3_comp_navigation_rail_active_label_text_color = 0x7f0c0097
com.iapp.leochen.apkinjector:macro/m3_comp_navigation_rail_active_icon_color = 0x7f0c0095
com.iapp.leochen.apkinjector:macro/m3_comp_navigation_rail_active_hover_state_layer_color = 0x7f0c0094
com.iapp.leochen.apkinjector:macro/m3_comp_navigation_rail_active_focus_state_layer_color = 0x7f0c0093
com.iapp.leochen.apkinjector:macro/m3_comp_navigation_drawer_modal_container_color = 0x7f0c0092
com.iapp.leochen.apkinjector:macro/m3_comp_navigation_drawer_inactive_pressed_state_layer_color = 0x7f0c0090
com.iapp.leochen.apkinjector:macro/m3_comp_navigation_drawer_inactive_pressed_label_text_color = 0x7f0c008f
com.iapp.leochen.apkinjector:string/m3_sys_motion_easing_standard = 0x7f100044
com.iapp.leochen.apkinjector:macro/m3_comp_navigation_drawer_inactive_pressed_icon_color = 0x7f0c008e
com.iapp.leochen.apkinjector:macro/m3_comp_navigation_drawer_inactive_icon_color = 0x7f0c008c
com.iapp.leochen.apkinjector:macro/m3_comp_navigation_drawer_inactive_hover_state_layer_color = 0x7f0c008b
com.iapp.leochen.apkinjector:macro/m3_comp_navigation_drawer_inactive_hover_label_text_color = 0x7f0c008a
com.iapp.leochen.apkinjector:macro/m3_comp_navigation_drawer_inactive_hover_icon_color = 0x7f0c0089
com.iapp.leochen.apkinjector:macro/m3_comp_navigation_drawer_inactive_focus_state_layer_color = 0x7f0c0088
com.iapp.leochen.apkinjector:macro/m3_comp_navigation_drawer_inactive_focus_label_text_color = 0x7f0c0087
com.iapp.leochen.apkinjector:macro/m3_comp_navigation_drawer_headline_type = 0x7f0c0085
com.iapp.leochen.apkinjector:string/material_timepicker_text_input_mode_description = 0x7f10005c
com.iapp.leochen.apkinjector:macro/m3_comp_navigation_drawer_headline_color = 0x7f0c0084
com.iapp.leochen.apkinjector:macro/m3_comp_navigation_drawer_active_pressed_icon_color = 0x7f0c0081
com.iapp.leochen.apkinjector:macro/m3_comp_navigation_drawer_active_label_text_color = 0x7f0c0080
com.iapp.leochen.apkinjector:macro/m3_comp_navigation_drawer_active_focus_label_text_color = 0x7f0c0079
com.iapp.leochen.apkinjector:macro/m3_comp_navigation_bar_label_text_type = 0x7f0c0077
com.iapp.leochen.apkinjector:style/ShapeAppearanceOverlay.MaterialAlertDialog.Material3 = 0x7f11018b
com.iapp.leochen.apkinjector:macro/m3_comp_navigation_bar_inactive_pressed_icon_color = 0x7f0c0074
com.iapp.leochen.apkinjector:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Text = 0x7f110152
com.iapp.leochen.apkinjector:macro/m3_comp_navigation_bar_inactive_icon_color = 0x7f0c0072
com.iapp.leochen.apkinjector:macro/m3_comp_navigation_bar_inactive_hover_label_text_color = 0x7f0c0070
com.iapp.leochen.apkinjector:macro/m3_comp_navigation_bar_inactive_hover_icon_color = 0x7f0c006f
com.iapp.leochen.apkinjector:macro/m3_comp_navigation_bar_inactive_focus_state_layer_color = 0x7f0c006e
com.iapp.leochen.apkinjector:macro/m3_comp_navigation_bar_inactive_focus_label_text_color = 0x7f0c006d
com.iapp.leochen.apkinjector:macro/m3_comp_navigation_bar_container_color = 0x7f0c006b
com.iapp.leochen.apkinjector:style/Base.Widget.AppCompat.Light.PopupMenu.Overflow = 0x7f1100e5
com.iapp.leochen.apkinjector:string/bottomsheet_action_expand_halfway = 0x7f100021
com.iapp.leochen.apkinjector:macro/m3_comp_navigation_bar_active_pressed_state_layer_color = 0x7f0c006a
com.iapp.leochen.apkinjector:macro/m3_comp_navigation_bar_active_pressed_label_text_color = 0x7f0c0069
com.iapp.leochen.apkinjector:macro/m3_comp_navigation_bar_active_label_text_color = 0x7f0c0067
com.iapp.leochen.apkinjector:macro/m3_comp_navigation_drawer_inactive_label_text_color = 0x7f0c008d
com.iapp.leochen.apkinjector:macro/m3_comp_navigation_bar_active_icon_color = 0x7f0c0065
com.iapp.leochen.apkinjector:macro/m3_comp_navigation_bar_active_focus_label_text_color = 0x7f0c0060
com.iapp.leochen.apkinjector:macro/m3_comp_navigation_bar_active_focus_icon_color = 0x7f0c005f
com.iapp.leochen.apkinjector:macro/m3_comp_input_chip_label_text_type = 0x7f0c005c
com.iapp.leochen.apkinjector:macro/m3_comp_input_chip_container_shape = 0x7f0c005b
com.iapp.leochen.apkinjector:macro/m3_comp_filter_chip_container_shape = 0x7f0c0057
com.iapp.leochen.apkinjector:macro/m3_comp_filled_tonal_icon_button_toggle_unselected_icon_color = 0x7f0c0056
com.iapp.leochen.apkinjector:macro/m3_comp_filled_tonal_icon_button_toggle_selected_icon_color = 0x7f0c0055
com.iapp.leochen.apkinjector:macro/m3_comp_filled_text_field_supporting_text_type = 0x7f0c0051
com.iapp.leochen.apkinjector:macro/m3_comp_primary_navigation_tab_active_pressed_state_layer_color = 0x7f0c00ca
com.iapp.leochen.apkinjector:macro/m3_comp_filled_text_field_error_supporting_text_color = 0x7f0c004e
com.iapp.leochen.apkinjector:string/m3_ref_typeface_plain_medium = 0x7f10003a
com.iapp.leochen.apkinjector:macro/m3_comp_filled_text_field_error_active_indicator_color = 0x7f0c004d
com.iapp.leochen.apkinjector:macro/m3_comp_filled_text_field_container_shape = 0x7f0c004c
com.iapp.leochen.apkinjector:macro/m3_comp_filled_icon_button_container_color = 0x7f0c0048
com.iapp.leochen.apkinjector:macro/m3_comp_filled_card_container_shape = 0x7f0c0047
com.iapp.leochen.apkinjector:macro/m3_comp_filled_card_container_color = 0x7f0c0046
com.iapp.leochen.apkinjector:macro/m3_comp_filled_button_label_text_type = 0x7f0c0045
com.iapp.leochen.apkinjector:macro/m3_comp_navigation_drawer_active_focus_state_layer_color = 0x7f0c007a
com.iapp.leochen.apkinjector:macro/m3_comp_filled_button_container_color = 0x7f0c0043
com.iapp.leochen.apkinjector:macro/m3_comp_filled_autocomplete_text_field_input_text_type = 0x7f0c0042
com.iapp.leochen.apkinjector:macro/m3_comp_fab_tertiary_icon_color = 0x7f0c0040
com.iapp.leochen.apkinjector:macro/m3_comp_fab_tertiary_container_color = 0x7f0c003f
com.iapp.leochen.apkinjector:macro/m3_comp_fab_surface_icon_color = 0x7f0c003e
com.iapp.leochen.apkinjector:macro/m3_comp_fab_secondary_icon_color = 0x7f0c003c
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox = 0x7f110455
com.iapp.leochen.apkinjector:macro/m3_comp_snackbar_supporting_text_type = 0x7f0c0116
com.iapp.leochen.apkinjector:macro/m3_comp_fab_primary_small_container_shape = 0x7f0c003a
com.iapp.leochen.apkinjector:macro/m3_comp_fab_primary_large_container_shape = 0x7f0c0039
com.iapp.leochen.apkinjector:macro/m3_comp_fab_primary_container_shape = 0x7f0c0037
com.iapp.leochen.apkinjector:style/Base.Widget.AppCompat.SearchView = 0x7f1100f3
com.iapp.leochen.apkinjector:macro/m3_comp_extended_fab_secondary_icon_color = 0x7f0c0031
com.iapp.leochen.apkinjector:macro/m3_comp_extended_fab_primary_container_color = 0x7f0c002c
com.iapp.leochen.apkinjector:macro/m3_comp_elevated_card_container_shape = 0x7f0c002b
com.iapp.leochen.apkinjector:macro/m3_comp_elevated_card_container_color = 0x7f0c002a
com.iapp.leochen.apkinjector:macro/m3_comp_elevated_button_container_color = 0x7f0c0029
com.iapp.leochen.apkinjector:string/abc_action_bar_up_description = 0x7f100001
com.iapp.leochen.apkinjector:macro/m3_comp_divider_color = 0x7f0c0028
com.iapp.leochen.apkinjector:macro/m3_comp_dialog_supporting_text_color = 0x7f0c0026
com.iapp.leochen.apkinjector:macro/m3_comp_date_picker_modal_year_selection_year_unselected_label_text_color = 0x7f0c0021
com.iapp.leochen.apkinjector:macro/m3_comp_date_picker_modal_year_selection_year_selected_container_color = 0x7f0c001f
com.iapp.leochen.apkinjector:macro/m3_comp_date_picker_modal_weekdays_label_text_type = 0x7f0c001e
com.iapp.leochen.apkinjector:macro/m3_comp_date_picker_modal_weekdays_label_text_color = 0x7f0c001d
com.iapp.leochen.apkinjector:macro/m3_comp_date_picker_modal_range_selection_month_subhead_type = 0x7f0c001c
com.iapp.leochen.apkinjector:style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle = 0x7f1101b2
com.iapp.leochen.apkinjector:macro/m3_comp_date_picker_modal_header_supporting_text_type = 0x7f0c0018
com.iapp.leochen.apkinjector:macro/m3_comp_date_picker_modal_date_unselected_label_text_color = 0x7f0c0014
com.iapp.leochen.apkinjector:macro/m3_comp_date_picker_modal_date_today_label_text_color = 0x7f0c0013
com.iapp.leochen.apkinjector:macro/m3_comp_date_picker_modal_date_today_container_outline_color = 0x7f0c0012
com.iapp.leochen.apkinjector:macro/m3_comp_date_picker_modal_date_selected_container_color = 0x7f0c0010
com.iapp.leochen.apkinjector:styleable/Tooltip = 0x7f120091
com.iapp.leochen.apkinjector:style/Base.Widget.AppCompat.ListView.DropDown = 0x7f1100e9
com.iapp.leochen.apkinjector:macro/m3_comp_date_picker_modal_container_shape = 0x7f0c000e
com.iapp.leochen.apkinjector:macro/m3_comp_checkbox_unselected_outline_color = 0x7f0c000c
com.iapp.leochen.apkinjector:macro/m3_comp_checkbox_selected_error_container_color = 0x7f0c0009
com.iapp.leochen.apkinjector:styleable/SwitchMaterial = 0x7f120088
com.iapp.leochen.apkinjector:style/Widget.Material3.MaterialCalendar.MonthTextView = 0x7f1103ad
com.iapp.leochen.apkinjector:macro/m3_comp_checkbox_selected_disabled_icon_color = 0x7f0c0008
com.iapp.leochen.apkinjector:macro/m3_comp_checkbox_selected_disabled_container_color = 0x7f0c0007
com.iapp.leochen.apkinjector:macro/m3_comp_badge_large_label_text_type = 0x7f0c0004
com.iapp.leochen.apkinjector:macro/m3_comp_navigation_bar_inactive_hover_state_layer_color = 0x7f0c0071
com.iapp.leochen.apkinjector:macro/m3_comp_extended_fab_surface_icon_color = 0x7f0c0033
com.iapp.leochen.apkinjector:macro/m3_comp_badge_large_label_text_color = 0x7f0c0003
com.iapp.leochen.apkinjector:layout/support_simple_spinner_dropdown_item = 0x7f0b0070
com.iapp.leochen.apkinjector:layout/select_dialog_multichoice_material = 0x7f0b006e
com.iapp.leochen.apkinjector:style/Base.TextAppearance.AppCompat.Caption = 0x7f11001a
com.iapp.leochen.apkinjector:layout/notification_template_part_time = 0x7f0b006c
com.iapp.leochen.apkinjector:layout/notification_template_part_chronometer = 0x7f0b006b
com.iapp.leochen.apkinjector:layout/notification_template_custom_big = 0x7f0b0069
com.iapp.leochen.apkinjector:macro/m3_comp_top_app_bar_small_on_scroll_container_color = 0x7f0c0173
com.iapp.leochen.apkinjector:layout/notification_action_tombstone = 0x7f0b0068
com.iapp.leochen.apkinjector:style/Widget.Design.Snackbar = 0x7f11033a
com.iapp.leochen.apkinjector:layout/notification_action = 0x7f0b0067
com.iapp.leochen.apkinjector:layout/mtrl_search_view = 0x7f0b0066
com.iapp.leochen.apkinjector:layout/mtrl_picker_text_input_date_range = 0x7f0b0064
com.iapp.leochen.apkinjector:layout/mtrl_picker_text_input_date = 0x7f0b0063
com.iapp.leochen.apkinjector:layout/mtrl_picker_header_fullscreen = 0x7f0b005f
com.iapp.leochen.apkinjector:layout/mtrl_picker_header_dialog = 0x7f0b005e
com.iapp.leochen.apkinjector:layout/mtrl_picker_fullscreen = 0x7f0b005d
com.iapp.leochen.apkinjector:layout/mtrl_picker_actions = 0x7f0b005b
com.iapp.leochen.apkinjector:style/ShapeAppearance.M3.Sys.Shape.Corner.Full = 0x7f11016a
com.iapp.leochen.apkinjector:layout/mtrl_navigation_rail_item = 0x7f0b005a
com.iapp.leochen.apkinjector:layout/mtrl_layout_snackbar_include = 0x7f0b0059
com.iapp.leochen.apkinjector:layout/mtrl_calendar_month_navigation = 0x7f0b0054
com.iapp.leochen.apkinjector:layout/mtrl_calendar_month_labeled = 0x7f0b0053
com.iapp.leochen.apkinjector:style/TextAppearance.AppCompat.Widget.ActionMode.Title.Inverse = 0x7f1101b9
com.iapp.leochen.apkinjector:layout/mtrl_calendar_horizontal = 0x7f0b0051
com.iapp.leochen.apkinjector:style/Base.Widget.MaterialComponents.Snackbar = 0x7f11011c
com.iapp.leochen.apkinjector:layout/mtrl_calendar_day_of_week = 0x7f0b004f
com.iapp.leochen.apkinjector:layout/mtrl_calendar_day = 0x7f0b004e
com.iapp.leochen.apkinjector:macro/m3_comp_badge_color = 0x7f0c0002
com.iapp.leochen.apkinjector:layout/mtrl_alert_select_dialog_multichoice = 0x7f0b004b
com.iapp.leochen.apkinjector:macro/m3_comp_navigation_bar_inactive_label_text_color = 0x7f0c0073
com.iapp.leochen.apkinjector:layout/mtrl_alert_select_dialog_item = 0x7f0b004a
com.iapp.leochen.apkinjector:style/TextAppearance.AppCompat.Display2 = 0x7f11019a
com.iapp.leochen.apkinjector:layout/mtrl_alert_dialog = 0x7f0b0047
com.iapp.leochen.apkinjector:style/Base.V14.Theme.MaterialComponents = 0x7f110092
com.iapp.leochen.apkinjector:layout/material_timepicker_dialog = 0x7f0b0045
com.iapp.leochen.apkinjector:styleable/AlertDialog = 0x7f120006
com.iapp.leochen.apkinjector:macro/m3_comp_outlined_text_field_container_shape = 0x7f0c00b1
com.iapp.leochen.apkinjector:layout/material_timepicker = 0x7f0b0044
com.iapp.leochen.apkinjector:layout/material_time_input = 0x7f0b0043
com.iapp.leochen.apkinjector:layout/material_textinput_timepicker = 0x7f0b0041
com.iapp.leochen.apkinjector:layout/material_radial_view_group = 0x7f0b0040
com.iapp.leochen.apkinjector:macro/m3_comp_time_picker_time_selector_selected_pressed_state_layer_color = 0x7f0c0164
com.iapp.leochen.apkinjector:layout/material_clock_period_toggle_land = 0x7f0b003d
com.iapp.leochen.apkinjector:layout/material_clock_period_toggle = 0x7f0b003c
com.iapp.leochen.apkinjector:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Spinner = 0x7f1102da
com.iapp.leochen.apkinjector:string/mtrl_switch_track_path = 0x7f100098
com.iapp.leochen.apkinjector:layout/material_clock_display_divider = 0x7f0b003b
com.iapp.leochen.apkinjector:layout/material_clock_display = 0x7f0b003a
com.iapp.leochen.apkinjector:layout/material_chip_input_combo = 0x7f0b0039
com.iapp.leochen.apkinjector:layout/m3_auto_complete_simple_item = 0x7f0b0037
com.iapp.leochen.apkinjector:layout/m3_alert_dialog_actions = 0x7f0b0035
com.iapp.leochen.apkinjector:style/TextAppearance.AppCompat.Light.SearchResult.Title = 0x7f1101a2
com.iapp.leochen.apkinjector:layout/layout_permission_request = 0x7f0b0033
com.iapp.leochen.apkinjector:layout/fragment_home = 0x7f0b002f
com.iapp.leochen.apkinjector:layout/fragment_about = 0x7f0b002e
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.MaterialCalendar.YearNavigationButton = 0x7f110438
com.iapp.leochen.apkinjector:layout/dialog_path_navigation = 0x7f0b002d
com.iapp.leochen.apkinjector:layout/design_text_input_start_icon = 0x7f0b002c
com.iapp.leochen.apkinjector:layout/design_text_input_end_icon = 0x7f0b002b
com.iapp.leochen.apkinjector:style/Widget.Material3.Button.TonalButton = 0x7f110361
com.iapp.leochen.apkinjector:string/mtrl_picker_range_header_only_start_selected = 0x7f100080
com.iapp.leochen.apkinjector:layout/design_navigation_menu_item = 0x7f0b002a
com.iapp.leochen.apkinjector:layout/design_navigation_item_separator = 0x7f0b0027
com.iapp.leochen.apkinjector:layout/design_menu_item_action_area = 0x7f0b0024
com.iapp.leochen.apkinjector:layout/design_layout_tab_text = 0x7f0b0023
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.TextInputEditText.FilledBox = 0x7f11044d
com.iapp.leochen.apkinjector:macro/m3_comp_outlined_text_field_disabled_outline_color = 0x7f0c00b4
com.iapp.leochen.apkinjector:layout/design_layout_tab_icon = 0x7f0b0022
com.iapp.leochen.apkinjector:layout/design_layout_snackbar = 0x7f0b0020
com.iapp.leochen.apkinjector:layout/design_bottom_sheet_dialog = 0x7f0b001f
com.iapp.leochen.apkinjector:layout/design_bottom_navigation_item = 0x7f0b001e
com.iapp.leochen.apkinjector:layout/custom_dialog = 0x7f0b001d
com.iapp.leochen.apkinjector:layout/abc_select_dialog_material = 0x7f0b001a
com.iapp.leochen.apkinjector:style/ShapeAppearance.Material3.Corner.Small = 0x7f110175
com.iapp.leochen.apkinjector:layout/abc_search_view = 0x7f0b0019
com.iapp.leochen.apkinjector:macro/m3_comp_extended_fab_secondary_container_color = 0x7f0c0030
com.iapp.leochen.apkinjector:layout/abc_screen_toolbar = 0x7f0b0017
com.iapp.leochen.apkinjector:layout/abc_screen_simple_overlay_action_mode = 0x7f0b0016
com.iapp.leochen.apkinjector:layout/notification_template_icon_group = 0x7f0b006a
com.iapp.leochen.apkinjector:layout/abc_screen_simple = 0x7f0b0015
com.iapp.leochen.apkinjector:layout/abc_popup_menu_item_layout = 0x7f0b0013
com.iapp.leochen.apkinjector:layout/abc_list_menu_item_radio = 0x7f0b0011
com.iapp.leochen.apkinjector:layout/abc_list_menu_item_layout = 0x7f0b0010
com.iapp.leochen.apkinjector:style/Animation.Material3.SideSheetDialog.Left = 0x7f110008
com.iapp.leochen.apkinjector:layout/abc_list_menu_item_icon = 0x7f0b000f
com.iapp.leochen.apkinjector:layout/abc_expanded_menu_layout = 0x7f0b000d
com.iapp.leochen.apkinjector:layout/abc_dialog_title_material = 0x7f0b000c
com.iapp.leochen.apkinjector:layout/abc_alert_dialog_button_bar_material = 0x7f0b0008
com.iapp.leochen.apkinjector:layout/abc_action_mode_close_item_material = 0x7f0b0005
com.iapp.leochen.apkinjector:layout/abc_action_mode_bar = 0x7f0b0004
com.iapp.leochen.apkinjector:layout/abc_action_menu_layout = 0x7f0b0003
com.iapp.leochen.apkinjector:interpolator/mtrl_linear = 0x7f0a0010
com.iapp.leochen.apkinjector:interpolator/mtrl_fast_out_slow_in = 0x7f0a000f
com.iapp.leochen.apkinjector:interpolator/m3_sys_motion_easing_standard_accelerate = 0x7f0a000c
com.iapp.leochen.apkinjector:interpolator/m3_sys_motion_easing_emphasized_decelerate = 0x7f0a0009
com.iapp.leochen.apkinjector:interpolator/m3_sys_motion_easing_emphasized_accelerate = 0x7f0a0008
com.iapp.leochen.apkinjector:interpolator/m3_sys_motion_easing_emphasized = 0x7f0a0007
com.iapp.leochen.apkinjector:interpolator/fast_out_slow_in = 0x7f0a0006
com.iapp.leochen.apkinjector:interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_0 = 0x7f0a0002
com.iapp.leochen.apkinjector:interpolator/btn_checkbox_checked_mtrl_animation_interpolator_1 = 0x7f0a0001
com.iapp.leochen.apkinjector:style/TextAppearance.MaterialComponents.Subtitle2 = 0x7f110206
com.iapp.leochen.apkinjector:integer/mtrl_view_visible = 0x7f090041
com.iapp.leochen.apkinjector:macro/m3_comp_search_bar_hover_state_layer_color = 0x7f0c00e7
com.iapp.leochen.apkinjector:integer/mtrl_tab_indicator_anim_duration_ms = 0x7f09003e
com.iapp.leochen.apkinjector:style/Base.Widget.AppCompat.ActionBar.TabView = 0x7f1100c7
com.iapp.leochen.apkinjector:integer/mtrl_switch_track_viewport_width = 0x7f09003d
com.iapp.leochen.apkinjector:integer/mtrl_switch_track_viewport_height = 0x7f09003c
com.iapp.leochen.apkinjector:integer/mtrl_switch_thumb_viewport_size = 0x7f09003b
com.iapp.leochen.apkinjector:integer/mtrl_switch_thumb_viewport_center_coordinate = 0x7f09003a
com.iapp.leochen.apkinjector:integer/mtrl_switch_thumb_post_morphing_duration = 0x7f090037
com.iapp.leochen.apkinjector:integer/mtrl_chip_anim_duration = 0x7f090035
com.iapp.leochen.apkinjector:style/Base.Widget.Material3.TabLayout.OnSurface = 0x7f110110
com.iapp.leochen.apkinjector:macro/m3_comp_snackbar_supporting_text_color = 0x7f0c0115
com.iapp.leochen.apkinjector:integer/mtrl_card_anim_duration_ms = 0x7f090034
com.iapp.leochen.apkinjector:integer/mtrl_calendar_selection_text_lines = 0x7f090031
com.iapp.leochen.apkinjector:integer/mtrl_calendar_header_orientation = 0x7f090030
com.iapp.leochen.apkinjector:integer/mtrl_btn_anim_duration_ms = 0x7f09002f
com.iapp.leochen.apkinjector:integer/mtrl_btn_anim_delay_ms = 0x7f09002e
com.iapp.leochen.apkinjector:style/Widget.Material3.SideSheet.Modal = 0x7f1103ce
com.iapp.leochen.apkinjector:integer/material_motion_path = 0x7f09002c
com.iapp.leochen.apkinjector:integer/material_motion_duration_short_2 = 0x7f09002b
com.iapp.leochen.apkinjector:integer/material_motion_duration_medium_1 = 0x7f090028
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.MaterialCalendar.Year = 0x7f110435
com.iapp.leochen.apkinjector:integer/material_motion_duration_long_2 = 0x7f090027
com.iapp.leochen.apkinjector:integer/material_motion_duration_long_1 = 0x7f090026
com.iapp.leochen.apkinjector:integer/m3_sys_shape_corner_large_corner_family = 0x7f090023
com.iapp.leochen.apkinjector:style/TextAppearance.Material3.DisplayLarge = 0x7f1101e8
com.iapp.leochen.apkinjector:integer/m3_sys_shape_corner_extra_large_corner_family = 0x7f090020
com.iapp.leochen.apkinjector:integer/m3_sys_motion_path = 0x7f09001f
com.iapp.leochen.apkinjector:integer/m3_sys_motion_duration_short4 = 0x7f09001e
com.iapp.leochen.apkinjector:integer/m3_sys_motion_duration_short2 = 0x7f09001c
com.iapp.leochen.apkinjector:integer/m3_sys_motion_duration_short1 = 0x7f09001b
com.iapp.leochen.apkinjector:integer/m3_sys_motion_duration_medium4 = 0x7f09001a
com.iapp.leochen.apkinjector:integer/m3_sys_motion_duration_medium3 = 0x7f090019
com.iapp.leochen.apkinjector:integer/m3_sys_motion_duration_medium1 = 0x7f090017
com.iapp.leochen.apkinjector:style/ThemeOverlay.Material3.TextInputEditText = 0x7f1102b9
com.iapp.leochen.apkinjector:integer/m3_sys_motion_duration_long4 = 0x7f090016
com.iapp.leochen.apkinjector:integer/m3_sys_motion_duration_long3 = 0x7f090015
com.iapp.leochen.apkinjector:integer/mtrl_calendar_year_selector_span = 0x7f090032
com.iapp.leochen.apkinjector:integer/m3_sys_motion_duration_long1 = 0x7f090013
com.iapp.leochen.apkinjector:style/Widget.Material3.PopupMenu.ContextMenu = 0x7f1103c2
com.iapp.leochen.apkinjector:integer/m3_sys_motion_duration_extra_long4 = 0x7f090012
com.iapp.leochen.apkinjector:macro/m3_comp_top_app_bar_medium_headline_type = 0x7f0c016e
com.iapp.leochen.apkinjector:integer/m3_sys_motion_duration_extra_long3 = 0x7f090011
com.iapp.leochen.apkinjector:integer/m3_sys_motion_duration_extra_long1 = 0x7f09000f
com.iapp.leochen.apkinjector:integer/m3_card_anim_delay_ms = 0x7f09000c
com.iapp.leochen.apkinjector:integer/m3_btn_anim_duration_ms = 0x7f09000b
com.iapp.leochen.apkinjector:styleable/Spinner = 0x7f120081
com.iapp.leochen.apkinjector:integer/m3_badge_max_number = 0x7f090009
com.iapp.leochen.apkinjector:integer/design_snackbar_text_max_lines = 0x7f090006
com.iapp.leochen.apkinjector:integer/config_tooltipAnimTime = 0x7f090005
com.iapp.leochen.apkinjector:style/Widget.Material3.CardView.Outlined = 0x7f110366
com.iapp.leochen.apkinjector:integer/cancel_button_image_alpha = 0x7f090004
com.iapp.leochen.apkinjector:integer/app_bar_elevation_anim_duration = 0x7f090002
com.iapp.leochen.apkinjector:styleable/ActionMenuView = 0x7f120003
com.iapp.leochen.apkinjector:integer/abc_config_activityShortDur = 0x7f090001
com.iapp.leochen.apkinjector:integer/abc_config_activityDefaultDur = 0x7f090000
com.iapp.leochen.apkinjector:id/x_right = 0x7f080217
com.iapp.leochen.apkinjector:macro/m3_comp_secondary_navigation_tab_with_icon_active_icon_color = 0x7f0c0102
com.iapp.leochen.apkinjector:id/wrap = 0x7f080213
com.iapp.leochen.apkinjector:id/withinBounds = 0x7f080212
com.iapp.leochen.apkinjector:macro/m3_comp_fab_surface_container_color = 0x7f0c003d
com.iapp.leochen.apkinjector:integer/show_password_duration = 0x7f090042
com.iapp.leochen.apkinjector:id/west = 0x7f08020f
com.iapp.leochen.apkinjector:id/visible_removing_fragment_view_tag = 0x7f08020e
com.iapp.leochen.apkinjector:id/visible = 0x7f08020d
com.iapp.leochen.apkinjector:id/view_tree_view_model_store_owner = 0x7f08020c
com.iapp.leochen.apkinjector:id/view_tree_lifecycle_owner = 0x7f080209
com.iapp.leochen.apkinjector:id/view_transition = 0x7f080208
com.iapp.leochen.apkinjector:id/view_offset_helper = 0x7f080207
com.iapp.leochen.apkinjector:id/viewPager = 0x7f080206
com.iapp.leochen.apkinjector:id/vertical_only = 0x7f080205
com.iapp.leochen.apkinjector:style/Widget.AppCompat.ImageButton = 0x7f110301
com.iapp.leochen.apkinjector:id/vertical = 0x7f080204
com.iapp.leochen.apkinjector:id/unchecked = 0x7f0801fe
com.iapp.leochen.apkinjector:macro/m3_comp_time_picker_period_selector_unselected_label_text_color = 0x7f0c015c
com.iapp.leochen.apkinjector:id/triangle = 0x7f0801fd
com.iapp.leochen.apkinjector:id/transition_pause_alpha = 0x7f0801f9
com.iapp.leochen.apkinjector:id/transition_clip = 0x7f0801f5
com.iapp.leochen.apkinjector:id/transitionToStart = 0x7f0801f4
com.iapp.leochen.apkinjector:id/transitionToEnd = 0x7f0801f3
com.iapp.leochen.apkinjector:id/topPanel = 0x7f0801f0
com.iapp.leochen.apkinjector:id/top = 0x7f0801ef
com.iapp.leochen.apkinjector:id/toolbar = 0x7f0801ee
com.iapp.leochen.apkinjector:id/time = 0x7f0801e9
com.iapp.leochen.apkinjector:style/TextAppearance.AppCompat.Widget.PopupMenu.Small = 0x7f1101c1
com.iapp.leochen.apkinjector:id/textinput_suffix_text = 0x7f0801e8
com.iapp.leochen.apkinjector:id/textinput_placeholder = 0x7f0801e6
com.iapp.leochen.apkinjector:id/text_input_start_icon = 0x7f0801e2
com.iapp.leochen.apkinjector:style/TextAppearance.AppCompat.Title = 0x7f1101ae
com.iapp.leochen.apkinjector:id/textTop = 0x7f0801df
com.iapp.leochen.apkinjector:id/textSpacerNoTitle = 0x7f0801dd
com.iapp.leochen.apkinjector:id/textSpacerNoButtons = 0x7f0801dc
com.iapp.leochen.apkinjector:id/textEnd = 0x7f0801db
com.iapp.leochen.apkinjector:id/text2 = 0x7f0801da
com.iapp.leochen.apkinjector:id/text = 0x7f0801d9
com.iapp.leochen.apkinjector:id/targetPathInputLayout = 0x7f0801d8
com.iapp.leochen.apkinjector:id/targetPathEditText = 0x7f0801d7
com.iapp.leochen.apkinjector:id/tag_window_insets_animation_callback = 0x7f0801d6
com.iapp.leochen.apkinjector:id/tag_unhandled_key_listeners = 0x7f0801d5
com.iapp.leochen.apkinjector:id/tag_unhandled_key_event_manager = 0x7f0801d4
com.iapp.leochen.apkinjector:style/ShapeAppearance.Material3.Corner.None = 0x7f110174
com.iapp.leochen.apkinjector:id/tag_on_receive_content_mime_types = 0x7f0801d0
com.iapp.leochen.apkinjector:id/tag_on_receive_content_listener = 0x7f0801cf
com.iapp.leochen.apkinjector:id/tag_accessibility_pane_title = 0x7f0801cd
com.iapp.leochen.apkinjector:id/tag_accessibility_actions = 0x7f0801ca
com.iapp.leochen.apkinjector:style/Base.Widget.AppCompat.ImageButton = 0x7f1100dd
com.iapp.leochen.apkinjector:integer/mtrl_switch_thumb_pre_morphing_duration = 0x7f090038
com.iapp.leochen.apkinjector:id/swipeRefreshLayout = 0x7f0801c8
com.iapp.leochen.apkinjector:style/Widget.AppCompat.CompoundButton.RadioButton = 0x7f1102fc
com.iapp.leochen.apkinjector:id/supportScrollUp = 0x7f0801c7
com.iapp.leochen.apkinjector:id/submit_area = 0x7f0801c6
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense = 0x7f110456
com.iapp.leochen.apkinjector:macro/m3_comp_extended_fab_surface_container_color = 0x7f0c0032
com.iapp.leochen.apkinjector:id/submenuarrow = 0x7f0801c5
com.iapp.leochen.apkinjector:id/stretch = 0x7f0801c4
com.iapp.leochen.apkinjector:id/staticPostLayout = 0x7f0801c2
com.iapp.leochen.apkinjector:layout/abc_alert_dialog_material = 0x7f0b0009
com.iapp.leochen.apkinjector:id/staticLayout = 0x7f0801c1
com.iapp.leochen.apkinjector:style/Base.V14.Theme.Material3.Light.SideSheetDialog = 0x7f110091
com.iapp.leochen.apkinjector:id/start = 0x7f0801bd
com.iapp.leochen.apkinjector:id/standard = 0x7f0801bc
com.iapp.leochen.apkinjector:id/src_over = 0x7f0801bb
com.iapp.leochen.apkinjector:id/src_in = 0x7f0801ba
com.iapp.leochen.apkinjector:id/src_atop = 0x7f0801b9
com.iapp.leochen.apkinjector:styleable/BottomSheetBehavior_Layout = 0x7f120017
com.iapp.leochen.apkinjector:id/square = 0x7f0801b8
com.iapp.leochen.apkinjector:id/spring = 0x7f0801b7
com.iapp.leochen.apkinjector:id/spread_inside = 0x7f0801b6
com.iapp.leochen.apkinjector:id/spread = 0x7f0801b5
com.iapp.leochen.apkinjector:id/split_action_bar = 0x7f0801b4
com.iapp.leochen.apkinjector:id/spline = 0x7f0801b3
com.iapp.leochen.apkinjector:id/spacer = 0x7f0801b1
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.TextInputLayout.FilledBox = 0x7f110451
com.iapp.leochen.apkinjector:id/south = 0x7f0801b0
com.iapp.leochen.apkinjector:id/snap = 0x7f0801ae
com.iapp.leochen.apkinjector:id/snackbar_text = 0x7f0801ad
com.iapp.leochen.apkinjector:id/skipped = 0x7f0801aa
com.iapp.leochen.apkinjector:id/sin = 0x7f0801a8
com.iapp.leochen.apkinjector:style/Base.ThemeOverlay.Material3.BottomSheetDialog = 0x7f110081
com.iapp.leochen.apkinjector:id/showTitle = 0x7f0801a7
com.iapp.leochen.apkinjector:id/showHome = 0x7f0801a6
com.iapp.leochen.apkinjector:style/Theme.Material3.DynamicColors.DayNight.NoActionBar = 0x7f11023c
com.iapp.leochen.apkinjector:id/shortcut = 0x7f0801a4
com.iapp.leochen.apkinjector:id/selection_type = 0x7f0801a1
com.iapp.leochen.apkinjector:layout/abc_action_menu_item_layout = 0x7f0b0002
com.iapp.leochen.apkinjector:id/select_dialog_listview = 0x7f08019f
com.iapp.leochen.apkinjector:id/search_plate = 0x7f08019c
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.TimePicker.Display.HelperText = 0x7f11045f
com.iapp.leochen.apkinjector:id/search_mag_icon = 0x7f08019b
com.iapp.leochen.apkinjector:id/search_go_btn = 0x7f08019a
com.iapp.leochen.apkinjector:id/search_edit_frame = 0x7f080199
com.iapp.leochen.apkinjector:layout/design_navigation_item_header = 0x7f0b0026
com.iapp.leochen.apkinjector:id/search_close_btn = 0x7f080198
com.iapp.leochen.apkinjector:id/search_button = 0x7f080197
com.iapp.leochen.apkinjector:id/search_badge = 0x7f080195
com.iapp.leochen.apkinjector:id/scrollIndicatorDown = 0x7f080191
com.iapp.leochen.apkinjector:id/screen = 0x7f08018f
com.iapp.leochen.apkinjector:id/scale = 0x7f08018e
com.iapp.leochen.apkinjector:macro/m3_comp_date_picker_modal_header_headline_type = 0x7f0c0016
com.iapp.leochen.apkinjector:id/save_overlay_view = 0x7f08018c
com.iapp.leochen.apkinjector:id/save_non_transition_alpha = 0x7f08018b
com.iapp.leochen.apkinjector:id/row_index_key = 0x7f08018a
com.iapp.leochen.apkinjector:id/rectangles = 0x7f080182
com.iapp.leochen.apkinjector:id/pressed = 0x7f08017d
com.iapp.leochen.apkinjector:string/abc_menu_shift_shortcut_label = 0x7f10000e
com.iapp.leochen.apkinjector:id/postLayout = 0x7f08017c
com.iapp.leochen.apkinjector:id/password_toggle = 0x7f080173
com.iapp.leochen.apkinjector:id/parent_matrix = 0x7f080172
com.iapp.leochen.apkinjector:style/Base.V14.Theme.Material3.Dark.SideSheetDialog = 0x7f11008d
com.iapp.leochen.apkinjector:id/parentPanel = 0x7f080170
com.iapp.leochen.apkinjector:id/parent = 0x7f08016f
com.iapp.leochen.apkinjector:layout/ime_base_split_test_activity = 0x7f0b0030
com.iapp.leochen.apkinjector:id/parallax = 0x7f08016e
com.iapp.leochen.apkinjector:macro/m3_comp_navigation_bar_active_hover_label_text_color = 0x7f0c0063
com.iapp.leochen.apkinjector:id/packed = 0x7f08016d
com.iapp.leochen.apkinjector:id/outward = 0x7f08016b
com.iapp.leochen.apkinjector:id/outline = 0x7f08016a
com.iapp.leochen.apkinjector:id/open_search_view_toolbar_container = 0x7f080169
com.iapp.leochen.apkinjector:id/open_search_view_toolbar = 0x7f080168
com.iapp.leochen.apkinjector:id/open_search_view_search_prefix = 0x7f080166
com.iapp.leochen.apkinjector:id/open_search_view_scrim = 0x7f080165
com.iapp.leochen.apkinjector:id/open_search_view_root = 0x7f080164
com.iapp.leochen.apkinjector:id/open_search_view_header_container = 0x7f080163
com.iapp.leochen.apkinjector:string/mtrl_switch_thumb_group_name = 0x7f100091
com.iapp.leochen.apkinjector:string/mtrl_picker_toggle_to_calendar_input_mode = 0x7f10008d
com.iapp.leochen.apkinjector:id/open_search_view_edit_text = 0x7f080162
com.iapp.leochen.apkinjector:id/rightToLeft = 0x7f080186
com.iapp.leochen.apkinjector:id/open_search_view_dummy_toolbar = 0x7f080161
com.iapp.leochen.apkinjector:id/open_search_view_content_container = 0x7f08015f
com.iapp.leochen.apkinjector:id/notification_main_column_container = 0x7f080158
com.iapp.leochen.apkinjector:id/notification_background = 0x7f080156
com.iapp.leochen.apkinjector:style/Widget.Material3.Badge.AdjustToBounds = 0x7f110347
com.iapp.leochen.apkinjector:id/north = 0x7f080155
com.iapp.leochen.apkinjector:id/onInterceptTouchReturnSwipe = 0x7f08015b
com.iapp.leochen.apkinjector:id/none = 0x7f080153
com.iapp.leochen.apkinjector:id/noState = 0x7f080152
com.iapp.leochen.apkinjector:style/TextAppearance.Material3.HeadlineLarge = 0x7f1101eb
com.iapp.leochen.apkinjector:id/noScroll = 0x7f080151
com.iapp.leochen.apkinjector:id/rounded = 0x7f080189
com.iapp.leochen.apkinjector:id/neverCompleteToEnd = 0x7f08014f
com.iapp.leochen.apkinjector:id/navigation_bar_item_small_label_view = 0x7f08014c
com.iapp.leochen.apkinjector:id/navigation_bar_item_large_label_view = 0x7f08014b
com.iapp.leochen.apkinjector:id/navigation_bar_item_icon_view = 0x7f080149
com.iapp.leochen.apkinjector:macro/m3_comp_filled_icon_button_toggle_selected_icon_color = 0x7f0c0049
com.iapp.leochen.apkinjector:id/navigation_bar_item_icon_container = 0x7f080148
com.iapp.leochen.apkinjector:id/navigation_bar_item_active_indicator_view = 0x7f080147
com.iapp.leochen.apkinjector:id/tag_on_apply_window_listener = 0x7f0801ce
com.iapp.leochen.apkinjector:id/navigateButton = 0x7f080146
com.iapp.leochen.apkinjector:id/nav_home = 0x7f080145
com.iapp.leochen.apkinjector:id/nav_about = 0x7f080144
com.iapp.leochen.apkinjector:id/multiply = 0x7f080143
com.iapp.leochen.apkinjector:id/mtrl_picker_text_input_range_start = 0x7f080140
com.iapp.leochen.apkinjector:id/mtrl_picker_text_input_date = 0x7f08013e
com.iapp.leochen.apkinjector:macro/m3_comp_switch_unselected_focus_track_outline_color = 0x7f0c0133
com.iapp.leochen.apkinjector:macro/m3_comp_outlined_text_field_input_text_color = 0x7f0c00c0
com.iapp.leochen.apkinjector:id/mtrl_picker_header_toggle = 0x7f08013d
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.Chip.Action = 0x7f11040e
com.iapp.leochen.apkinjector:id/mtrl_picker_header_title_and_selection = 0x7f08013c
com.iapp.leochen.apkinjector:id/mtrl_picker_header_selection_text = 0x7f08013b
com.iapp.leochen.apkinjector:style/Base.V21.ThemeOverlay.Material3.SideSheetDialog = 0x7f1100ac
com.iapp.leochen.apkinjector:id/mtrl_picker_header = 0x7f08013a
com.iapp.leochen.apkinjector:id/mtrl_card_checked_layer_id = 0x7f080135
com.iapp.leochen.apkinjector:styleable/Capability = 0x7f120019
com.iapp.leochen.apkinjector:id/mtrl_calendar_text_input_frame = 0x7f080133
com.iapp.leochen.apkinjector:macro/m3_comp_outlined_text_field_error_outline_color = 0x7f0c00b6
com.iapp.leochen.apkinjector:id/mtrl_calendar_months = 0x7f080131
com.iapp.leochen.apkinjector:id/mtrl_calendar_main_pane = 0x7f080130
com.iapp.leochen.apkinjector:id/mtrl_calendar_frame = 0x7f08012f
com.iapp.leochen.apkinjector:id/mtrl_calendar_days_of_week = 0x7f08012e
com.iapp.leochen.apkinjector:style/Widget.Material3.Button.TextButton.Dialog = 0x7f11035c
com.iapp.leochen.apkinjector:style/Base.V24.Theme.Material3.Light = 0x7f1100b4
com.iapp.leochen.apkinjector:id/motion_base = 0x7f08012b
com.iapp.leochen.apkinjector:id/month_navigation_previous = 0x7f080129
com.iapp.leochen.apkinjector:id/month_navigation_next = 0x7f080128
com.iapp.leochen.apkinjector:id/month_navigation_fragment_toggle = 0x7f080127
com.iapp.leochen.apkinjector:style/Widget.Material3.FloatingActionButton.Surface = 0x7f110395
com.iapp.leochen.apkinjector:id/month_grid = 0x7f080125
com.iapp.leochen.apkinjector:id/mini = 0x7f080124
com.iapp.leochen.apkinjector:id/matrix = 0x7f080121
com.iapp.leochen.apkinjector:id/material_timepicker_mode_button = 0x7f08011d
com.iapp.leochen.apkinjector:id/material_timepicker_cancel_button = 0x7f08011b
com.iapp.leochen.apkinjector:id/material_textinput_timepicker = 0x7f08011a
com.iapp.leochen.apkinjector:id/material_minute_tv = 0x7f080119
com.iapp.leochen.apkinjector:id/material_minute_text_input = 0x7f080118
com.iapp.leochen.apkinjector:id/material_label = 0x7f080117
com.iapp.leochen.apkinjector:id/material_hour_tv = 0x7f080116
com.iapp.leochen.apkinjector:string/m3_sys_motion_easing_linear = 0x7f100043
com.iapp.leochen.apkinjector:id/material_clock_period_pm_button = 0x7f080113
com.iapp.leochen.apkinjector:id/material_clock_period_am_button = 0x7f080112
com.iapp.leochen.apkinjector:id/material_clock_level = 0x7f080111
com.iapp.leochen.apkinjector:id/match_parent = 0x7f08010c
com.iapp.leochen.apkinjector:id/match_constraint = 0x7f08010b
com.iapp.leochen.apkinjector:id/masked = 0x7f08010a
com.iapp.leochen.apkinjector:id/m3_side_sheet = 0x7f080107
com.iapp.leochen.apkinjector:style/Widget.Material3.TabLayout.OnSurface = 0x7f1103d8
com.iapp.leochen.apkinjector:interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_1 = 0x7f0a0003
com.iapp.leochen.apkinjector:id/loadingIndicator = 0x7f080106
com.iapp.leochen.apkinjector:id/listMode = 0x7f080104
com.iapp.leochen.apkinjector:id/linear = 0x7f080103
com.iapp.leochen.apkinjector:id/left = 0x7f0800fe
com.iapp.leochen.apkinjector:id/labeled = 0x7f0800fc
com.iapp.leochen.apkinjector:id/item_touch_helper_previous_elevation = 0x7f0800f9
com.iapp.leochen.apkinjector:id/is_pooling_container_tag = 0x7f0800f7
com.iapp.leochen.apkinjector:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.SubmenuArrow = 0x7f11014b
com.iapp.leochen.apkinjector:id/invisible = 0x7f0800f5
com.iapp.leochen.apkinjector:id/indeterminate = 0x7f0800f3
com.iapp.leochen.apkinjector:id/included = 0x7f0800f2
com.iapp.leochen.apkinjector:style/Base.Widget.AppCompat.Spinner = 0x7f1100f7
com.iapp.leochen.apkinjector:id/immediateStop = 0x7f0800f1
com.iapp.leochen.apkinjector:id/image = 0x7f0800f0
com.iapp.leochen.apkinjector:id/ignoreRequest = 0x7f0800ef
com.iapp.leochen.apkinjector:id/ignore = 0x7f0800ee
com.iapp.leochen.apkinjector:style/Base.V14.ThemeOverlay.MaterialComponents.Dialog = 0x7f11009e
com.iapp.leochen.apkinjector:id/icon_group = 0x7f0800ec
com.iapp.leochen.apkinjector:style/Theme.AppCompat.DayNight.Dialog.Alert = 0x7f110212
com.iapp.leochen.apkinjector:id/transition_transform = 0x7f0801fc
com.iapp.leochen.apkinjector:id/text_input_error_icon = 0x7f0801e1
com.iapp.leochen.apkinjector:id/icon = 0x7f0800eb
com.iapp.leochen.apkinjector:id/horizontal_only = 0x7f0800ea
com.iapp.leochen.apkinjector:styleable/Chip = 0x7f12001d
com.iapp.leochen.apkinjector:macro/m3_comp_navigation_bar_inactive_focus_icon_color = 0x7f0c006c
com.iapp.leochen.apkinjector:id/honorRequest = 0x7f0800e8
com.iapp.leochen.apkinjector:id/homeAsUp = 0x7f0800e7
com.iapp.leochen.apkinjector:id/home = 0x7f0800e6
com.iapp.leochen.apkinjector:id/hideable = 0x7f0800e5
com.iapp.leochen.apkinjector:id/hide_ime_id = 0x7f0800e4
com.iapp.leochen.apkinjector:style/Base.Widget.AppCompat.ActionBar.TabText = 0x7f1100c6
com.iapp.leochen.apkinjector:id/header_title = 0x7f0800e3
com.iapp.leochen.apkinjector:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense = 0x7f1102c8
com.iapp.leochen.apkinjector:id/group_divider = 0x7f0800e0
com.iapp.leochen.apkinjector:macro/m3_comp_switch_selected_pressed_icon_color = 0x7f0c012b
com.iapp.leochen.apkinjector:id/view_tree_on_back_pressed_dispatcher_owner = 0x7f08020a
com.iapp.leochen.apkinjector:id/grantPermissionButton = 0x7f0800dd
com.iapp.leochen.apkinjector:id/gone = 0x7f0800dc
com.iapp.leochen.apkinjector:id/ghost_view_holder = 0x7f0800db
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.MaterialCalendar.Year.Selected = 0x7f110436
com.iapp.leochen.apkinjector:id/ghost_view = 0x7f0800da
com.iapp.leochen.apkinjector:id/frost = 0x7f0800d8
com.iapp.leochen.apkinjector:id/fragment_container_view_tag = 0x7f0800d7
com.iapp.leochen.apkinjector:id/floating = 0x7f0800d5
com.iapp.leochen.apkinjector:id/fixed = 0x7f0800d3
com.iapp.leochen.apkinjector:id/fitXY = 0x7f0800d2
com.iapp.leochen.apkinjector:id/fitToContents = 0x7f0800d1
com.iapp.leochen.apkinjector:id/fitEnd = 0x7f0800cf
com.iapp.leochen.apkinjector:id/fitCenter = 0x7f0800ce
com.iapp.leochen.apkinjector:id/filled = 0x7f0800cd
com.iapp.leochen.apkinjector:id/fill_vertical = 0x7f0800cc
com.iapp.leochen.apkinjector:id/fill = 0x7f0800ca
com.iapp.leochen.apkinjector:id/fileRecyclerViewNext = 0x7f0800c9
com.iapp.leochen.apkinjector:id/fileRecyclerView = 0x7f0800c8
com.iapp.leochen.apkinjector:id/fileName = 0x7f0800c7
com.iapp.leochen.apkinjector:style/ShapeAppearanceOverlay.MaterialComponents.Chip = 0x7f11018d
com.iapp.leochen.apkinjector:id/fileListContainer = 0x7f0800c6
com.iapp.leochen.apkinjector:style/Base.Theme.MaterialComponents.Light.Bridge = 0x7f110070
com.iapp.leochen.apkinjector:id/fileIcon = 0x7f0800c5
com.iapp.leochen.apkinjector:id/fileDate = 0x7f0800c4
com.iapp.leochen.apkinjector:id/fileBrowserLayout = 0x7f0800c3
com.iapp.leochen.apkinjector:id/fade = 0x7f0800c1
com.iapp.leochen.apkinjector:id/expanded_menu = 0x7f0800c0
com.iapp.leochen.apkinjector:id/enterAlwaysCollapsed = 0x7f0800bc
com.iapp.leochen.apkinjector:id/enterAlways = 0x7f0800bb
com.iapp.leochen.apkinjector:id/endToStart = 0x7f0800ba
com.iapp.leochen.apkinjector:id/end = 0x7f0800b9
com.iapp.leochen.apkinjector:id/embed = 0x7f0800b8
com.iapp.leochen.apkinjector:id/edit_text_id = 0x7f0800b6
com.iapp.leochen.apkinjector:id/edit_query = 0x7f0800b5
com.iapp.leochen.apkinjector:id/edge = 0x7f0800b4
com.iapp.leochen.apkinjector:id/east = 0x7f0800b3
com.iapp.leochen.apkinjector:id/line1 = 0x7f080101
com.iapp.leochen.apkinjector:id/dropdown_menu = 0x7f0800af
com.iapp.leochen.apkinjector:macro/m3_comp_search_view_header_supporting_text_color = 0x7f0c00f7
com.iapp.leochen.apkinjector:id/dragStart = 0x7f0800ad
com.iapp.leochen.apkinjector:id/dragRight = 0x7f0800ac
com.iapp.leochen.apkinjector:id/dragLeft = 0x7f0800ab
com.iapp.leochen.apkinjector:style/Widget.AppCompat.TextView = 0x7f11032d
com.iapp.leochen.apkinjector:macro/m3_comp_assist_chip_container_shape = 0x7f0c0000
com.iapp.leochen.apkinjector:id/dragEnd = 0x7f0800aa
com.iapp.leochen.apkinjector:id/dragDown = 0x7f0800a9
com.iapp.leochen.apkinjector:macro/m3_comp_date_picker_modal_container_color = 0x7f0c000d
com.iapp.leochen.apkinjector:id/disablePostScroll = 0x7f0800a4
com.iapp.leochen.apkinjector:macro/m3_comp_time_picker_time_selector_separator_color = 0x7f0c0165
com.iapp.leochen.apkinjector:id/disableHome = 0x7f0800a2
com.iapp.leochen.apkinjector:id/direct = 0x7f0800a1
com.iapp.leochen.apkinjector:id/dialog_button = 0x7f08009f
com.iapp.leochen.apkinjector:layout/abc_list_menu_item_checkbox = 0x7f0b000e
com.iapp.leochen.apkinjector:id/design_navigation_view = 0x7f08009e
com.iapp.leochen.apkinjector:style/Widget.AppCompat.ActionBar.TabView = 0x7f1102ec
com.iapp.leochen.apkinjector:id/design_menu_item_text = 0x7f08009d
com.iapp.leochen.apkinjector:id/design_menu_item_action_area_stub = 0x7f08009c
com.iapp.leochen.apkinjector:string/bottomsheet_drag_handle_content_description = 0x7f100023
com.iapp.leochen.apkinjector:id/design_menu_item_action_area = 0x7f08009b
com.iapp.leochen.apkinjector:id/design_bottom_sheet = 0x7f08009a
com.iapp.leochen.apkinjector:id/deltaRelative = 0x7f080098
com.iapp.leochen.apkinjector:id/decor_content_parent = 0x7f080096
com.iapp.leochen.apkinjector:id/decelerateAndComplete = 0x7f080095
com.iapp.leochen.apkinjector:id/decelerate = 0x7f080094
com.iapp.leochen.apkinjector:id/currentState = 0x7f08008f
com.iapp.leochen.apkinjector:id/cradle = 0x7f08008c
com.iapp.leochen.apkinjector:id/counterclockwise = 0x7f08008b
com.iapp.leochen.apkinjector:id/coordinator = 0x7f080089
com.iapp.leochen.apkinjector:id/continuousVelocity = 0x7f080088
com.iapp.leochen.apkinjector:id/contiguous = 0x7f080087
com.iapp.leochen.apkinjector:id/progress_horizontal = 0x7f08017f
com.iapp.leochen.apkinjector:id/content = 0x7f080085
com.iapp.leochen.apkinjector:id/container = 0x7f080084
com.iapp.leochen.apkinjector:id/constraint = 0x7f080083
com.iapp.leochen.apkinjector:id/confirm_button = 0x7f080082
com.iapp.leochen.apkinjector:id/compress = 0x7f080081
com.iapp.leochen.apkinjector:id/clockwise = 0x7f08007e
com.iapp.leochen.apkinjector:id/clip_vertical = 0x7f08007d
com.iapp.leochen.apkinjector:id/clip_horizontal = 0x7f08007c
com.iapp.leochen.apkinjector:id/circle_center = 0x7f08007a
com.iapp.leochen.apkinjector:id/chronometer = 0x7f080079
com.iapp.leochen.apkinjector:id/checked = 0x7f080078
com.iapp.leochen.apkinjector:id/checkbox = 0x7f080077
com.iapp.leochen.apkinjector:id/chain2 = 0x7f080075
com.iapp.leochen.apkinjector:macro/m3_comp_switch_unselected_hover_track_color = 0x7f0c0138
com.iapp.leochen.apkinjector:id/chain = 0x7f080074
com.iapp.leochen.apkinjector:id/centerCrop = 0x7f080070
com.iapp.leochen.apkinjector:id/carryVelocity = 0x7f08006e
com.iapp.leochen.apkinjector:styleable/MaterialDivider = 0x7f120057
com.iapp.leochen.apkinjector:id/cancel_button = 0x7f08006d
com.iapp.leochen.apkinjector:id/cancelButton = 0x7f08006c
com.iapp.leochen.apkinjector:id/callMeasure = 0x7f08006b
com.iapp.leochen.apkinjector:id/buttonPanel = 0x7f080069
com.iapp.leochen.apkinjector:id/bounceStart = 0x7f080068
com.iapp.leochen.apkinjector:string/mtrl_picker_date_header_title = 0x7f100074
com.iapp.leochen.apkinjector:id/bounceEnd = 0x7f080067
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.Button.TextButton = 0x7f110404
com.iapp.leochen.apkinjector:id/bounceBoth = 0x7f080066
com.iapp.leochen.apkinjector:id/bounce = 0x7f080065
com.iapp.leochen.apkinjector:id/bottomNavigation = 0x7f080064
com.iapp.leochen.apkinjector:id/notification_main_column = 0x7f080157
com.iapp.leochen.apkinjector:id/blocking = 0x7f080062
com.iapp.leochen.apkinjector:style/Animation.AppCompat.Dialog = 0x7f110002
com.iapp.leochen.apkinjector:id/barrier = 0x7f08005c
com.iapp.leochen.apkinjector:id/axisRelative = 0x7f08005b
com.iapp.leochen.apkinjector:id/autoCompleteToStart = 0x7f08005a
com.iapp.leochen.apkinjector:id/autoComplete = 0x7f080058
com.iapp.leochen.apkinjector:id/auto = 0x7f080057
com.iapp.leochen.apkinjector:id/async = 0x7f080056
com.iapp.leochen.apkinjector:id/asConfigured = 0x7f080055
com.iapp.leochen.apkinjector:id/arrowIcon = 0x7f080054
com.iapp.leochen.apkinjector:id/arc = 0x7f080053
com.iapp.leochen.apkinjector:id/appBarLayout = 0x7f080052
com.iapp.leochen.apkinjector:id/easeOut = 0x7f0800b2
com.iapp.leochen.apkinjector:id/anticipate = 0x7f080051
com.iapp.leochen.apkinjector:macro/m3_comp_primary_navigation_tab_active_indicator_color = 0x7f0c00c9
com.iapp.leochen.apkinjector:id/animationContainer = 0x7f08004f
com.iapp.leochen.apkinjector:id/animateToEnd = 0x7f08004d
com.iapp.leochen.apkinjector:id/always = 0x7f08004c
com.iapp.leochen.apkinjector:plurals/mtrl_badge_content_description = 0x7f0f0000
com.iapp.leochen.apkinjector:id/material_timepicker_ok_button = 0x7f08011e
com.iapp.leochen.apkinjector:id/allStates = 0x7f08004b
com.iapp.leochen.apkinjector:id/aligned = 0x7f080049
com.iapp.leochen.apkinjector:id/alertTitle = 0x7f080048
com.iapp.leochen.apkinjector:id/expand_activities_button = 0x7f0800bf
com.iapp.leochen.apkinjector:id/activity_chooser_view_content = 0x7f080046
com.iapp.leochen.apkinjector:id/actions = 0x7f080045
com.iapp.leochen.apkinjector:id/action_mode_close_button = 0x7f080043
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.ExtendedFloatingActionButton = 0x7f11041b
com.iapp.leochen.apkinjector:id/action_menu_presenter = 0x7f080040
com.iapp.leochen.apkinjector:id/action_menu_divider = 0x7f08003f
com.iapp.leochen.apkinjector:id/action_image = 0x7f08003e
com.iapp.leochen.apkinjector:id/action_divider = 0x7f08003d
com.iapp.leochen.apkinjector:style/TextAppearance.AppCompat.Display1 = 0x7f110199
com.iapp.leochen.apkinjector:id/action_container = 0x7f08003b
com.iapp.leochen.apkinjector:id/action_bar_title = 0x7f08003a
com.iapp.leochen.apkinjector:style/Widget.AppCompat.ActionBar = 0x7f1102e8
com.iapp.leochen.apkinjector:id/action_bar_subtitle = 0x7f080039
com.iapp.leochen.apkinjector:macro/m3_comp_filled_text_field_input_text_type = 0x7f0c0050
com.iapp.leochen.apkinjector:id/action_bar_root = 0x7f080037
com.iapp.leochen.apkinjector:id/actionUp = 0x7f080033
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.NavigationRailView = 0x7f11043a
com.iapp.leochen.apkinjector:id/actionDown = 0x7f080031
com.iapp.leochen.apkinjector:id/accessibility_custom_action_9 = 0x7f080030
com.iapp.leochen.apkinjector:id/accessibility_custom_action_8 = 0x7f08002f
com.iapp.leochen.apkinjector:id/accessibility_custom_action_6 = 0x7f08002d
com.iapp.leochen.apkinjector:id/accessibility_custom_action_5 = 0x7f08002c
com.iapp.leochen.apkinjector:id/accessibility_custom_action_4 = 0x7f08002b
com.iapp.leochen.apkinjector:id/accessibility_custom_action_28 = 0x7f080026
com.iapp.leochen.apkinjector:string/fab_transformation_scrim_behavior = 0x7f100032
com.iapp.leochen.apkinjector:id/accessibility_custom_action_27 = 0x7f080025
com.iapp.leochen.apkinjector:id/accessibility_custom_action_26 = 0x7f080024
com.iapp.leochen.apkinjector:id/closest = 0x7f08007f
com.iapp.leochen.apkinjector:id/accessibility_custom_action_25 = 0x7f080023
com.iapp.leochen.apkinjector:id/accessibility_custom_action_23 = 0x7f080021
com.iapp.leochen.apkinjector:style/Widget.Material3.TabLayout = 0x7f1103d7
com.iapp.leochen.apkinjector:id/special_effects_controller_view_tag = 0x7f0801b2
com.iapp.leochen.apkinjector:id/accessibility_custom_action_22 = 0x7f080020
com.iapp.leochen.apkinjector:id/accessibility_custom_action_16 = 0x7f080019
com.iapp.leochen.apkinjector:attr/actionBarSplitStyle = 0x7f030006
com.iapp.leochen.apkinjector:id/accessibility_custom_action_15 = 0x7f080018
com.iapp.leochen.apkinjector:style/ThemeOverlay.Material3 = 0x7f110280
com.iapp.leochen.apkinjector:id/accessibility_custom_action_14 = 0x7f080017
com.iapp.leochen.apkinjector:dimen/design_bottom_navigation_icon_size = 0x7f060064
com.iapp.leochen.apkinjector:id/SYM = 0x7f08000b
com.iapp.leochen.apkinjector:style/Base.Theme.AppCompat.DialogWhenLarge = 0x7f110052
com.iapp.leochen.apkinjector:attr/dialogTheme = 0x7f03017a
com.iapp.leochen.apkinjector:id/FUNCTION = 0x7f080004
com.iapp.leochen.apkinjector:animator/mtrl_fab_show_motion_spec = 0x7f02001f
com.iapp.leochen.apkinjector:color/material_dynamic_primary20 = 0x7f05023f
com.iapp.leochen.apkinjector:id/ALT = 0x7f080000
com.iapp.leochen.apkinjector:attr/placeholder_emptyVisibility = 0x7f030386
com.iapp.leochen.apkinjector:id/accessibility_custom_action_11 = 0x7f080014
com.iapp.leochen.apkinjector:attr/flow_padding = 0x7f0301f5
com.iapp.leochen.apkinjector:drawable/tooltip_frame_light = 0x7f0700ef
com.iapp.leochen.apkinjector:dimen/m3_timepicker_display_stroke_width = 0x7f06021e
com.iapp.leochen.apkinjector:drawable/notification_template_icon_low_bg = 0x7f0700ea
com.iapp.leochen.apkinjector:drawable/notification_bg_normal_pressed = 0x7f0700e6
com.iapp.leochen.apkinjector:id/accessibility_custom_action_21 = 0x7f08001f
com.iapp.leochen.apkinjector:drawable/notification_bg_normal = 0x7f0700e5
com.iapp.leochen.apkinjector:style/TextAppearance.AppCompat.Widget.ActionBar.Menu = 0x7f1101b1
com.iapp.leochen.apkinjector:attr/tickColor = 0x7f03048b
com.iapp.leochen.apkinjector:color/secondary_text_default_material_dark = 0x7f0502fa
com.iapp.leochen.apkinjector:drawable/mtrl_switch_thumb_unchecked_checked = 0x7f0700da
com.iapp.leochen.apkinjector:drawable/mtrl_switch_thumb_pressed_checked = 0x7f0700d7
com.iapp.leochen.apkinjector:drawable/mtrl_switch_thumb_pressed = 0x7f0700d6
com.iapp.leochen.apkinjector:color/primary_text_default_material_dark = 0x7f0502f4
com.iapp.leochen.apkinjector:drawable/mtrl_switch_thumb_checked_pressed = 0x7f0700d4
com.iapp.leochen.apkinjector:drawable/mtrl_navigation_bar_item_background = 0x7f0700cf
com.iapp.leochen.apkinjector:drawable/mtrl_ic_error = 0x7f0700cd
com.iapp.leochen.apkinjector:macro/m3_comp_radio_button_unselected_focus_icon_color = 0x7f0c00df
com.iapp.leochen.apkinjector:drawable/mtrl_ic_checkbox_unchecked = 0x7f0700cc
com.iapp.leochen.apkinjector:color/cardview_dark_background = 0x7f05002c
com.iapp.leochen.apkinjector:dimen/m3_bottom_nav_item_padding_bottom = 0x7f0600bf
com.iapp.leochen.apkinjector:drawable/mtrl_ic_cancel = 0x7f0700c9
com.iapp.leochen.apkinjector:drawable/mtrl_ic_arrow_drop_up = 0x7f0700c8
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_primary100 = 0x7f0500d0
com.iapp.leochen.apkinjector:dimen/highlight_alpha_material_dark = 0x7f060094
com.iapp.leochen.apkinjector:drawable/m3_tabs_line_indicator = 0x7f0700ad
com.iapp.leochen.apkinjector:drawable/mtrl_ic_arrow_drop_down = 0x7f0700c7
com.iapp.leochen.apkinjector:drawable/mtrl_dropdown_arrow = 0x7f0700c6
com.iapp.leochen.apkinjector:attr/customStringValue = 0x7f03016b
com.iapp.leochen.apkinjector:drawable/mtrl_checkbox_button_icon_unchecked_indeterminate = 0x7f0700c3
com.iapp.leochen.apkinjector:styleable/AppBarLayout_Layout = 0x7f12000c
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.LinearProgressIndicator = 0x7f11041f
com.iapp.leochen.apkinjector:style/Widget.Material3.TextInputEditText.FilledBox.Dense = 0x7f1103db
com.iapp.leochen.apkinjector:drawable/mtrl_checkbox_button_icon_checked_unchecked = 0x7f0700bf
com.iapp.leochen.apkinjector:drawable/mtrl_checkbox_button_icon_checked_indeterminate = 0x7f0700be
com.iapp.leochen.apkinjector:attr/isMaterial3DynamicColorApplied = 0x7f03024b
com.iapp.leochen.apkinjector:drawable/mtrl_checkbox_button_icon = 0x7f0700bd
com.iapp.leochen.apkinjector:attr/bottomNavigationStyle = 0x7f03007a
com.iapp.leochen.apkinjector:color/m3_highlighted_text = 0x7f05008c
com.iapp.leochen.apkinjector:drawable/mtrl_switch_thumb_unchecked_pressed = 0x7f0700db
com.iapp.leochen.apkinjector:attr/nestedScrollable = 0x7f03035d
com.iapp.leochen.apkinjector:drawable/mtrl_checkbox_button_checked_unchecked = 0x7f0700bc
com.iapp.leochen.apkinjector:drawable/mtrl_checkbox_button = 0x7f0700bb
com.iapp.leochen.apkinjector:dimen/mtrl_btn_icon_btn_padding_left = 0x7f060260
com.iapp.leochen.apkinjector:drawable/material_ic_keyboard_arrow_previous_black_24dp = 0x7f0700b6
com.iapp.leochen.apkinjector:drawable/material_ic_edit_black_24dp = 0x7f0700b3
com.iapp.leochen.apkinjector:macro/m3_comp_date_picker_modal_range_selection_month_subhead_color = 0x7f0c001b
com.iapp.leochen.apkinjector:attr/alphabeticModifiers = 0x7f03002f
com.iapp.leochen.apkinjector:attr/tabContentStart = 0x7f030419
com.iapp.leochen.apkinjector:drawable/material_ic_calendar_black_24dp = 0x7f0700b1
com.iapp.leochen.apkinjector:attr/extendedFloatingActionButtonTertiaryStyle = 0x7f0301c8
com.iapp.leochen.apkinjector:drawable/m3_tabs_rounded_line_indicator = 0x7f0700ae
com.iapp.leochen.apkinjector:id/tag_transition_group = 0x7f0801d3
com.iapp.leochen.apkinjector:attr/helperTextTextColor = 0x7f030221
com.iapp.leochen.apkinjector:drawable/m3_selection_control_ripple = 0x7f0700ab
com.iapp.leochen.apkinjector:drawable/m3_password_eye = 0x7f0700a8
com.iapp.leochen.apkinjector:drawable/m3_bottom_sheet_drag_handle = 0x7f0700a7
com.iapp.leochen.apkinjector:attr/textInputOutlinedStyle = 0x7f03046e
com.iapp.leochen.apkinjector:drawable/ic_search_black_24 = 0x7f0700a3
com.iapp.leochen.apkinjector:integer/status_bar_notification_info_maxnum = 0x7f090043
com.iapp.leochen.apkinjector:drawable/ic_navigation = 0x7f0700a2
com.iapp.leochen.apkinjector:drawable/ic_mtrl_chip_close_circle = 0x7f0700a1
com.iapp.leochen.apkinjector:drawable/$mtrl_checkbox_button_unchecked_checked__0 = 0x7f07001e
com.iapp.leochen.apkinjector:drawable/ic_mtrl_checked_circle = 0x7f07009e
com.iapp.leochen.apkinjector:style/Base.V7.Theme.AppCompat.Light = 0x7f1100bd
com.iapp.leochen.apkinjector:attr/behavior_fitToContents = 0x7f03006b
com.iapp.leochen.apkinjector:attr/endIconDrawable = 0x7f0301a4
com.iapp.leochen.apkinjector:drawable/ic_m3_chip_check = 0x7f07009b
com.iapp.leochen.apkinjector:drawable/ic_keyboard_black_24dp = 0x7f070098
com.iapp.leochen.apkinjector:drawable/ic_folder = 0x7f070095
com.iapp.leochen.apkinjector:drawable/ic_clear_black_24 = 0x7f070092
com.iapp.leochen.apkinjector:dimen/m3_comp_elevated_button_container_elevation = 0x7f060108
com.iapp.leochen.apkinjector:drawable/ic_call_decline_low = 0x7f070090
com.iapp.leochen.apkinjector:style/Theme.MaterialComponents.DayNight = 0x7f11024b
com.iapp.leochen.apkinjector:attr/itemPaddingBottom = 0x7f030259
com.iapp.leochen.apkinjector:drawable/ic_call_decline = 0x7f07008f
com.iapp.leochen.apkinjector:macro/m3_comp_outlined_text_field_disabled_supporting_text_color = 0x7f0c00b5
com.iapp.leochen.apkinjector:drawable/ic_call_answer_video_low = 0x7f07008e
com.iapp.leochen.apkinjector:drawable/ic_bookmark_border = 0x7f07008a
com.iapp.leochen.apkinjector:drawable/design_password_eye = 0x7f070085
com.iapp.leochen.apkinjector:drawable/design_ic_visibility_off = 0x7f070084
com.iapp.leochen.apkinjector:attr/customReference = 0x7f03016a
com.iapp.leochen.apkinjector:attr/shrinkMotionSpec = 0x7f0303d2
com.iapp.leochen.apkinjector:drawable/btn_radio_on_to_off_mtrl_animation = 0x7f070081
com.iapp.leochen.apkinjector:color/m3_sys_color_on_primary_fixed_variant = 0x7f0501ef
com.iapp.leochen.apkinjector:drawable/btn_checkbox_unchecked_mtrl = 0x7f07007c
com.iapp.leochen.apkinjector:id/actionDownUp = 0x7f080032
com.iapp.leochen.apkinjector:drawable/btn_checkbox_checked_mtrl = 0x7f07007a
com.iapp.leochen.apkinjector:drawable/avd_show_password = 0x7f070079
com.iapp.leochen.apkinjector:attr/motionDurationLong3 = 0x7f03032d
com.iapp.leochen.apkinjector:drawable/avd_hide_password = 0x7f070078
com.iapp.leochen.apkinjector:id/action_mode_bar_stub = 0x7f080042
com.iapp.leochen.apkinjector:id/CTRL = 0x7f080003
com.iapp.leochen.apkinjector:style/ThemeOverlay.Material3.HarmonizedColors = 0x7f1102a7
com.iapp.leochen.apkinjector:id/slide = 0x7f0801ab
com.iapp.leochen.apkinjector:attr/grid_validateInputs = 0x7f030217
com.iapp.leochen.apkinjector:drawable/abc_vector_test = 0x7f070077
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.CompoundButton.CheckBox = 0x7f110418
com.iapp.leochen.apkinjector:style/Widget.Material3.CollapsingToolbar.Large = 0x7f11037c
com.iapp.leochen.apkinjector:drawable/abc_textfield_search_default_mtrl_alpha = 0x7f070075
com.iapp.leochen.apkinjector:drawable/abc_textfield_default_mtrl_alpha = 0x7f070073
com.iapp.leochen.apkinjector:anim/design_snackbar_in = 0x7f01001a
com.iapp.leochen.apkinjector:attr/viewTransitionMode = 0x7f0304d3
com.iapp.leochen.apkinjector:drawable/abc_textfield_activated_mtrl_alpha = 0x7f070072
com.iapp.leochen.apkinjector:styleable/AppCompatEmojiHelper = 0x7f12000d
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_secondary30 = 0x7f0500df
com.iapp.leochen.apkinjector:drawable/abc_text_select_handle_right_mtrl = 0x7f070071
com.iapp.leochen.apkinjector:attr/constraintSetStart = 0x7f030134
com.iapp.leochen.apkinjector:drawable/abc_text_select_handle_left_mtrl = 0x7f07006f
com.iapp.leochen.apkinjector:drawable/abc_switch_track_mtrl_alpha = 0x7f07006b
com.iapp.leochen.apkinjector:drawable/abc_star_half_black_48dp = 0x7f070069
com.iapp.leochen.apkinjector:drawable/abc_seekbar_track_material = 0x7f070065
com.iapp.leochen.apkinjector:color/material_timepicker_button_background = 0x7f0502b2
com.iapp.leochen.apkinjector:dimen/notification_main_column_padding_top = 0x7f060312
com.iapp.leochen.apkinjector:dimen/mtrl_calendar_header_height = 0x7f060281
com.iapp.leochen.apkinjector:drawable/abc_scrubber_track_mtrl_alpha = 0x7f070062
com.iapp.leochen.apkinjector:styleable/MotionLayout = 0x7f120067
com.iapp.leochen.apkinjector:dimen/m3_comp_fab_primary_hover_container_elevation = 0x7f060118
com.iapp.leochen.apkinjector:drawable/abc_scrubber_primary_mtrl_alpha = 0x7f070061
com.iapp.leochen.apkinjector:macro/m3_comp_fab_primary_container_color = 0x7f0c0036
com.iapp.leochen.apkinjector:drawable/abc_scrubber_control_to_pressed_mtrl_005 = 0x7f070060
com.iapp.leochen.apkinjector:attr/cardBackgroundColor = 0x7f03009a
com.iapp.leochen.apkinjector:dimen/m3_comp_text_button_hover_state_layer_opacity = 0x7f06019d
com.iapp.leochen.apkinjector:drawable/abc_ratingbar_material = 0x7f07005c
com.iapp.leochen.apkinjector:attr/layout_constraintLeft_toRightOf = 0x7f03029b
com.iapp.leochen.apkinjector:drawable/abc_list_selector_disabled_holo_dark = 0x7f070055
com.iapp.leochen.apkinjector:dimen/m3_sys_motion_easing_legacy_accelerate_control_x2 = 0x7f0601ff
com.iapp.leochen.apkinjector:drawable/abc_list_selector_background_transition_holo_light = 0x7f070054
com.iapp.leochen.apkinjector:style/Widget.Material3.Chip.Assist = 0x7f110368
com.iapp.leochen.apkinjector:drawable/abc_list_divider_material = 0x7f07004d
com.iapp.leochen.apkinjector:attr/colorSurfaceContainerLowest = 0x7f030126
com.iapp.leochen.apkinjector:attr/animationMode = 0x7f030035
com.iapp.leochen.apkinjector:drawable/abc_item_background_holo_light = 0x7f07004c
com.iapp.leochen.apkinjector:id/favoriteButton = 0x7f0800c2
com.iapp.leochen.apkinjector:dimen/m3_sys_motion_easing_legacy_control_x1 = 0x7f060202
com.iapp.leochen.apkinjector:drawable/abc_ic_menu_share_mtrl_alpha = 0x7f070048
com.iapp.leochen.apkinjector:attr/buttonIcon = 0x7f030091
com.iapp.leochen.apkinjector:drawable/abc_ic_menu_selectall_mtrl_alpha = 0x7f070047
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.Button.Icon = 0x7f110401
com.iapp.leochen.apkinjector:style/ThemeOverlay.MaterialAlertDialog.Material3.Title.Icon = 0x7f1102bf
com.iapp.leochen.apkinjector:style/TextAppearance.AppCompat.Inverse = 0x7f11019e
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_dark_surface_dim = 0x7f05019a
com.iapp.leochen.apkinjector:drawable/abc_ic_go_search_api_material = 0x7f070042
com.iapp.leochen.apkinjector:layout/mtrl_alert_select_dialog_singlechoice = 0x7f0b004c
com.iapp.leochen.apkinjector:drawable/abc_ic_clear_material = 0x7f070040
com.iapp.leochen.apkinjector:layout/abc_popup_menu_header_item_layout = 0x7f0b0012
com.iapp.leochen.apkinjector:drawable/abc_edit_text_material = 0x7f07003d
com.iapp.leochen.apkinjector:drawable/abc_dialog_material_background = 0x7f07003c
com.iapp.leochen.apkinjector:drawable/abc_btn_radio_to_on_mtrl_000 = 0x7f070034
com.iapp.leochen.apkinjector:drawable/abc_control_background_material = 0x7f07003b
com.iapp.leochen.apkinjector:drawable/abc_item_background_holo_dark = 0x7f07004b
com.iapp.leochen.apkinjector:drawable/abc_cab_background_top_mtrl_alpha = 0x7f07003a
com.iapp.leochen.apkinjector:drawable/abc_cab_background_top_material = 0x7f070039
com.iapp.leochen.apkinjector:string/call_notification_hang_up_action = 0x7f100027
com.iapp.leochen.apkinjector:drawable/abc_cab_background_internal_bg = 0x7f070038
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.PopupMenu.ContextMenu = 0x7f110441
com.iapp.leochen.apkinjector:drawable/abc_btn_radio_to_on_mtrl_015 = 0x7f070035
com.iapp.leochen.apkinjector:drawable/abc_btn_radio_material_anim = 0x7f070033
com.iapp.leochen.apkinjector:style/Base.V14.Theme.Material3.Dark = 0x7f11008a
com.iapp.leochen.apkinjector:drawable/abc_btn_radio_material = 0x7f070032
com.iapp.leochen.apkinjector:style/ThemeOverlay.Material3.Button.IconButton.Filled.Tonal = 0x7f11028f
com.iapp.leochen.apkinjector:drawable/abc_btn_check_material_anim = 0x7f07002d
com.iapp.leochen.apkinjector:drawable/abc_action_bar_item_background_material = 0x7f07002a
com.iapp.leochen.apkinjector:drawable/$mtrl_switch_thumb_unchecked_pressed__0 = 0x7f070028
com.iapp.leochen.apkinjector:style/Widget.Material3.SearchView.Prefix = 0x7f1103ca
com.iapp.leochen.apkinjector:drawable/$mtrl_switch_thumb_pressed_unchecked__0 = 0x7f070025
com.iapp.leochen.apkinjector:styleable/FlowLayout = 0x7f120035
com.iapp.leochen.apkinjector:style/Theme.Design.Light.NoActionBar = 0x7f110227
com.iapp.leochen.apkinjector:drawable/$mtrl_switch_thumb_pressed_checked__0 = 0x7f070024
com.iapp.leochen.apkinjector:styleable/ScrollingViewBehavior_Layout = 0x7f120078
com.iapp.leochen.apkinjector:drawable/$mtrl_switch_thumb_checked_unchecked__1 = 0x7f070023
com.iapp.leochen.apkinjector:id/permissionRequestLayout = 0x7f080178
com.iapp.leochen.apkinjector:dimen/m3_comp_fab_primary_hover_state_layer_opacity = 0x7f060119
com.iapp.leochen.apkinjector:drawable/$mtrl_checkbox_button_icon_unchecked_indeterminate__1 = 0x7f07001c
com.iapp.leochen.apkinjector:layout/abc_activity_chooser_view = 0x7f0b0006
com.iapp.leochen.apkinjector:drawable/mtrl_bottomsheet_drag_handle = 0x7f0700ba
com.iapp.leochen.apkinjector:styleable/ConstraintOverride = 0x7f12002a
com.iapp.leochen.apkinjector:string/path_password_eye = 0x7f10009c
com.iapp.leochen.apkinjector:drawable/$mtrl_checkbox_button_icon_unchecked_checked__2 = 0x7f07001a
com.iapp.leochen.apkinjector:drawable/$mtrl_checkbox_button_icon_unchecked_checked__0 = 0x7f070018
com.iapp.leochen.apkinjector:drawable/$mtrl_checkbox_button_icon_indeterminate_unchecked__0 = 0x7f070015
com.iapp.leochen.apkinjector:drawable/$mtrl_checkbox_button_checked_unchecked__0 = 0x7f07000d
com.iapp.leochen.apkinjector:styleable/MaterialToolbar = 0x7f12005e
com.iapp.leochen.apkinjector:drawable/$m3_avd_show_password__2 = 0x7f07000c
com.iapp.leochen.apkinjector:style/ThemeOverlay.MaterialComponents.Light = 0x7f1102d2
com.iapp.leochen.apkinjector:drawable/$m3_avd_show_password__0 = 0x7f07000a
com.iapp.leochen.apkinjector:styleable/AppCompatImageView = 0x7f12000e
com.iapp.leochen.apkinjector:animator/m3_chip_state_list_anim = 0x7f02000e
com.iapp.leochen.apkinjector:drawable/$m3_avd_hide_password__2 = 0x7f070009
com.iapp.leochen.apkinjector:attr/colorSurface = 0x7f030120
com.iapp.leochen.apkinjector:drawable/$ic_launcher_foreground__0 = 0x7f070006
com.iapp.leochen.apkinjector:drawable/$avd_hide_password__1 = 0x7f070001
com.iapp.leochen.apkinjector:style/Theme.MaterialComponents.Dialog.Bridge = 0x7f11025e
com.iapp.leochen.apkinjector:dimen/tooltip_y_offset_touch = 0x7f060322
com.iapp.leochen.apkinjector:dimen/tooltip_y_offset_non_touch = 0x7f060321
com.iapp.leochen.apkinjector:attr/switchTextAppearance = 0x7f030417
com.iapp.leochen.apkinjector:dimen/tooltip_margin = 0x7f06031d
com.iapp.leochen.apkinjector:dimen/m3_comp_top_app_bar_small_on_scroll_container_elevation = 0x7f0601ac
com.iapp.leochen.apkinjector:dimen/tooltip_horizontal_padding = 0x7f06031c
com.iapp.leochen.apkinjector:dimen/tooltip_corner_radius = 0x7f06031b
com.iapp.leochen.apkinjector:interpolator/m3_sys_motion_easing_linear = 0x7f0a000a
com.iapp.leochen.apkinjector:dimen/notification_top_pad_large_text = 0x7f06031a
com.iapp.leochen.apkinjector:macro/m3_comp_navigation_drawer_active_pressed_label_text_color = 0x7f0c0082
com.iapp.leochen.apkinjector:id/middle = 0x7f080123
com.iapp.leochen.apkinjector:dimen/notification_top_pad = 0x7f060319
com.iapp.leochen.apkinjector:color/m3_ref_palette_neutral60 = 0x7f05010f
com.iapp.leochen.apkinjector:drawable/abc_tab_indicator_material = 0x7f07006c
com.iapp.leochen.apkinjector:attr/layout_constraintCircleRadius = 0x7f03028a
com.iapp.leochen.apkinjector:dimen/notification_subtext_size = 0x7f060318
com.iapp.leochen.apkinjector:style/TextAppearance.M3.Sys.Typescale.LabelLarge = 0x7f1101dd
com.iapp.leochen.apkinjector:dimen/notification_small_icon_size_as_large = 0x7f060317
com.iapp.leochen.apkinjector:style/Widget.Material3.TabLayout.Secondary = 0x7f1103d9
com.iapp.leochen.apkinjector:dimen/notification_right_side_padding_top = 0x7f060315
com.iapp.leochen.apkinjector:id/material_hour_text_input = 0x7f080115
com.iapp.leochen.apkinjector:dimen/notification_right_icon_size = 0x7f060314
com.iapp.leochen.apkinjector:dimen/item_touch_helper_max_drag_scroll_per_frame = 0x7f06009a
com.iapp.leochen.apkinjector:dimen/notification_large_icon_width = 0x7f060311
com.iapp.leochen.apkinjector:color/material_dynamic_neutral_variant100 = 0x7f050231
com.iapp.leochen.apkinjector:dimen/notification_large_icon_height = 0x7f060310
com.iapp.leochen.apkinjector:dimen/notification_content_margin_start = 0x7f06030f
com.iapp.leochen.apkinjector:dimen/notification_big_circle_margin = 0x7f06030e
com.iapp.leochen.apkinjector:macro/m3_comp_checkbox_selected_container_color = 0x7f0c0006
com.iapp.leochen.apkinjector:attr/colorSwitchThumbNormal = 0x7f03012a
com.iapp.leochen.apkinjector:dimen/notification_action_icon_size = 0x7f06030c
com.iapp.leochen.apkinjector:dimen/mtrl_tooltip_padding = 0x7f06030a
com.iapp.leochen.apkinjector:style/ThemeOverlay.Material3.DayNight.BottomSheetDialog = 0x7f110297
com.iapp.leochen.apkinjector:attr/tabPaddingBottom = 0x7f030429
com.iapp.leochen.apkinjector:attr/mock_diagonalsColor = 0x7f030320
com.iapp.leochen.apkinjector:dimen/mtrl_textinput_start_icon_margin_end = 0x7f060304
com.iapp.leochen.apkinjector:style/TextAppearance.Material3.LabelMedium = 0x7f1101ef
com.iapp.leochen.apkinjector:color/m3_sys_color_on_primary_fixed = 0x7f0501ee
com.iapp.leochen.apkinjector:dimen/mtrl_textinput_counter_margin_start = 0x7f060301
com.iapp.leochen.apkinjector:dimen/mtrl_textinput_box_label_cutout_padding = 0x7f0602fe
com.iapp.leochen.apkinjector:dimen/mtrl_switch_track_width = 0x7f0602fb
com.iapp.leochen.apkinjector:dimen/mtrl_switch_track_height = 0x7f0602fa
com.iapp.leochen.apkinjector:dimen/mtrl_switch_thumb_size = 0x7f0602f9
com.iapp.leochen.apkinjector:string/material_timepicker_am = 0x7f100056
com.iapp.leochen.apkinjector:color/cardview_shadow_start_color = 0x7f05002f
com.iapp.leochen.apkinjector:dimen/mtrl_snackbar_padding_horizontal = 0x7f0602f5
com.iapp.leochen.apkinjector:dimen/mtrl_snackbar_message_margin_horizontal = 0x7f0602f4
com.iapp.leochen.apkinjector:dimen/mtrl_snackbar_background_corner_radius = 0x7f0602f1
com.iapp.leochen.apkinjector:id/overshoot = 0x7f08016c
com.iapp.leochen.apkinjector:dimen/mtrl_textinput_end_icon_margin_start = 0x7f060302
com.iapp.leochen.apkinjector:dimen/mtrl_snackbar_action_text_color_alpha = 0x7f0602f0
com.iapp.leochen.apkinjector:style/ThemeOverlay.Material3.AutoCompleteTextView = 0x7f110282
com.iapp.leochen.apkinjector:id/SHOW_PROGRESS = 0x7f08000a
com.iapp.leochen.apkinjector:dimen/mtrl_slider_widget_height = 0x7f0602ef
com.iapp.leochen.apkinjector:dimen/mtrl_slider_tick_radius = 0x7f0602ec
com.iapp.leochen.apkinjector:macro/m3_sys_color_light_surface_tint = 0x7f0c0176
com.iapp.leochen.apkinjector:dimen/mtrl_slider_thumb_radius = 0x7f0602ea
com.iapp.leochen.apkinjector:animator/m3_appbar_state_list_animator = 0x7f020009
com.iapp.leochen.apkinjector:color/m3_ref_palette_tertiary80 = 0x7f05014a
com.iapp.leochen.apkinjector:dimen/mtrl_slider_thumb_elevation = 0x7f0602e9
com.iapp.leochen.apkinjector:dimen/m3_btn_max_width = 0x7f0600d8
com.iapp.leochen.apkinjector:dimen/mtrl_slider_halo_radius = 0x7f0602e5
com.iapp.leochen.apkinjector:dimen/mtrl_shape_corner_size_medium_component = 0x7f0602e3
com.iapp.leochen.apkinjector:attr/tabUnboundedRipple = 0x7f030434
com.iapp.leochen.apkinjector:dimen/mtrl_shape_corner_size_large_component = 0x7f0602e2
com.iapp.leochen.apkinjector:dimen/mtrl_progress_indicator_full_rounded_corner_radius = 0x7f0602e0
com.iapp.leochen.apkinjector:dimen/mtrl_progress_circular_track_thickness_small = 0x7f0602df
com.iapp.leochen.apkinjector:style/Base.Theme.AppCompat.Light.Dialog.MinWidth = 0x7f110058
com.iapp.leochen.apkinjector:attr/drawableEndCompat = 0x7f030189
com.iapp.leochen.apkinjector:dimen/mtrl_progress_circular_track_thickness_medium = 0x7f0602de
com.iapp.leochen.apkinjector:style/Widget.Material3.MaterialCalendar.Day = 0x7f11039c
com.iapp.leochen.apkinjector:dimen/mtrl_progress_circular_size_small = 0x7f0602dc
com.iapp.leochen.apkinjector:dimen/compat_control_corner_material = 0x7f06005a
com.iapp.leochen.apkinjector:dimen/mtrl_progress_circular_size_extra_small = 0x7f0602da
com.iapp.leochen.apkinjector:dimen/mtrl_progress_circular_inset_medium = 0x7f0602d6
com.iapp.leochen.apkinjector:dimen/mtrl_progress_circular_inset_extra_small = 0x7f0602d5
com.iapp.leochen.apkinjector:dimen/mtrl_progress_circular_inset = 0x7f0602d4
com.iapp.leochen.apkinjector:styleable/SearchBar = 0x7f120079
com.iapp.leochen.apkinjector:dimen/mtrl_navigation_rail_margin = 0x7f0602d1
com.iapp.leochen.apkinjector:id/action_mode_bar = 0x7f080041
com.iapp.leochen.apkinjector:dimen/mtrl_navigation_rail_active_text_size = 0x7f0602cb
com.iapp.leochen.apkinjector:color/mtrl_text_btn_text_color_selector = 0x7f0502e8
com.iapp.leochen.apkinjector:dimen/mtrl_navigation_item_icon_padding = 0x7f0602c7
com.iapp.leochen.apkinjector:layout/item_file = 0x7f0b0032
com.iapp.leochen.apkinjector:dimen/mtrl_navigation_elevation = 0x7f0602c5
com.iapp.leochen.apkinjector:id/peekHeight = 0x7f080176
com.iapp.leochen.apkinjector:dimen/mtrl_min_touch_target_size = 0x7f0602c2
com.iapp.leochen.apkinjector:attr/clockIcon = 0x7f0300dd
com.iapp.leochen.apkinjector:dimen/mtrl_progress_circular_track_thickness_extra_small = 0x7f0602dd
com.iapp.leochen.apkinjector:dimen/m3_alert_dialog_elevation = 0x7f0600a0
com.iapp.leochen.apkinjector:dimen/mtrl_low_ripple_pressed_alpha = 0x7f0602c1
com.iapp.leochen.apkinjector:macro/m3_comp_time_picker_time_selector_selected_focus_state_layer_color = 0x7f0c0161
com.iapp.leochen.apkinjector:dimen/mtrl_low_ripple_default_alpha = 0x7f0602be
com.iapp.leochen.apkinjector:dimen/mtrl_high_ripple_hovered_alpha = 0x7f0602bc
com.iapp.leochen.apkinjector:style/ThemeOverlay.AppCompat.Dialog.Alert = 0x7f11027d
com.iapp.leochen.apkinjector:color/design_default_color_on_background = 0x7f050041
com.iapp.leochen.apkinjector:dimen/mtrl_fab_translation_z_pressed = 0x7f0602b9
com.iapp.leochen.apkinjector:dimen/mtrl_fab_translation_z_hovered_focused = 0x7f0602b8
com.iapp.leochen.apkinjector:drawable/abc_ic_commit_search_api_mtrl_alpha = 0x7f070041
com.iapp.leochen.apkinjector:dimen/mtrl_fab_min_touch_target = 0x7f0602b7
com.iapp.leochen.apkinjector:drawable/abc_btn_default_mtrl_shape = 0x7f070031
com.iapp.leochen.apkinjector:dimen/mtrl_fab_elevation = 0x7f0602b6
com.iapp.leochen.apkinjector:style/ShapeAppearance.M3.Sys.Shape.Corner.ExtraLarge = 0x7f110168
com.iapp.leochen.apkinjector:attr/actionMenuTextAppearance = 0x7f030010
com.iapp.leochen.apkinjector:dimen/mtrl_extended_fab_translation_z_base = 0x7f0602b3
com.iapp.leochen.apkinjector:dimen/mtrl_extended_fab_start_padding = 0x7f0602b0
com.iapp.leochen.apkinjector:dimen/mtrl_extended_fab_min_width = 0x7f0602af
com.iapp.leochen.apkinjector:dimen/mtrl_calendar_day_vertical_padding = 0x7f06027a
com.iapp.leochen.apkinjector:dimen/mtrl_extended_fab_min_height = 0x7f0602ae
com.iapp.leochen.apkinjector:dimen/mtrl_extended_fab_elevation = 0x7f0602a9
com.iapp.leochen.apkinjector:dimen/mtrl_extended_fab_disabled_translation_z = 0x7f0602a8
com.iapp.leochen.apkinjector:attr/tickVisible = 0x7f030493
com.iapp.leochen.apkinjector:drawable/mtrl_switch_track_decoration = 0x7f0700dd
com.iapp.leochen.apkinjector:dimen/mtrl_extended_fab_disabled_elevation = 0x7f0602a7
com.iapp.leochen.apkinjector:id/material_timepicker_view = 0x7f08011f
com.iapp.leochen.apkinjector:dimen/mtrl_exposed_dropdown_menu_popup_elevation = 0x7f0602a3
com.iapp.leochen.apkinjector:dimen/mtrl_chip_text_size = 0x7f0602a2
com.iapp.leochen.apkinjector:attr/homeAsUpIndicator = 0x7f03022b
com.iapp.leochen.apkinjector:dimen/mtrl_card_elevation = 0x7f06029f
com.iapp.leochen.apkinjector:attr/forceApplySystemWindowInsetTop = 0x7f030207
com.iapp.leochen.apkinjector:dimen/mtrl_card_dragged_z = 0x7f06029e
com.iapp.leochen.apkinjector:dimen/mtrl_calendar_year_vertical_padding = 0x7f060299
com.iapp.leochen.apkinjector:styleable/TextInputLayout = 0x7f12008e
com.iapp.leochen.apkinjector:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Header = 0x7f11003e
com.iapp.leochen.apkinjector:dimen/mtrl_calendar_year_height = 0x7f060297
com.iapp.leochen.apkinjector:dimen/mtrl_calendar_year_corner = 0x7f060296
com.iapp.leochen.apkinjector:id/accessibility_custom_action_30 = 0x7f080029
com.iapp.leochen.apkinjector:id/accessibility_custom_action_20 = 0x7f08001e
com.iapp.leochen.apkinjector:id/SHOW_PATH = 0x7f080009
com.iapp.leochen.apkinjector:drawable/$avd_hide_password__0 = 0x7f070000
com.iapp.leochen.apkinjector:dimen/mtrl_calendar_title_baseline_to_top = 0x7f060294
com.iapp.leochen.apkinjector:id/grouping = 0x7f0800e1
com.iapp.leochen.apkinjector:dimen/mtrl_calendar_text_input_padding_top = 0x7f060293
com.iapp.leochen.apkinjector:color/m3_ref_palette_secondary80 = 0x7f05013d
com.iapp.leochen.apkinjector:drawable/abc_text_cursor_material = 0x7f07006e
com.iapp.leochen.apkinjector:dimen/m3_chip_elevated_elevation = 0x7f0600f7
com.iapp.leochen.apkinjector:dimen/mtrl_calendar_selection_text_baseline_to_bottom_fullscreen = 0x7f060291
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.MaterialCalendar.Day = 0x7f110422
com.iapp.leochen.apkinjector:dimen/mtrl_calendar_navigation_top_padding = 0x7f06028d
com.iapp.leochen.apkinjector:style/Platform.V21.AppCompat = 0x7f110140
com.iapp.leochen.apkinjector:dimen/mtrl_calendar_month_vertical_padding = 0x7f06028a
com.iapp.leochen.apkinjector:dimen/mtrl_calendar_maximum_default_fullscreen_minor_axis = 0x7f060288
com.iapp.leochen.apkinjector:macro/m3_comp_outlined_text_field_hover_outline_color = 0x7f0c00be
com.iapp.leochen.apkinjector:dimen/mtrl_calendar_landscape_header_width = 0x7f060287
com.iapp.leochen.apkinjector:dimen/mtrl_calendar_header_text_padding = 0x7f060284
com.iapp.leochen.apkinjector:macro/m3_comp_filled_autocomplete_menu_container_color = 0x7f0c0041
com.iapp.leochen.apkinjector:drawable/ic_m3_chip_close = 0x7f07009d
com.iapp.leochen.apkinjector:drawable/$m3_avd_hide_password__0 = 0x7f070007
com.iapp.leochen.apkinjector:attr/extendMotionSpec = 0x7f0301c2
com.iapp.leochen.apkinjector:dimen/mtrl_calendar_header_content_padding_fullscreen = 0x7f06027f
com.iapp.leochen.apkinjector:dimen/m3_comp_navigation_rail_container_elevation = 0x7f060147
com.iapp.leochen.apkinjector:drawable/ic_m3_chip_checked_circle = 0x7f07009c
com.iapp.leochen.apkinjector:dimen/m3_comp_navigation_rail_icon_size = 0x7f06014b
com.iapp.leochen.apkinjector:dimen/mtrl_calendar_header_content_padding = 0x7f06027e
com.iapp.leochen.apkinjector:color/m3_sys_color_dark_on_error = 0x7f050161
com.iapp.leochen.apkinjector:dimen/mtrl_calendar_day_today_stroke = 0x7f060279
com.iapp.leochen.apkinjector:dimen/mtrl_calendar_day_height = 0x7f060277
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.BottomSheet.Modal = 0x7f1103ff
com.iapp.leochen.apkinjector:dimen/mtrl_calendar_action_padding = 0x7f060273
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.CircularProgressIndicator.ExtraSmall = 0x7f110414
com.iapp.leochen.apkinjector:style/Base.Theme.AppCompat.Light = 0x7f110053
com.iapp.leochen.apkinjector:dimen/mtrl_btn_text_btn_padding_right = 0x7f06026e
com.iapp.leochen.apkinjector:macro/m3_comp_icon_button_selected_icon_color = 0x7f0c0059
com.iapp.leochen.apkinjector:dimen/mtrl_btn_text_size = 0x7f06026f
com.iapp.leochen.apkinjector:style/Base.V21.Theme.AppCompat.Dialog = 0x7f1100a3
com.iapp.leochen.apkinjector:dimen/m3_sys_motion_easing_linear_control_x2 = 0x7f06020b
com.iapp.leochen.apkinjector:dimen/mtrl_btn_text_btn_padding_left = 0x7f06026d
com.iapp.leochen.apkinjector:dimen/mtrl_btn_stroke_size = 0x7f06026b
com.iapp.leochen.apkinjector:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle = 0x7f110033
com.iapp.leochen.apkinjector:id/easeInOut = 0x7f0800b1
com.iapp.leochen.apkinjector:dimen/mtrl_btn_pressed_z = 0x7f060269
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_neutral_variant60 = 0x7f0500c3
com.iapp.leochen.apkinjector:dimen/mtrl_btn_max_width = 0x7f060264
com.iapp.leochen.apkinjector:attr/waveVariesBy = 0x7f0304df
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_light_primary = 0x7f0501b1
com.iapp.leochen.apkinjector:dimen/mtrl_navigation_rail_text_size = 0x7f0602d3
com.iapp.leochen.apkinjector:attr/imagePanX = 0x7f03023c
com.iapp.leochen.apkinjector:dimen/mtrl_btn_inset = 0x7f060262
com.iapp.leochen.apkinjector:id/textinput_prefix_text = 0x7f0801e7
com.iapp.leochen.apkinjector:attr/imagePanY = 0x7f03023d
com.iapp.leochen.apkinjector:dimen/mtrl_btn_hovered_z = 0x7f06025f
com.iapp.leochen.apkinjector:dimen/mtrl_btn_focused_z = 0x7f06025e
com.iapp.leochen.apkinjector:dimen/mtrl_btn_elevation = 0x7f06025d
com.iapp.leochen.apkinjector:dimen/mtrl_btn_disabled_z = 0x7f06025c
com.iapp.leochen.apkinjector:drawable/mtrl_popupmenu_background_overlay = 0x7f0700d1
com.iapp.leochen.apkinjector:dimen/mtrl_btn_disabled_elevation = 0x7f06025b
com.iapp.leochen.apkinjector:styleable/MaterialTextView = 0x7f12005c
com.iapp.leochen.apkinjector:dimen/mtrl_btn_corner_radius = 0x7f060259
com.iapp.leochen.apkinjector:macro/m3_comp_menu_list_item_selected_container_color = 0x7f0c005e
com.iapp.leochen.apkinjector:attr/titleMarginTop = 0x7f03049f
com.iapp.leochen.apkinjector:dimen/mtrl_bottomappbar_height = 0x7f060258
com.iapp.leochen.apkinjector:attr/materialAlertDialogButtonSpacerVisibility = 0x7f0302de
com.iapp.leochen.apkinjector:attr/motionDurationExtraLong3 = 0x7f030329
com.iapp.leochen.apkinjector:dimen/mtrl_bottomappbar_fab_cradle_rounded_corner_radius = 0x7f060256
com.iapp.leochen.apkinjector:dimen/mtrl_bottomappbar_fab_cradle_margin = 0x7f060255
com.iapp.leochen.apkinjector:dimen/mtrl_calendar_selection_text_baseline_to_bottom = 0x7f060290
com.iapp.leochen.apkinjector:dimen/mtrl_badge_with_text_size = 0x7f060252
com.iapp.leochen.apkinjector:dimen/mtrl_badge_toolbar_action_menu_item_horizontal_offset = 0x7f060250
com.iapp.leochen.apkinjector:macro/m3_comp_navigation_drawer_active_hover_label_text_color = 0x7f0c007c
com.iapp.leochen.apkinjector:dimen/mtrl_calendar_content_padding = 0x7f060275
com.iapp.leochen.apkinjector:dimen/mtrl_badge_text_size = 0x7f06024f
com.iapp.leochen.apkinjector:style/Theme.AppCompat.Light.NoActionBar = 0x7f110221
com.iapp.leochen.apkinjector:string/material_motion_easing_standard = 0x7f100052
com.iapp.leochen.apkinjector:color/material_harmonized_color_on_error = 0x7f05026c
com.iapp.leochen.apkinjector:dimen/mtrl_badge_text_horizontal_edge_offset = 0x7f06024e
com.iapp.leochen.apkinjector:dimen/mtrl_badge_size = 0x7f06024d
com.iapp.leochen.apkinjector:attr/motionEffect_end = 0x7f030344
com.iapp.leochen.apkinjector:attr/flow_firstHorizontalBias = 0x7f0301e8
com.iapp.leochen.apkinjector:dimen/mtrl_alert_dialog_background_inset_start = 0x7f060248
com.iapp.leochen.apkinjector:dimen/material_time_picker_minimum_screen_width = 0x7f060245
com.iapp.leochen.apkinjector:dimen/material_textinput_max_width = 0x7f060242
com.iapp.leochen.apkinjector:style/Base.V14.Theme.MaterialComponents.Dialog.Bridge = 0x7f110095
com.iapp.leochen.apkinjector:string/mtrl_picker_text_input_day_abbr = 0x7f100089
com.iapp.leochen.apkinjector:dimen/material_helper_text_font_1_3_padding_horizontal = 0x7f06023e
com.iapp.leochen.apkinjector:macro/m3_comp_time_picker_clock_dial_color = 0x7f0c014c
com.iapp.leochen.apkinjector:animator/mtrl_btn_unelevated_state_list_anim = 0x7f020016
com.iapp.leochen.apkinjector:dimen/material_helper_text_default_padding_top = 0x7f06023d
com.iapp.leochen.apkinjector:dimen/material_font_2_0_box_collapsed_padding_top = 0x7f06023c
com.iapp.leochen.apkinjector:color/m3_sys_color_dark_surface_container_high = 0x7f050174
com.iapp.leochen.apkinjector:attr/colorError = 0x7f0300fa
com.iapp.leochen.apkinjector:dimen/material_filled_edittext_font_1_3_padding_top = 0x7f060238
com.iapp.leochen.apkinjector:dimen/material_emphasis_medium = 0x7f060236
com.iapp.leochen.apkinjector:style/Base.DialogWindowTitleBackground.AppCompat = 0x7f110012
com.iapp.leochen.apkinjector:dimen/material_emphasis_high_type = 0x7f060235
com.iapp.leochen.apkinjector:style/Widget.Material3.AutoCompleteTextView.FilledBox = 0x7f110342
com.iapp.leochen.apkinjector:dimen/material_emphasis_disabled_background = 0x7f060234
com.iapp.leochen.apkinjector:drawable/ic_home = 0x7f070096
com.iapp.leochen.apkinjector:dimen/m3_sys_motion_easing_legacy_control_x2 = 0x7f060203
com.iapp.leochen.apkinjector:dimen/material_emphasis_disabled = 0x7f060233
com.iapp.leochen.apkinjector:dimen/material_divider_thickness = 0x7f060232
com.iapp.leochen.apkinjector:style/RtlUnderlay.Widget.AppCompat.ActionButton = 0x7f110154
com.iapp.leochen.apkinjector:dimen/material_clock_size = 0x7f06022f
com.iapp.leochen.apkinjector:attr/defaultScrollFlagsEnabled = 0x7f030173
com.iapp.leochen.apkinjector:dimen/material_clock_period_toggle_vertical_gap = 0x7f06022d
com.iapp.leochen.apkinjector:dimen/material_clock_period_toggle_horizontal_gap = 0x7f06022c
com.iapp.leochen.apkinjector:dimen/material_clock_hand_center_dot_radius = 0x7f060227
com.iapp.leochen.apkinjector:dimen/material_clock_display_width = 0x7f060224
com.iapp.leochen.apkinjector:id/material_clock_face = 0x7f08010f
com.iapp.leochen.apkinjector:attr/textAppearanceHeadline6 = 0x7f030449
com.iapp.leochen.apkinjector:color/m3_sys_color_dark_secondary_container = 0x7f050170
com.iapp.leochen.apkinjector:dimen/material_bottom_sheet_max_width = 0x7f060221
com.iapp.leochen.apkinjector:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Header.Text = 0x7f1102d8
com.iapp.leochen.apkinjector:dimen/m3_toolbar_text_size_title = 0x7f060220
com.iapp.leochen.apkinjector:dimen/m3_timepicker_window_elevation = 0x7f06021f
com.iapp.leochen.apkinjector:dimen/m3_sys_state_pressed_state_layer_opacity = 0x7f06021d
com.iapp.leochen.apkinjector:style/Widget.AppCompat.Toolbar.Button.Navigation = 0x7f110330
com.iapp.leochen.apkinjector:dimen/m3_sys_motion_easing_standard_decelerate_control_x1 = 0x7f060216
com.iapp.leochen.apkinjector:id/pin = 0x7f080179
com.iapp.leochen.apkinjector:dimen/m3_sys_motion_easing_standard_control_y1 = 0x7f060214
com.iapp.leochen.apkinjector:integer/m3_sys_motion_duration_extra_long2 = 0x7f090010
com.iapp.leochen.apkinjector:dimen/m3_sys_motion_easing_standard_accelerate_control_x2 = 0x7f06020f
com.iapp.leochen.apkinjector:drawable/$mtrl_checkbox_button_checked_unchecked__1 = 0x7f07000e
com.iapp.leochen.apkinjector:dimen/m3_sys_motion_easing_standard_accelerate_control_x1 = 0x7f06020e
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.Light.ActionBar.Solid = 0x7f11041e
com.iapp.leochen.apkinjector:attr/deltaPolarRadius = 0x7f030176
com.iapp.leochen.apkinjector:dimen/m3_sys_motion_easing_legacy_decelerate_control_y2 = 0x7f060209
com.iapp.leochen.apkinjector:style/Base.Widget.MaterialComponents.AutoCompleteTextView = 0x7f110112
com.iapp.leochen.apkinjector:dimen/m3_sys_motion_easing_legacy_decelerate_control_y1 = 0x7f060208
com.iapp.leochen.apkinjector:dimen/m3_sys_motion_easing_legacy_decelerate_control_x2 = 0x7f060207
com.iapp.leochen.apkinjector:dimen/m3_sys_motion_easing_legacy_decelerate_control_x1 = 0x7f060206
com.iapp.leochen.apkinjector:dimen/m3_sys_motion_easing_legacy_control_y2 = 0x7f060205
com.iapp.leochen.apkinjector:dimen/m3_sys_motion_easing_legacy_accelerate_control_y2 = 0x7f060201
com.iapp.leochen.apkinjector:attr/colorSecondaryFixedDim = 0x7f03011e
com.iapp.leochen.apkinjector:dimen/m3_sys_motion_easing_emphasized_decelerate_control_y2 = 0x7f0601fd
com.iapp.leochen.apkinjector:id/neverCompleteToStart = 0x7f080150
com.iapp.leochen.apkinjector:dimen/m3_sys_motion_easing_emphasized_decelerate_control_x2 = 0x7f0601fb
com.iapp.leochen.apkinjector:dimen/mtrl_calendar_header_selection_line_height = 0x7f060283
com.iapp.leochen.apkinjector:dimen/mtrl_navigation_rail_elevation = 0x7f0602ce
com.iapp.leochen.apkinjector:dimen/m3_sys_motion_easing_emphasized_decelerate_control_x1 = 0x7f0601fa
com.iapp.leochen.apkinjector:dimen/m3_sys_motion_easing_emphasized_accelerate_control_y1 = 0x7f0601f8
com.iapp.leochen.apkinjector:color/m3_ref_palette_neutral12 = 0x7f050105
com.iapp.leochen.apkinjector:dimen/m3_sys_motion_easing_emphasized_accelerate_control_x2 = 0x7f0601f7
com.iapp.leochen.apkinjector:style/Theme.MaterialComponents.Light.DarkActionBar = 0x7f110267
com.iapp.leochen.apkinjector:attr/endIconScaleType = 0x7f0301a7
com.iapp.leochen.apkinjector:color/m3_sys_color_dark_on_surface_variant = 0x7f050168
com.iapp.leochen.apkinjector:color/material_dynamic_color_dark_error_container = 0x7f05021b
com.iapp.leochen.apkinjector:dimen/m3_sys_elevation_level2 = 0x7f0601f2
com.iapp.leochen.apkinjector:styleable/Transform = 0x7f120092
com.iapp.leochen.apkinjector:attr/subtitle = 0x7f03040a
com.iapp.leochen.apkinjector:dimen/m3_snackbar_margin = 0x7f0601ef
com.iapp.leochen.apkinjector:attr/floatingActionButtonTertiaryStyle = 0x7f0301e7
com.iapp.leochen.apkinjector:dimen/m3_small_fab_max_image_size = 0x7f0601ec
com.iapp.leochen.apkinjector:dimen/m3_comp_primary_navigation_tab_inactive_focus_state_layer_opacity = 0x7f06015e
com.iapp.leochen.apkinjector:drawable/m3_avd_hide_password = 0x7f0700a5
com.iapp.leochen.apkinjector:id/parentRelative = 0x7f080171
com.iapp.leochen.apkinjector:dimen/m3_slider_thumb_elevation = 0x7f0601eb
com.iapp.leochen.apkinjector:dimen/m3_simple_item_color_selected_alpha = 0x7f0601ea
com.iapp.leochen.apkinjector:macro/m3_comp_radio_button_disabled_selected_icon_color = 0x7f0c00d6
com.iapp.leochen.apkinjector:drawable/btn_radio_on_mtrl = 0x7f070080
com.iapp.leochen.apkinjector:style/Base.Widget.AppCompat.SeekBar = 0x7f1100f5
com.iapp.leochen.apkinjector:dimen/m3_simple_item_color_hovered_alpha = 0x7f0601e9
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.Button.TextButton.Icon = 0x7f110408
com.iapp.leochen.apkinjector:style/Theme.MaterialComponents.Dialog.MinWidth.Bridge = 0x7f110262
com.iapp.leochen.apkinjector:dimen/mtrl_switch_thumb_icon_size = 0x7f0602f8
com.iapp.leochen.apkinjector:color/m3_sys_color_dark_surface = 0x7f050171
com.iapp.leochen.apkinjector:dimen/m3_side_sheet_width = 0x7f0601e8
com.iapp.leochen.apkinjector:dimen/m3_side_sheet_margin_detached = 0x7f0601e5
com.iapp.leochen.apkinjector:id/dimensions = 0x7f0800a0
com.iapp.leochen.apkinjector:id/cos = 0x7f08008a
com.iapp.leochen.apkinjector:dimen/mtrl_textinput_box_corner_radius_medium = 0x7f0602fc
com.iapp.leochen.apkinjector:attr/springBoundary = 0x7f0303e6
com.iapp.leochen.apkinjector:dimen/m3_searchview_elevation = 0x7f0601e3
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.TextInputEditText.OutlinedBox.Dense = 0x7f110450
com.iapp.leochen.apkinjector:dimen/m3_searchbar_text_size = 0x7f0601e1
com.iapp.leochen.apkinjector:macro/m3_comp_radio_button_unselected_focus_state_layer_color = 0x7f0c00e0
com.iapp.leochen.apkinjector:macro/m3_comp_extended_fab_primary_container_shape = 0x7f0c002d
com.iapp.leochen.apkinjector:color/m3_calendar_item_disabled_text = 0x7f050069
com.iapp.leochen.apkinjector:dimen/m3_searchbar_margin_vertical = 0x7f0601dd
com.iapp.leochen.apkinjector:attr/materialCircleRadius = 0x7f0302f9
com.iapp.leochen.apkinjector:dimen/m3_searchbar_margin_horizontal = 0x7f0601dc
com.iapp.leochen.apkinjector:dimen/m3_searchbar_elevation = 0x7f0601da
com.iapp.leochen.apkinjector:dimen/m3_ripple_selectable_pressed_alpha = 0x7f0601d9
com.iapp.leochen.apkinjector:id/scrollView = 0x7f080193
com.iapp.leochen.apkinjector:dimen/m3_ripple_hovered_alpha = 0x7f0601d7
com.iapp.leochen.apkinjector:dimen/m3_ripple_focused_alpha = 0x7f0601d6
com.iapp.leochen.apkinjector:macro/m3_comp_time_picker_time_selector_selected_hover_state_layer_color = 0x7f0c0162
com.iapp.leochen.apkinjector:attr/verticalOffset = 0x7f0304d0
com.iapp.leochen.apkinjector:dimen/m3_ripple_default_alpha = 0x7f0601d5
com.iapp.leochen.apkinjector:string/mtrl_chip_close_icon_content_description = 0x7f100069
com.iapp.leochen.apkinjector:id/navigation_header_container = 0x7f08014d
com.iapp.leochen.apkinjector:dimen/m3_navigation_rail_item_padding_bottom_with_large_font = 0x7f0601d1
com.iapp.leochen.apkinjector:dimen/cardview_default_elevation = 0x7f060053
com.iapp.leochen.apkinjector:dimen/m3_navigation_rail_item_padding_bottom = 0x7f0601d0
com.iapp.leochen.apkinjector:style/Widget.Material3.CircularProgressIndicator.ExtraSmall = 0x7f110374
com.iapp.leochen.apkinjector:id/open_search_view_divider = 0x7f080160
com.iapp.leochen.apkinjector:dimen/m3_navigation_rail_item_active_indicator_height = 0x7f0601cc
com.iapp.leochen.apkinjector:style/ShapeAppearanceOverlay.MaterialComponents.FloatingActionButton = 0x7f11018f
com.iapp.leochen.apkinjector:dimen/m3_navigation_rail_icon_size = 0x7f0601cb
com.iapp.leochen.apkinjector:attr/liftOnScrollColor = 0x7f0302c0
com.iapp.leochen.apkinjector:dimen/m3_navigation_rail_default_width = 0x7f0601c9
com.iapp.leochen.apkinjector:dimen/m3_comp_primary_navigation_tab_with_icon_icon_size = 0x7f060161
com.iapp.leochen.apkinjector:dimen/m3_navigation_menu_headline_horizontal_padding = 0x7f0601c8
com.iapp.leochen.apkinjector:dimen/m3_navigation_item_vertical_padding = 0x7f0601c6
com.iapp.leochen.apkinjector:style/ShapeAppearance.M3.Comp.Switch.Track.Shape = 0x7f110166
com.iapp.leochen.apkinjector:dimen/m3_navigation_item_shape_inset_end = 0x7f0601c3
com.iapp.leochen.apkinjector:dimen/m3_navigation_item_shape_inset_bottom = 0x7f0601c2
com.iapp.leochen.apkinjector:color/m3_timepicker_display_ripple_color = 0x7f05020d
com.iapp.leochen.apkinjector:attr/showAnimationBehavior = 0x7f0303c9
com.iapp.leochen.apkinjector:drawable/$m3_avd_show_password__1 = 0x7f07000b
com.iapp.leochen.apkinjector:style/Widget.Material3.SideSheet.Modal.Detached = 0x7f1103cf
com.iapp.leochen.apkinjector:dimen/m3_menu_elevation = 0x7f0601bc
com.iapp.leochen.apkinjector:drawable/abc_tab_indicator_mtrl_alpha = 0x7f07006d
com.iapp.leochen.apkinjector:style/Widget.AppCompat.Light.ActivityChooserView = 0x7f11030f
com.iapp.leochen.apkinjector:style/Base.TextAppearance.AppCompat.Headline = 0x7f11001f
com.iapp.leochen.apkinjector:id/mtrl_calendar_selection_frame = 0x7f080132
com.iapp.leochen.apkinjector:attr/nestedScrollFlags = 0x7f03035b
com.iapp.leochen.apkinjector:dimen/m3_large_fab_max_image_size = 0x7f0601b9
com.iapp.leochen.apkinjector:dimen/m3_appbar_size_compact = 0x7f0600a9
com.iapp.leochen.apkinjector:dimen/m3_fab_translation_z_pressed = 0x7f0601b8
com.iapp.leochen.apkinjector:dimen/m3_fab_translation_z_hovered_focused = 0x7f0601b7
com.iapp.leochen.apkinjector:layout/mtrl_picker_header_selection_text = 0x7f0b0060
com.iapp.leochen.apkinjector:dimen/m3_fab_corner_size = 0x7f0601b6
com.iapp.leochen.apkinjector:drawable/abc_list_selector_holo_dark = 0x7f070057
com.iapp.leochen.apkinjector:dimen/m3_divider_heavy_thickness = 0x7f0601ae
com.iapp.leochen.apkinjector:string/material_motion_easing_linear = 0x7f100051
com.iapp.leochen.apkinjector:drawable/$mtrl_checkbox_button_unchecked_checked__1 = 0x7f07001f
com.iapp.leochen.apkinjector:dimen/m3_datepicker_elevation = 0x7f0601ad
com.iapp.leochen.apkinjector:id/transition_layout_save = 0x7f0801f8
com.iapp.leochen.apkinjector:dimen/m3_comp_top_app_bar_small_container_height = 0x7f0601ab
com.iapp.leochen.apkinjector:drawable/mtrl_ic_checkbox_checked = 0x7f0700cb
com.iapp.leochen.apkinjector:dimen/m3_comp_top_app_bar_medium_container_height = 0x7f0601a9
com.iapp.leochen.apkinjector:style/Theme.Material3.DayNight.Dialog.MinWidth = 0x7f110235
com.iapp.leochen.apkinjector:drawable/abc_btn_switch_to_on_mtrl_00012 = 0x7f070037
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.MaterialCalendar.HeaderLayout.Fullscreen = 0x7f11042d
com.iapp.leochen.apkinjector:style/Widget.Material3.MaterialCalendar.HeaderLayout.Fullscreen = 0x7f1103a6
com.iapp.leochen.apkinjector:dimen/m3_comp_top_app_bar_large_container_height = 0x7f0601a8
com.iapp.leochen.apkinjector:attr/hintTextColor = 0x7f03022a
com.iapp.leochen.apkinjector:color/material_dynamic_secondary10 = 0x7f05024a
com.iapp.leochen.apkinjector:dimen/mtrl_calendar_navigation_height = 0x7f06028c
com.iapp.leochen.apkinjector:macro/m3_comp_navigation_drawer_active_icon_color = 0x7f0c007e
com.iapp.leochen.apkinjector:id/wrap_content_constrained = 0x7f080215
com.iapp.leochen.apkinjector:id/layout = 0x7f0800fd
com.iapp.leochen.apkinjector:attr/navigationRailStyle = 0x7f030359
com.iapp.leochen.apkinjector:dimen/m3_comp_time_picker_period_selector_hover_state_layer_opacity = 0x7f0601a2
com.iapp.leochen.apkinjector:dimen/mtrl_high_ripple_default_alpha = 0x7f0602ba
com.iapp.leochen.apkinjector:string/mtrl_checkbox_button_icon_path_name = 0x7f100061
com.iapp.leochen.apkinjector:color/material_grey_900 = 0x7f050269
com.iapp.leochen.apkinjector:dimen/m3_comp_time_picker_period_selector_focus_state_layer_opacity = 0x7f0601a1
com.iapp.leochen.apkinjector:id/forever = 0x7f0800d6
com.iapp.leochen.apkinjector:dimen/m3_comp_time_input_time_input_field_focus_outline_width = 0x7f06019f
com.iapp.leochen.apkinjector:drawable/abc_ic_ab_back_material = 0x7f07003e
com.iapp.leochen.apkinjector:dimen/mtrl_extended_fab_translation_z_hovered_focused = 0x7f0602b4
com.iapp.leochen.apkinjector:dimen/m3_comp_switch_unselected_pressed_state_layer_opacity = 0x7f06019b
com.iapp.leochen.apkinjector:attr/displayOptions = 0x7f03017b
com.iapp.leochen.apkinjector:dimen/m3_comp_switch_selected_pressed_state_layer_opacity = 0x7f060196
com.iapp.leochen.apkinjector:macro/m3_comp_switch_unselected_hover_state_layer_color = 0x7f0c0137
com.iapp.leochen.apkinjector:dimen/m3_comp_switch_selected_hover_state_layer_opacity = 0x7f060195
com.iapp.leochen.apkinjector:dimen/m3_comp_switch_selected_focus_state_layer_opacity = 0x7f060194
com.iapp.leochen.apkinjector:style/ShapeAppearance.M3.Comp.SearchBar.Avatar.Shape = 0x7f110160
com.iapp.leochen.apkinjector:color/m3_dynamic_default_color_primary_text = 0x7f050081
com.iapp.leochen.apkinjector:dimen/m3_comp_switch_disabled_unselected_handle_opacity = 0x7f060192
com.iapp.leochen.apkinjector:macro/m3_comp_top_app_bar_large_headline_type = 0x7f0c016c
com.iapp.leochen.apkinjector:macro/m3_comp_navigation_rail_label_text_type = 0x7f0c009f
com.iapp.leochen.apkinjector:dimen/m3_comp_switch_disabled_selected_handle_opacity = 0x7f06018f
com.iapp.leochen.apkinjector:dimen/m3_comp_suggestion_chip_flat_container_elevation = 0x7f06018c
com.iapp.leochen.apkinjector:dimen/abc_text_size_menu_header_material = 0x7f06004a
com.iapp.leochen.apkinjector:drawable/navigation_empty_icon = 0x7f0700df
com.iapp.leochen.apkinjector:attr/scaleFromTextSize = 0x7f0303ae
com.iapp.leochen.apkinjector:dimen/m3_comp_snackbar_container_elevation = 0x7f060189
com.iapp.leochen.apkinjector:dimen/m3_comp_slider_inactive_track_height = 0x7f060187
com.iapp.leochen.apkinjector:attr/actionMenuTextColor = 0x7f030011
com.iapp.leochen.apkinjector:attr/iconifiedByDefault = 0x7f030238
com.iapp.leochen.apkinjector:dimen/m3_comp_slider_disabled_inactive_track_opacity = 0x7f060186
com.iapp.leochen.apkinjector:dimen/m3_comp_slider_disabled_handle_opacity = 0x7f060185
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.MaterialCalendar.Year.Today = 0x7f110437
com.iapp.leochen.apkinjector:dimen/m3_comp_slider_active_handle_leading_space = 0x7f060182
com.iapp.leochen.apkinjector:dimen/m3_comp_slider_active_handle_height = 0x7f060181
com.iapp.leochen.apkinjector:dimen/m3_comp_sheet_side_docked_standard_container_elevation = 0x7f060180
com.iapp.leochen.apkinjector:dimen/m3_comp_sheet_side_docked_modal_container_elevation = 0x7f06017f
com.iapp.leochen.apkinjector:dimen/m3_comp_sheet_side_docked_container_width = 0x7f06017e
com.iapp.leochen.apkinjector:macro/m3_comp_time_picker_period_selector_unselected_hover_state_layer_color = 0x7f0c015b
com.iapp.leochen.apkinjector:color/material_personalized_color_surface_dim = 0x7f05029e
com.iapp.leochen.apkinjector:dimen/m3_comp_sheet_bottom_docked_modal_container_elevation = 0x7f06017c
com.iapp.leochen.apkinjector:attr/titleCollapseMode = 0x7f030499
com.iapp.leochen.apkinjector:dimen/mtrl_btn_z = 0x7f060270
com.iapp.leochen.apkinjector:dimen/m3_comp_sheet_bottom_docked_drag_handle_width = 0x7f06017b
com.iapp.leochen.apkinjector:attr/bottomSheetDragHandleStyle = 0x7f03007c
com.iapp.leochen.apkinjector:dimen/m3_comp_sheet_bottom_docked_drag_handle_height = 0x7f06017a
com.iapp.leochen.apkinjector:color/mtrl_tabs_legacy_text_color_selector = 0x7f0502e6
com.iapp.leochen.apkinjector:dimen/m3_comp_secondary_navigation_tab_pressed_state_layer_opacity = 0x7f060179
com.iapp.leochen.apkinjector:style/Widget.Material3.BottomNavigationView = 0x7f11034c
com.iapp.leochen.apkinjector:style/ThemeOverlay.Material3.Search = 0x7f1102b5
com.iapp.leochen.apkinjector:attr/badgeWithTextRadius = 0x7f03005f
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_dark_surface_variant = 0x7f05019b
com.iapp.leochen.apkinjector:dimen/m3_comp_secondary_navigation_tab_hover_state_layer_opacity = 0x7f060178
com.iapp.leochen.apkinjector:dimen/m3_comp_secondary_navigation_tab_focus_state_layer_opacity = 0x7f060177
com.iapp.leochen.apkinjector:dimen/m3_comp_secondary_navigation_tab_active_indicator_height = 0x7f060176
com.iapp.leochen.apkinjector:attr/marginTopSystemWindowInsets = 0x7f0302dc
com.iapp.leochen.apkinjector:dimen/m3_comp_search_view_full_screen_header_container_height = 0x7f060175
com.iapp.leochen.apkinjector:styleable/KeyPosition = 0x7f120045
com.iapp.leochen.apkinjector:id/accessibility_custom_action_12 = 0x7f080015
com.iapp.leochen.apkinjector:macro/m3_comp_filter_chip_label_text_type = 0x7f0c0058
com.iapp.leochen.apkinjector:color/material_dynamic_neutral99 = 0x7f05022e
com.iapp.leochen.apkinjector:dimen/m3_comp_search_view_docked_header_container_height = 0x7f060174
com.iapp.leochen.apkinjector:dimen/m3_btn_icon_btn_padding_left = 0x7f0600d1
com.iapp.leochen.apkinjector:dimen/m3_comp_search_bar_pressed_state_layer_opacity = 0x7f060172
com.iapp.leochen.apkinjector:style/Widget.AppCompat.Button.Colored = 0x7f1102f7
com.iapp.leochen.apkinjector:attr/defaultQueryHint = 0x7f030172
com.iapp.leochen.apkinjector:dimen/m3_comp_search_bar_hover_state_layer_opacity = 0x7f060171
com.iapp.leochen.apkinjector:dimen/m3_comp_search_bar_avatar_size = 0x7f06016e
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_neutral6 = 0x7f0500aa
com.iapp.leochen.apkinjector:dimen/m3_comp_radio_button_selected_pressed_state_layer_opacity = 0x7f060169
com.iapp.leochen.apkinjector:dimen/m3_comp_radio_button_selected_focus_state_layer_opacity = 0x7f060167
com.iapp.leochen.apkinjector:dimen/m3_comp_radio_button_disabled_selected_icon_opacity = 0x7f060165
com.iapp.leochen.apkinjector:styleable/NavigationBarView = 0x7f12006b
com.iapp.leochen.apkinjector:dimen/abc_dialog_padding_top_material = 0x7f060025
com.iapp.leochen.apkinjector:dimen/m3_comp_progress_indicator_track_thickness = 0x7f060164
com.iapp.leochen.apkinjector:macro/m3_comp_switch_unselected_focus_track_color = 0x7f0c0132
com.iapp.leochen.apkinjector:dimen/m3_comp_primary_navigation_tab_active_indicator_height = 0x7f06015c
com.iapp.leochen.apkinjector:dimen/m3_comp_primary_navigation_tab_active_hover_state_layer_opacity = 0x7f06015b
com.iapp.leochen.apkinjector:drawable/btn_checkbox_unchecked_to_checked_mtrl_animation = 0x7f07007d
com.iapp.leochen.apkinjector:integer/m3_sys_shape_corner_small_corner_family = 0x7f090025
com.iapp.leochen.apkinjector:dimen/m3_comp_primary_navigation_tab_active_focus_state_layer_opacity = 0x7f06015a
com.iapp.leochen.apkinjector:attr/trackCornerRadius = 0x7f0304b7
com.iapp.leochen.apkinjector:dimen/m3_comp_outlined_text_field_disabled_supporting_text_opacity = 0x7f060157
com.iapp.leochen.apkinjector:dimen/m3_comp_outlined_text_field_disabled_label_text_opacity = 0x7f060156
com.iapp.leochen.apkinjector:style/TextAppearance.AppCompat.Widget.Button.Colored = 0x7f1101bc
com.iapp.leochen.apkinjector:dimen/m3_comp_outlined_icon_button_unselected_outline_width = 0x7f060154
com.iapp.leochen.apkinjector:id/title = 0x7f0801ea
com.iapp.leochen.apkinjector:dimen/m3_comp_outlined_card_outline_width = 0x7f060153
com.iapp.leochen.apkinjector:dimen/m3_comp_outlined_card_icon_size = 0x7f060152
com.iapp.leochen.apkinjector:style/ShapeAppearance.M3.Sys.Shape.Corner.Large = 0x7f11016b
com.iapp.leochen.apkinjector:dimen/mtrl_progress_circular_size = 0x7f0602d9
com.iapp.leochen.apkinjector:style/Base.Widget.AppCompat.CompoundButton.Switch = 0x7f1100d8
com.iapp.leochen.apkinjector:integer/mtrl_switch_thumb_pressed_duration = 0x7f090039
com.iapp.leochen.apkinjector:dimen/m3_comp_navigation_rail_focus_state_layer_opacity = 0x7f060149
com.iapp.leochen.apkinjector:dimen/m3_comp_navigation_rail_container_width = 0x7f060148
com.iapp.leochen.apkinjector:anim/m3_motion_fade_exit = 0x7f010024
com.iapp.leochen.apkinjector:dimen/m3_navigation_item_icon_padding = 0x7f0601c1
com.iapp.leochen.apkinjector:attr/colorSecondary = 0x7f03011b
com.iapp.leochen.apkinjector:dimen/m3_comp_navigation_rail_active_indicator_height = 0x7f060145
com.iapp.leochen.apkinjector:dimen/m3_comp_navigation_drawer_icon_size = 0x7f060141
com.iapp.leochen.apkinjector:dimen/m3_comp_navigation_drawer_focus_state_layer_opacity = 0x7f06013f
com.iapp.leochen.apkinjector:dimen/m3_comp_navigation_bar_pressed_state_layer_opacity = 0x7f06013d
com.iapp.leochen.apkinjector:style/Widget.Material3.SideSheet.Detached = 0x7f1103cd
com.iapp.leochen.apkinjector:dimen/m3_comp_navigation_bar_icon_size = 0x7f06013c
com.iapp.leochen.apkinjector:attr/materialIconButtonFilledStyle = 0x7f0302fe
com.iapp.leochen.apkinjector:dimen/material_clock_number_text_size = 0x7f06022a
com.iapp.leochen.apkinjector:color/switch_thumb_material_light = 0x7f050301
com.iapp.leochen.apkinjector:dimen/m3_comp_navigation_bar_focus_state_layer_opacity = 0x7f06013a
com.iapp.leochen.apkinjector:style/ThemeOverlay.Material3.HarmonizedColors.Empty = 0x7f1102a8
com.iapp.leochen.apkinjector:id/radio = 0x7f080180
com.iapp.leochen.apkinjector:dimen/m3_comp_navigation_bar_active_indicator_width = 0x7f060137
com.iapp.leochen.apkinjector:style/Theme.Material3.DynamicColors.Dark.NoActionBar = 0x7f11023a
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_light_on_tertiary = 0x7f0501ad
com.iapp.leochen.apkinjector:dimen/m3_comp_top_app_bar_small_container_elevation = 0x7f0601aa
com.iapp.leochen.apkinjector:attr/tooltipFrameBackground = 0x7f0304ac
com.iapp.leochen.apkinjector:dimen/m3_comp_navigation_bar_active_indicator_height = 0x7f060136
com.iapp.leochen.apkinjector:attr/colorTertiaryContainer = 0x7f03012c
com.iapp.leochen.apkinjector:dimen/m3_comp_input_chip_with_avatar_avatar_size = 0x7f060133
com.iapp.leochen.apkinjector:style/Widget.Material3.Button.OutlinedButton.Icon = 0x7f11035a
com.iapp.leochen.apkinjector:dimen/m3_comp_filter_chip_flat_container_elevation = 0x7f06012d
com.iapp.leochen.apkinjector:dimen/m3_comp_filled_text_field_disabled_active_indicator_opacity = 0x7f06012a
com.iapp.leochen.apkinjector:attr/contentPaddingRight = 0x7f030144
com.iapp.leochen.apkinjector:dimen/material_clock_period_toggle_height = 0x7f06022b
com.iapp.leochen.apkinjector:dimen/m3_comp_filled_card_pressed_state_layer_opacity = 0x7f060129
com.iapp.leochen.apkinjector:dimen/m3_comp_filled_card_dragged_state_layer_opacity = 0x7f060125
com.iapp.leochen.apkinjector:dimen/m3_comp_filled_button_with_icon_icon_size = 0x7f060123
com.iapp.leochen.apkinjector:style/Widget.AppCompat.Light.ActionBar.TabText = 0x7f110307
com.iapp.leochen.apkinjector:dimen/m3_comp_filled_button_container_elevation = 0x7f060122
com.iapp.leochen.apkinjector:dimen/m3_comp_filled_autocomplete_menu_container_elevation = 0x7f060121
com.iapp.leochen.apkinjector:attr/checkedIconSize = 0x7f0300b9
com.iapp.leochen.apkinjector:dimen/mtrl_tooltip_minWidth = 0x7f060309
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.ProgressIndicator = 0x7f110444
com.iapp.leochen.apkinjector:dimen/m3_comp_fab_primary_pressed_container_elevation = 0x7f06011d
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.MaterialButtonToggleGroup = 0x7f110420
com.iapp.leochen.apkinjector:dimen/mtrl_extended_fab_end_padding_icon = 0x7f0602ab
com.iapp.leochen.apkinjector:dimen/m3_comp_fab_primary_large_icon_size = 0x7f06011c
com.iapp.leochen.apkinjector:macro/m3_comp_search_bar_trailing_icon_color = 0x7f0c00f0
com.iapp.leochen.apkinjector:dimen/m3_comp_fab_primary_container_height = 0x7f060116
com.iapp.leochen.apkinjector:dimen/m3_comp_fab_primary_container_elevation = 0x7f060115
com.iapp.leochen.apkinjector:style/Theme.AppCompat.NoActionBar = 0x7f110222
com.iapp.leochen.apkinjector:dimen/m3_comp_extended_fab_primary_pressed_state_layer_opacity = 0x7f060114
com.iapp.leochen.apkinjector:dimen/m3_comp_extended_fab_primary_pressed_container_elevation = 0x7f060113
com.iapp.leochen.apkinjector:dimen/m3_comp_extended_fab_primary_hover_state_layer_opacity = 0x7f060111
com.iapp.leochen.apkinjector:dimen/m3_comp_extended_fab_primary_container_height = 0x7f06010d
com.iapp.leochen.apkinjector:dimen/m3_comp_extended_fab_primary_container_elevation = 0x7f06010c
com.iapp.leochen.apkinjector:dimen/m3_comp_elevated_card_icon_size = 0x7f06010b
com.iapp.leochen.apkinjector:dimen/m3_comp_elevated_button_disabled_container_elevation = 0x7f060109
com.iapp.leochen.apkinjector:color/material_dynamic_secondary60 = 0x7f050250
com.iapp.leochen.apkinjector:dimen/m3_comp_divider_thickness = 0x7f060107
com.iapp.leochen.apkinjector:dimen/m3_comp_primary_navigation_tab_inactive_pressed_state_layer_opacity = 0x7f060160
com.iapp.leochen.apkinjector:color/m3_dynamic_dark_primary_text_disable_only = 0x7f050080
com.iapp.leochen.apkinjector:dimen/m3_comp_date_picker_modal_range_selection_header_container_height = 0x7f060106
com.iapp.leochen.apkinjector:dimen/m3_comp_date_picker_modal_header_container_height = 0x7f060105
com.iapp.leochen.apkinjector:dimen/m3_comp_date_picker_modal_date_today_container_outline_width = 0x7f060104
com.iapp.leochen.apkinjector:color/m3_timepicker_secondary_text_button_ripple_color = 0x7f05020f
com.iapp.leochen.apkinjector:dimen/m3_comp_bottom_app_bar_container_elevation = 0x7f060101
com.iapp.leochen.apkinjector:string/mtrl_checkbox_state_description_checked = 0x7f100066
com.iapp.leochen.apkinjector:id/title_template = 0x7f0801ec
com.iapp.leochen.apkinjector:dimen/m3_comp_badge_size = 0x7f060100
com.iapp.leochen.apkinjector:style/Widget.Material3.Slider.Label = 0x7f1103d1
com.iapp.leochen.apkinjector:style/ThemeOverlay.AppCompat.Dialog = 0x7f11027c
com.iapp.leochen.apkinjector:dimen/m3_comp_assist_chip_flat_outline_width = 0x7f0600fd
com.iapp.leochen.apkinjector:dimen/abc_seekbar_track_progress_height_material = 0x7f060039
com.iapp.leochen.apkinjector:dimen/m3_comp_assist_chip_flat_container_elevation = 0x7f0600fc
com.iapp.leochen.apkinjector:dimen/m3_comp_assist_chip_container_height = 0x7f0600fa
com.iapp.leochen.apkinjector:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog = 0x7f1102d4
com.iapp.leochen.apkinjector:dimen/mtrl_extended_fab_icon_size = 0x7f0602ac
com.iapp.leochen.apkinjector:dimen/m3_chip_hovered_translation_z = 0x7f0600f8
com.iapp.leochen.apkinjector:dimen/m3_chip_dragged_translation_z = 0x7f0600f6
com.iapp.leochen.apkinjector:animator/fragment_close_enter = 0x7f020003
com.iapp.leochen.apkinjector:dimen/design_bottom_navigation_active_item_max_width = 0x7f06005f
com.iapp.leochen.apkinjector:dimen/m3_chip_disabled_translation_z = 0x7f0600f5
com.iapp.leochen.apkinjector:style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Small = 0x7f1101a4
com.iapp.leochen.apkinjector:dimen/m3_chip_corner_size = 0x7f0600f4
com.iapp.leochen.apkinjector:style/Widget.Material3.ExtendedFloatingActionButton.Surface = 0x7f110389
com.iapp.leochen.apkinjector:color/m3_sys_color_dark_on_secondary_container = 0x7f050166
com.iapp.leochen.apkinjector:color/mtrl_filled_stroke_color = 0x7f0502d0
com.iapp.leochen.apkinjector:dimen/m3_carousel_small_item_size_min = 0x7f0600f2
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.TabLayout.Colored = 0x7f11044b
com.iapp.leochen.apkinjector:style/Base.Widget.MaterialComponents.TextView = 0x7f11011f
com.iapp.leochen.apkinjector:color/m3_ref_palette_neutral99 = 0x7f050119
com.iapp.leochen.apkinjector:drawable/abc_btn_check_to_on_mtrl_000 = 0x7f07002e
com.iapp.leochen.apkinjector:animator/m3_card_state_list_anim = 0x7f02000d
com.iapp.leochen.apkinjector:dimen/m3_carousel_small_item_default_corner_size = 0x7f0600f0
com.iapp.leochen.apkinjector:drawable/ic_arrow_back_black_24 = 0x7f070088
com.iapp.leochen.apkinjector:attr/keylines = 0x7f03026e
com.iapp.leochen.apkinjector:attr/layout_marginBaseline = 0x7f0302b9
com.iapp.leochen.apkinjector:color/m3_slider_thumb_color_legacy = 0x7f050157
com.iapp.leochen.apkinjector:dimen/m3_carousel_debug_keyline_width = 0x7f0600ed
com.iapp.leochen.apkinjector:dimen/mtrl_navigation_item_horizontal_padding = 0x7f0602c6
com.iapp.leochen.apkinjector:dimen/design_snackbar_action_text_color_alpha = 0x7f06007f
com.iapp.leochen.apkinjector:dimen/m3_card_elevated_hovered_z = 0x7f0600e9
com.iapp.leochen.apkinjector:style/TextAppearance.AppCompat.Body2 = 0x7f110196
com.iapp.leochen.apkinjector:drawable/design_ic_visibility = 0x7f070083
com.iapp.leochen.apkinjector:dimen/m3_card_elevated_elevation = 0x7f0600e8
com.iapp.leochen.apkinjector:styleable/LinearLayoutCompat = 0x7f120049
com.iapp.leochen.apkinjector:dimen/abc_dialog_list_padding_top_no_title = 0x7f060021
com.iapp.leochen.apkinjector:dimen/m3_card_elevation = 0x7f0600ea
com.iapp.leochen.apkinjector:style/Widget.Material3.Button.OutlinedButton = 0x7f110359
com.iapp.leochen.apkinjector:dimen/m3_card_elevated_disabled_z = 0x7f0600e6
com.iapp.leochen.apkinjector:dimen/mtrl_textinput_outline_box_expanded_padding = 0x7f060303
com.iapp.leochen.apkinjector:dimen/m3_btn_text_btn_padding_left = 0x7f0600e0
com.iapp.leochen.apkinjector:color/design_dark_default_color_on_primary = 0x7f050036
com.iapp.leochen.apkinjector:dimen/m3_btn_padding_top = 0x7f0600dc
com.iapp.leochen.apkinjector:id/autoCompleteToEnd = 0x7f080059
com.iapp.leochen.apkinjector:dimen/m3_btn_padding_left = 0x7f0600da
com.iapp.leochen.apkinjector:attr/isMaterialTheme = 0x7f03024d
com.iapp.leochen.apkinjector:dimen/m3_btn_inset = 0x7f0600d7
com.iapp.leochen.apkinjector:dimen/m3_btn_icon_only_default_padding = 0x7f0600d3
com.iapp.leochen.apkinjector:style/Base.Animation.AppCompat.DropDownUp = 0x7f11000e
com.iapp.leochen.apkinjector:dimen/hint_alpha_material_dark = 0x7f060096
com.iapp.leochen.apkinjector:color/button_material_light = 0x7f050029
com.iapp.leochen.apkinjector:dimen/m3_btn_dialog_btn_spacing = 0x7f0600cc
com.iapp.leochen.apkinjector:attr/ensureMinTouchTargetSize = 0x7f0301ac
com.iapp.leochen.apkinjector:dimen/m3_bottomappbar_height = 0x7f0600c9
com.iapp.leochen.apkinjector:dimen/m3_btn_translation_z_base = 0x7f0600e2
com.iapp.leochen.apkinjector:dimen/m3_bottomappbar_fab_end_margin = 0x7f0600c8
com.iapp.leochen.apkinjector:attr/actionModeTheme = 0x7f03001f
com.iapp.leochen.apkinjector:dimen/m3_bottomappbar_fab_cradle_vertical_offset = 0x7f0600c7
com.iapp.leochen.apkinjector:dimen/m3_bottom_sheet_modal_elevation = 0x7f0600c4
com.iapp.leochen.apkinjector:styleable/AppBarLayout = 0x7f12000a
com.iapp.leochen.apkinjector:dimen/m3_bottom_sheet_drag_handle_bottom_padding = 0x7f0600c2
com.iapp.leochen.apkinjector:drawable/$mtrl_checkbox_button_icon_unchecked_indeterminate__2 = 0x7f07001d
com.iapp.leochen.apkinjector:dimen/m3_bottom_nav_item_padding_top = 0x7f0600c0
com.iapp.leochen.apkinjector:dimen/m3_bottom_nav_item_active_indicator_margin_horizontal = 0x7f0600bd
com.iapp.leochen.apkinjector:dimen/m3_badge_with_text_vertical_padding = 0x7f0600bb
com.iapp.leochen.apkinjector:dimen/m3_badge_with_text_offset = 0x7f0600b8
com.iapp.leochen.apkinjector:dimen/m3_badge_size = 0x7f0600b5
com.iapp.leochen.apkinjector:id/customPanel = 0x7f080091
com.iapp.leochen.apkinjector:dimen/m3_badge_horizontal_offset = 0x7f0600b3
com.iapp.leochen.apkinjector:dimen/m3_comp_navigation_rail_hover_state_layer_opacity = 0x7f06014a
com.iapp.leochen.apkinjector:dimen/m3_back_progress_side_container_max_scale_x_distance_shrink = 0x7f0600b1
com.iapp.leochen.apkinjector:style/Widget.AppCompat.ProgressBar = 0x7f110320
com.iapp.leochen.apkinjector:macro/m3_comp_text_button_pressed_state_layer_color = 0x7f0c0146
com.iapp.leochen.apkinjector:drawable/abc_ratingbar_small_material = 0x7f07005d
com.iapp.leochen.apkinjector:style/Widget.Material3.TextInputLayout.OutlinedBox.Dense = 0x7f1103e3
com.iapp.leochen.apkinjector:dimen/mtrl_navigation_item_shape_vertical_margin = 0x7f0602ca
com.iapp.leochen.apkinjector:dimen/m3_back_progress_side_container_max_scale_x_distance_grow = 0x7f0600b0
com.iapp.leochen.apkinjector:attr/layout_goneMarginBaseline = 0x7f0302b0
com.iapp.leochen.apkinjector:dimen/m3_back_progress_main_container_max_translation_y = 0x7f0600ae
com.iapp.leochen.apkinjector:dimen/m3_back_progress_bottom_container_max_scale_y_distance = 0x7f0600ad
com.iapp.leochen.apkinjector:macro/m3_comp_outlined_text_field_error_supporting_text_color = 0x7f0c00b7
com.iapp.leochen.apkinjector:dimen/m3_appbar_scrim_height_trigger = 0x7f0600a6
com.iapp.leochen.apkinjector:dimen/m3_appbar_expanded_title_margin_horizontal = 0x7f0600a5
com.iapp.leochen.apkinjector:macro/m3_comp_date_picker_modal_range_selection_active_indicator_container_color = 0x7f0c0019
com.iapp.leochen.apkinjector:layout/abc_screen_content_include = 0x7f0b0014
com.iapp.leochen.apkinjector:dimen/design_snackbar_text_size = 0x7f060088
com.iapp.leochen.apkinjector:dimen/mtrl_calendar_year_width = 0x7f06029a
com.iapp.leochen.apkinjector:dimen/m3_alert_dialog_title_bottom_margin = 0x7f0600a3
com.iapp.leochen.apkinjector:layout/abc_alert_dialog_title_material = 0x7f0b000a
com.iapp.leochen.apkinjector:attr/boxCornerRadiusTopStart = 0x7f030084
com.iapp.leochen.apkinjector:dimen/m3_alert_dialog_icon_margin = 0x7f0600a1
com.iapp.leochen.apkinjector:dimen/m3_alert_dialog_corner_size = 0x7f06009f
com.iapp.leochen.apkinjector:id/groups = 0x7f0800e2
com.iapp.leochen.apkinjector:dimen/item_touch_helper_swipe_escape_max_velocity = 0x7f06009b
com.iapp.leochen.apkinjector:dimen/hint_pressed_alpha_material_light = 0x7f060099
com.iapp.leochen.apkinjector:dimen/hint_alpha_material_light = 0x7f060097
com.iapp.leochen.apkinjector:string/password_toggle_content_description = 0x7f10009b
com.iapp.leochen.apkinjector:anim/abc_grow_fade_in_from_bottom = 0x7f010002
com.iapp.leochen.apkinjector:dimen/highlight_alpha_material_colored = 0x7f060093
com.iapp.leochen.apkinjector:dimen/fastscroll_minimum_range = 0x7f060092
com.iapp.leochen.apkinjector:styleable/PropertySet = 0x7f120072
com.iapp.leochen.apkinjector:attr/chipIconTint = 0x7f0300c5
com.iapp.leochen.apkinjector:dimen/design_textinput_caption_translate_y = 0x7f06008d
com.iapp.leochen.apkinjector:style/ThemeOverlay.MaterialComponents.Toolbar.Popup.Primary = 0x7f1102e5
com.iapp.leochen.apkinjector:dimen/design_tab_text_size_2line = 0x7f06008c
com.iapp.leochen.apkinjector:drawable/btn_radio_off_mtrl = 0x7f07007e
com.iapp.leochen.apkinjector:dimen/design_tab_text_size = 0x7f06008b
com.iapp.leochen.apkinjector:id/elastic = 0x7f0800b7
com.iapp.leochen.apkinjector:color/m3_dynamic_hint_foreground = 0x7f050084
com.iapp.leochen.apkinjector:color/m3_ref_palette_secondary70 = 0x7f05013c
com.iapp.leochen.apkinjector:dimen/design_snackbar_padding_vertical_2lines = 0x7f060087
com.iapp.leochen.apkinjector:dimen/design_snackbar_min_width = 0x7f060084
com.iapp.leochen.apkinjector:attr/colorTertiaryFixed = 0x7f03012d
com.iapp.leochen.apkinjector:dimen/design_snackbar_max_width = 0x7f060083
com.iapp.leochen.apkinjector:attr/indicatorColor = 0x7f030242
com.iapp.leochen.apkinjector:dimen/design_snackbar_elevation = 0x7f060081
com.iapp.leochen.apkinjector:style/ThemeOverlay.MaterialComponents.Toolbar.Surface = 0x7f1102e7
com.iapp.leochen.apkinjector:dimen/design_snackbar_background_corner_radius = 0x7f060080
com.iapp.leochen.apkinjector:dimen/m3_comp_switch_unselected_focus_state_layer_opacity = 0x7f060199
com.iapp.leochen.apkinjector:dimen/design_snackbar_action_inline_max_width = 0x7f06007e
com.iapp.leochen.apkinjector:dimen/design_navigation_max_width = 0x7f06007b
com.iapp.leochen.apkinjector:styleable/PopupWindow = 0x7f120070
com.iapp.leochen.apkinjector:drawable/notify_panel_notification_icon_bg = 0x7f0700ec
com.iapp.leochen.apkinjector:attr/elevationOverlayColor = 0x7f03019e
com.iapp.leochen.apkinjector:dimen/design_navigation_item_horizontal_padding = 0x7f060078
com.iapp.leochen.apkinjector:dimen/design_fab_size_normal = 0x7f060072
com.iapp.leochen.apkinjector:macro/m3_comp_radio_button_selected_pressed_icon_color = 0x7f0c00dd
com.iapp.leochen.apkinjector:id/textStart = 0x7f0801de
com.iapp.leochen.apkinjector:dimen/m3_searchbar_text_margin_start_no_navigation_icon = 0x7f0601e0
com.iapp.leochen.apkinjector:style/Theme.MaterialComponents.Light.Dialog.Alert = 0x7f11026a
com.iapp.leochen.apkinjector:interpolator/mtrl_linear_out_slow_in = 0x7f0a0011
com.iapp.leochen.apkinjector:id/right_side = 0x7f080188
com.iapp.leochen.apkinjector:dimen/design_bottom_sheet_peek_height_min = 0x7f06006d
com.iapp.leochen.apkinjector:dimen/disabled_alpha_material_light = 0x7f06008f
com.iapp.leochen.apkinjector:dimen/m3_badge_with_text_vertical_offset = 0x7f0600ba
com.iapp.leochen.apkinjector:attr/showAsAction = 0x7f0303ca
com.iapp.leochen.apkinjector:dimen/design_bottom_sheet_modal_elevation = 0x7f06006c
com.iapp.leochen.apkinjector:style/MaterialAlertDialog.MaterialComponents.Title.Icon.CenterStacked = 0x7f110132
com.iapp.leochen.apkinjector:dimen/design_bottom_sheet_elevation = 0x7f06006b
com.iapp.leochen.apkinjector:attr/buttonIconDimen = 0x7f030092
com.iapp.leochen.apkinjector:animator/mtrl_card_state_list_anim = 0x7f020017
com.iapp.leochen.apkinjector:color/m3_ref_palette_neutral_variant100 = 0x7f05011c
com.iapp.leochen.apkinjector:dimen/design_bottom_navigation_text_size = 0x7f06006a
com.iapp.leochen.apkinjector:anim/slide_in_left = 0x7f01002c
com.iapp.leochen.apkinjector:dimen/design_bottom_navigation_shadow_height = 0x7f060069
com.iapp.leochen.apkinjector:style/Animation.AppCompat.DropDownUp = 0x7f110003
com.iapp.leochen.apkinjector:id/navigation_bar_item_labels_group = 0x7f08014a
com.iapp.leochen.apkinjector:dimen/design_bottom_navigation_margin = 0x7f060068
com.iapp.leochen.apkinjector:dimen/m3_searchbar_outlined_stroke_width = 0x7f0601de
com.iapp.leochen.apkinjector:dimen/design_appbar_elevation = 0x7f06005e
com.iapp.leochen.apkinjector:dimen/compat_button_padding_horizontal_material = 0x7f060058
com.iapp.leochen.apkinjector:dimen/compat_button_inset_vertical_material = 0x7f060057
com.iapp.leochen.apkinjector:dimen/compat_button_inset_horizontal_material = 0x7f060056
com.iapp.leochen.apkinjector:dimen/clock_face_margin_start = 0x7f060055
com.iapp.leochen.apkinjector:attr/boxCornerRadiusBottomStart = 0x7f030082
com.iapp.leochen.apkinjector:dimen/cardview_compat_inset_shadow = 0x7f060052
com.iapp.leochen.apkinjector:style/Base.Theme.MaterialComponents.Dialog = 0x7f110069
com.iapp.leochen.apkinjector:dimen/abc_text_size_subtitle_material_toolbar = 0x7f06004e
com.iapp.leochen.apkinjector:dimen/m3_bottom_sheet_elevation = 0x7f0600c3
com.iapp.leochen.apkinjector:drawable/abc_text_select_handle_middle_mtrl = 0x7f070070
com.iapp.leochen.apkinjector:dimen/abc_text_size_subhead_material = 0x7f06004d
com.iapp.leochen.apkinjector:color/material_dynamic_primary10 = 0x7f05023d
com.iapp.leochen.apkinjector:dimen/abc_text_size_small_material = 0x7f06004c
com.iapp.leochen.apkinjector:dimen/mtrl_calendar_bottom_padding = 0x7f060274
com.iapp.leochen.apkinjector:dimen/abc_text_size_menu_material = 0x7f06004b
com.iapp.leochen.apkinjector:dimen/abc_text_size_medium_material = 0x7f060049
com.iapp.leochen.apkinjector:dimen/abc_text_size_display_2_material = 0x7f060044
com.iapp.leochen.apkinjector:layout/abc_action_bar_up_container = 0x7f0b0001
com.iapp.leochen.apkinjector:dimen/abc_text_size_body_2_material = 0x7f060040
com.iapp.leochen.apkinjector:dimen/abc_text_size_body_1_material = 0x7f06003f
com.iapp.leochen.apkinjector:dimen/abc_switch_padding = 0x7f06003e
com.iapp.leochen.apkinjector:attr/colorOnPrimaryFixedVariant = 0x7f030104
com.iapp.leochen.apkinjector:attr/textAppearanceBody2 = 0x7f03043b
com.iapp.leochen.apkinjector:dimen/mtrl_calendar_dialog_background_inset = 0x7f06027d
com.iapp.leochen.apkinjector:dimen/m3_comp_outlined_button_outline_width = 0x7f06014f
com.iapp.leochen.apkinjector:dimen/abc_star_medium = 0x7f06003c
com.iapp.leochen.apkinjector:dimen/abc_star_big = 0x7f06003b
com.iapp.leochen.apkinjector:macro/m3_comp_navigation_bar_active_hover_icon_color = 0x7f0c0062
com.iapp.leochen.apkinjector:dimen/m3_comp_time_picker_time_selector_focus_state_layer_opacity = 0x7f0601a5
com.iapp.leochen.apkinjector:dimen/material_clock_display_height = 0x7f060222
com.iapp.leochen.apkinjector:id/ifRoom = 0x7f0800ed
com.iapp.leochen.apkinjector:attr/hideMotionSpec = 0x7f030223
com.iapp.leochen.apkinjector:dimen/m3_back_progress_side_container_max_scale_y_distance = 0x7f0600b2
com.iapp.leochen.apkinjector:dimen/abc_select_dialog_padding_start_material = 0x7f06003a
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.MaterialCalendar.Day.Today = 0x7f110425
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_neutral80 = 0x7f0500ad
com.iapp.leochen.apkinjector:dimen/abc_seekbar_track_background_height_material = 0x7f060038
com.iapp.leochen.apkinjector:dimen/abc_progress_bar_height_material = 0x7f060035
com.iapp.leochen.apkinjector:dimen/abc_panel_menu_list_width = 0x7f060034
com.iapp.leochen.apkinjector:attr/closeIconEnabled = 0x7f0300e0
com.iapp.leochen.apkinjector:attr/grid_orientation = 0x7f030211
com.iapp.leochen.apkinjector:dimen/m3_comp_outlined_text_field_outline_width = 0x7f060159
com.iapp.leochen.apkinjector:dimen/abc_list_item_height_material = 0x7f060031
com.iapp.leochen.apkinjector:dimen/abc_list_item_height_large_material = 0x7f060030
com.iapp.leochen.apkinjector:animator/design_appbar_state_list_animator = 0x7f020000
com.iapp.leochen.apkinjector:dimen/abc_floating_window_z = 0x7f06002f
com.iapp.leochen.apkinjector:dimen/abc_dropdownitem_icon_width = 0x7f060029
com.iapp.leochen.apkinjector:dimen/abc_disabled_alpha_material_light = 0x7f060028
com.iapp.leochen.apkinjector:string/searchview_clear_text_content_description = 0x7f1000a2
com.iapp.leochen.apkinjector:dimen/abc_dialog_padding_material = 0x7f060024
com.iapp.leochen.apkinjector:dimen/design_navigation_icon_padding = 0x7f060076
com.iapp.leochen.apkinjector:dimen/abc_dialog_min_width_minor = 0x7f060023
com.iapp.leochen.apkinjector:style/ThemeOverlay.Material3.NavigationRailView = 0x7f1102b2
com.iapp.leochen.apkinjector:attr/lStar = 0x7f03026f
com.iapp.leochen.apkinjector:dimen/abc_dialog_min_width_major = 0x7f060022
com.iapp.leochen.apkinjector:styleable/ActionMenuItemView = 0x7f120002
com.iapp.leochen.apkinjector:style/Base.Widget.AppCompat.TextView.SpinnerItem = 0x7f1100fa
com.iapp.leochen.apkinjector:dimen/abc_dialog_fixed_width_major = 0x7f06001e
com.iapp.leochen.apkinjector:macro/m3_comp_switch_selected_hover_state_layer_color = 0x7f0c0127
com.iapp.leochen.apkinjector:layout/design_layout_snackbar_include = 0x7f0b0021
com.iapp.leochen.apkinjector:color/material_dynamic_tertiary99 = 0x7f050262
com.iapp.leochen.apkinjector:dimen/abc_dialog_fixed_height_major = 0x7f06001c
com.iapp.leochen.apkinjector:id/center_vertical = 0x7f080073
com.iapp.leochen.apkinjector:dimen/abc_control_padding_material = 0x7f06001a
com.iapp.leochen.apkinjector:drawable/abc_btn_switch_to_on_mtrl_00001 = 0x7f070036
com.iapp.leochen.apkinjector:layout/mtrl_picker_header_title_text = 0x7f0b0061
com.iapp.leochen.apkinjector:dimen/abc_control_inset_material = 0x7f060019
com.iapp.leochen.apkinjector:dimen/abc_control_corner_material = 0x7f060018
com.iapp.leochen.apkinjector:style/Widget.Material3.MaterialCalendar.Item = 0x7f1103ab
com.iapp.leochen.apkinjector:color/dim_foreground_material_dark = 0x7f050058
com.iapp.leochen.apkinjector:dimen/abc_config_prefDialogWidth = 0x7f060017
com.iapp.leochen.apkinjector:dimen/abc_button_padding_vertical_material = 0x7f060015
com.iapp.leochen.apkinjector:dimen/abc_button_padding_horizontal_material = 0x7f060014
com.iapp.leochen.apkinjector:dimen/m3_comp_switch_unselected_hover_state_layer_opacity = 0x7f06019a
com.iapp.leochen.apkinjector:dimen/abc_button_inset_vertical_material = 0x7f060013
com.iapp.leochen.apkinjector:dimen/abc_button_inset_horizontal_material = 0x7f060012
com.iapp.leochen.apkinjector:id/useLogo = 0x7f080203
com.iapp.leochen.apkinjector:dimen/abc_alert_dialog_button_dimen = 0x7f060011
com.iapp.leochen.apkinjector:style/Theme.MaterialComponents.DayNight.Dialog.FixedSize = 0x7f110254
com.iapp.leochen.apkinjector:drawable/$mtrl_checkbox_button_icon_unchecked_checked__1 = 0x7f070019
com.iapp.leochen.apkinjector:layout/abc_activity_chooser_view_list_item = 0x7f0b0007
com.iapp.leochen.apkinjector:attr/behavior_expandedOffset = 0x7f03006a
com.iapp.leochen.apkinjector:color/mtrl_navigation_bar_colored_item_tint = 0x7f0502d2
com.iapp.leochen.apkinjector:dimen/abc_action_button_min_width_overflow_material = 0x7f06000f
com.iapp.leochen.apkinjector:id/dragAnticlockwise = 0x7f0800a7
com.iapp.leochen.apkinjector:drawable/abc_popup_background_mtrl_mult = 0x7f07005a
com.iapp.leochen.apkinjector:attr/layoutDuringTransition = 0x7f030278
com.iapp.leochen.apkinjector:attr/expandedTitleMarginBottom = 0x7f0301bc
com.iapp.leochen.apkinjector:dimen/abc_action_button_min_width_material = 0x7f06000e
com.iapp.leochen.apkinjector:macro/m3_comp_time_input_time_input_field_label_text_color = 0x7f0c0149
com.iapp.leochen.apkinjector:id/action_bar_activity_content = 0x7f080035
com.iapp.leochen.apkinjector:dimen/mtrl_transition_shared_axis_slide_distance = 0x7f06030b
com.iapp.leochen.apkinjector:color/m3_chip_background_color = 0x7f050071
com.iapp.leochen.apkinjector:dimen/abc_action_bar_subtitle_bottom_margin_material = 0x7f06000b
com.iapp.leochen.apkinjector:macro/m3_comp_date_picker_modal_header_supporting_text_color = 0x7f0c0017
com.iapp.leochen.apkinjector:dimen/abc_action_bar_stacked_max_height = 0x7f060009
com.iapp.leochen.apkinjector:dimen/m3_back_progress_main_container_min_edge_gap = 0x7f0600af
com.iapp.leochen.apkinjector:dimen/abc_action_bar_overflow_padding_start_material = 0x7f060008
com.iapp.leochen.apkinjector:style/Theme.AppCompat.Empty = 0x7f11021a
com.iapp.leochen.apkinjector:attr/collapsedTitleGravity = 0x7f0300ea
com.iapp.leochen.apkinjector:dimen/abc_action_bar_icon_vertical_padding_material = 0x7f060006
com.iapp.leochen.apkinjector:dimen/abc_action_bar_default_padding_start_material = 0x7f060004
com.iapp.leochen.apkinjector:attr/placeholderTextAppearance = 0x7f030384
com.iapp.leochen.apkinjector:dimen/abc_action_bar_content_inset_with_nav = 0x7f060001
com.iapp.leochen.apkinjector:dimen/m3_comp_extended_fab_primary_focus_container_elevation = 0x7f06010e
com.iapp.leochen.apkinjector:attr/iconTint = 0x7f030236
com.iapp.leochen.apkinjector:dimen/abc_action_bar_content_inset_material = 0x7f060000
com.iapp.leochen.apkinjector:color/primary_material_light = 0x7f0502f3
com.iapp.leochen.apkinjector:dimen/m3_sys_motion_easing_standard_decelerate_control_x2 = 0x7f060217
com.iapp.leochen.apkinjector:style/TextAppearance.Design.Placeholder = 0x7f1101cf
com.iapp.leochen.apkinjector:color/white = 0x7f050306
com.iapp.leochen.apkinjector:macro/m3_comp_switch_selected_focus_state_layer_color = 0x7f0c0122
com.iapp.leochen.apkinjector:dimen/mtrl_shape_corner_size_small_component = 0x7f0602e4
com.iapp.leochen.apkinjector:color/tooltip_background_dark = 0x7f050304
com.iapp.leochen.apkinjector:color/switch_thumb_material_dark = 0x7f050300
com.iapp.leochen.apkinjector:color/secondary_text_disabled_material_light = 0x7f0502fd
com.iapp.leochen.apkinjector:drawable/abc_ic_menu_overflow_material = 0x7f070045
com.iapp.leochen.apkinjector:attr/materialCalendarDay = 0x7f0302e6
com.iapp.leochen.apkinjector:color/ripple_material_light = 0x7f0502f9
com.iapp.leochen.apkinjector:color/primary_text_default_material_light = 0x7f0502f5
com.iapp.leochen.apkinjector:color/primary_dark_material_light = 0x7f0502f1
com.iapp.leochen.apkinjector:dimen/abc_dropdownitem_text_padding_right = 0x7f06002b
com.iapp.leochen.apkinjector:color/mtrl_textinput_filled_box_default_background_color = 0x7f0502eb
com.iapp.leochen.apkinjector:style/Theme.Material3.Dark.NoActionBar = 0x7f11022f
com.iapp.leochen.apkinjector:style/Theme.Material3.Dark.Dialog = 0x7f11022b
com.iapp.leochen.apkinjector:attr/tickMarkTint = 0x7f03048f
com.iapp.leochen.apkinjector:dimen/m3_comp_outlined_text_field_disabled_input_text_opacity = 0x7f060155
com.iapp.leochen.apkinjector:attr/iconEndPadding = 0x7f030231
com.iapp.leochen.apkinjector:dimen/m3_comp_fab_primary_focus_state_layer_opacity = 0x7f060117
com.iapp.leochen.apkinjector:dimen/m3_appbar_expanded_title_margin_bottom = 0x7f0600a4
com.iapp.leochen.apkinjector:color/mtrl_tabs_ripple_color = 0x7f0502e7
com.iapp.leochen.apkinjector:color/mtrl_tabs_icon_color_selector_colored = 0x7f0502e5
com.iapp.leochen.apkinjector:style/Base.Widget.AppCompat.SearchView.ActionBar = 0x7f1100f4
com.iapp.leochen.apkinjector:color/mtrl_tabs_icon_color_selector = 0x7f0502e4
com.iapp.leochen.apkinjector:attr/itemMinHeight = 0x7f030257
com.iapp.leochen.apkinjector:color/mtrl_tabs_colored_ripple_color = 0x7f0502e3
com.iapp.leochen.apkinjector:color/mtrl_switch_track_decoration_tint = 0x7f0502e1
com.iapp.leochen.apkinjector:color/mtrl_scrim_color = 0x7f0502de
com.iapp.leochen.apkinjector:style/RtlOverlay.Widget.AppCompat.DialogTitle.Icon = 0x7f110147
com.iapp.leochen.apkinjector:color/mtrl_outlined_stroke_color = 0x7f0502dc
com.iapp.leochen.apkinjector:color/mtrl_on_primary_text_btn_text_color_selector = 0x7f0502d9
com.iapp.leochen.apkinjector:attr/framePosition = 0x7f03020a
com.iapp.leochen.apkinjector:drawable/m3_radiobutton_ripple = 0x7f0700aa
com.iapp.leochen.apkinjector:drawable/ic_mtrl_chip_checked_circle = 0x7f0700a0
com.iapp.leochen.apkinjector:id/escape = 0x7f0800bd
com.iapp.leochen.apkinjector:color/mtrl_navigation_item_text_color = 0x7f0502d8
com.iapp.leochen.apkinjector:color/mtrl_indicator_text_color = 0x7f0502d1
com.iapp.leochen.apkinjector:dimen/mtrl_calendar_month_horizontal_padding = 0x7f060289
com.iapp.leochen.apkinjector:color/material_personalized_color_text_primary_inverse = 0x7f0502a4
com.iapp.leochen.apkinjector:color/mtrl_navigation_item_icon_tint = 0x7f0502d7
com.iapp.leochen.apkinjector:id/unlabeled = 0x7f080200
com.iapp.leochen.apkinjector:animator/m3_extended_fab_state_list_animator = 0x7f020014
com.iapp.leochen.apkinjector:color/mtrl_navigation_bar_item_tint = 0x7f0502d4
com.iapp.leochen.apkinjector:dimen/material_clock_hand_stroke_width = 0x7f060229
com.iapp.leochen.apkinjector:style/Base.TextAppearance.AppCompat.Widget.ActionMode.Title = 0x7f110038
com.iapp.leochen.apkinjector:attr/autoTransition = 0x7f030045
com.iapp.leochen.apkinjector:color/mtrl_filled_icon_tint = 0x7f0502cf
com.iapp.leochen.apkinjector:dimen/m3_extended_fab_top_padding = 0x7f0601b4
com.iapp.leochen.apkinjector:dimen/mtrl_calendar_header_toggle_margin_bottom = 0x7f060285
com.iapp.leochen.apkinjector:anim/slide_out_right_smooth = 0x7f010033
com.iapp.leochen.apkinjector:drawable/indeterminate_static = 0x7f0700a4
com.iapp.leochen.apkinjector:color/mtrl_fab_ripple_color = 0x7f0502cd
com.iapp.leochen.apkinjector:color/mtrl_error = 0x7f0502ca
com.iapp.leochen.apkinjector:drawable/abc_textfield_search_activated_mtrl_alpha = 0x7f070074
com.iapp.leochen.apkinjector:layout/material_timepicker_textinput_display = 0x7f0b0046
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_neutral12 = 0x7f0500a1
com.iapp.leochen.apkinjector:color/mtrl_choice_chip_text_color = 0x7f0502c9
com.iapp.leochen.apkinjector:id/material_clock_display = 0x7f08010d
com.iapp.leochen.apkinjector:dimen/mtrl_switch_thumb_elevation = 0x7f0602f7
com.iapp.leochen.apkinjector:style/Theme.AppCompat.Light.Dialog.Alert = 0x7f11021e
com.iapp.leochen.apkinjector:attr/colorOutline = 0x7f030111
com.iapp.leochen.apkinjector:color/mtrl_choice_chip_ripple_color = 0x7f0502c8
com.iapp.leochen.apkinjector:dimen/mtrl_card_spacing = 0x7f0602a0
com.iapp.leochen.apkinjector:color/m3_ref_palette_neutral94 = 0x7f050115
com.iapp.leochen.apkinjector:color/mtrl_choice_chip_background_color = 0x7f0502c7
com.iapp.leochen.apkinjector:color/mtrl_chip_text_color = 0x7f0502c6
com.iapp.leochen.apkinjector:attr/toggleCheckedStateOnClick = 0x7f0304a6
com.iapp.leochen.apkinjector:color/mtrl_calendar_item_stroke_color = 0x7f0502bf
com.iapp.leochen.apkinjector:macro/m3_comp_outlined_text_field_supporting_text_color = 0x7f0c00c4
com.iapp.leochen.apkinjector:color/mtrl_btn_text_color_disabled = 0x7f0502bc
com.iapp.leochen.apkinjector:style/Widget.Material3.FloatingActionButton.Large.Primary = 0x7f11038b
com.iapp.leochen.apkinjector:id/chains = 0x7f080076
com.iapp.leochen.apkinjector:color/design_dark_default_color_background = 0x7f050032
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_light_surface_variant = 0x7f0501bd
com.iapp.leochen.apkinjector:dimen/m3_comp_extended_fab_primary_icon_size = 0x7f060112
com.iapp.leochen.apkinjector:color/mtrl_btn_text_btn_ripple_color = 0x7f0502bb
com.iapp.leochen.apkinjector:drawable/abc_seekbar_thumb_material = 0x7f070063
com.iapp.leochen.apkinjector:style/ThemeOverlay.MaterialComponents.BottomAppBar.Primary = 0x7f1102c9
com.iapp.leochen.apkinjector:style/Base.Theme.AppCompat.CompactMenu = 0x7f11004d
com.iapp.leochen.apkinjector:color/m3_ref_palette_tertiary90 = 0x7f05014b
com.iapp.leochen.apkinjector:color/material_personalized_color_surface_variant = 0x7f0502a0
com.iapp.leochen.apkinjector:color/mtrl_btn_stroke_color_selector = 0x7f0502b9
com.iapp.leochen.apkinjector:color/mtrl_btn_ripple_color = 0x7f0502b8
com.iapp.leochen.apkinjector:color/material_timepicker_modebutton_tint = 0x7f0502b6
com.iapp.leochen.apkinjector:id/cut = 0x7f080092
com.iapp.leochen.apkinjector:color/material_timepicker_button_stroke = 0x7f0502b3
com.iapp.leochen.apkinjector:color/material_slider_inactive_track_color = 0x7f0502b0
com.iapp.leochen.apkinjector:color/material_slider_halo_color = 0x7f0502ae
com.iapp.leochen.apkinjector:dimen/m3_comp_text_button_focus_state_layer_opacity = 0x7f06019c
com.iapp.leochen.apkinjector:attr/cornerSizeTopLeft = 0x7f030155
com.iapp.leochen.apkinjector:attr/carousel_forwardTransition = 0x7f0300a6
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_light_on_surface = 0x7f0501ab
com.iapp.leochen.apkinjector:color/material_slider_active_tick_marks_color = 0x7f0502ac
com.iapp.leochen.apkinjector:color/material_personalized_hint_foreground = 0x7f0502a8
com.iapp.leochen.apkinjector:color/m3_sys_color_light_tertiary_container = 0x7f0501ed
com.iapp.leochen.apkinjector:color/material_personalized_color_text_secondary_and_tertiary_inverse_disabled = 0x7f0502a7
com.iapp.leochen.apkinjector:color/material_personalized_color_text_primary_inverse_disable_only = 0x7f0502a5
com.iapp.leochen.apkinjector:attr/errorTextAppearance = 0x7f0301b5
com.iapp.leochen.apkinjector:color/material_personalized_color_tertiary_container = 0x7f0502a2
com.iapp.leochen.apkinjector:color/material_personalized_color_tertiary = 0x7f0502a1
com.iapp.leochen.apkinjector:color/m3_tabs_icon_color = 0x7f0501fa
com.iapp.leochen.apkinjector:color/mtrl_calendar_selected_range = 0x7f0502c0
com.iapp.leochen.apkinjector:color/material_personalized_color_surface_inverse = 0x7f05029f
com.iapp.leochen.apkinjector:attr/multiChoiceItemLayout = 0x7f030354
com.iapp.leochen.apkinjector:dimen/design_fab_elevation = 0x7f06006f
com.iapp.leochen.apkinjector:macro/m3_comp_navigation_drawer_inactive_focus_icon_color = 0x7f0c0086
com.iapp.leochen.apkinjector:color/material_personalized_color_surface_container_low = 0x7f05029c
com.iapp.leochen.apkinjector:style/Base.V21.Theme.AppCompat.Light.Dialog = 0x7f1100a5
com.iapp.leochen.apkinjector:color/material_personalized_color_surface_container_highest = 0x7f05029b
com.iapp.leochen.apkinjector:color/material_personalized_color_surface_container_high = 0x7f05029a
com.iapp.leochen.apkinjector:style/Widget.AppCompat.Button.Small = 0x7f1102f8
com.iapp.leochen.apkinjector:color/material_personalized_color_surface_bright = 0x7f050298
com.iapp.leochen.apkinjector:style/Base.Widget.AppCompat.ActionBar.TabBar = 0x7f1100c5
com.iapp.leochen.apkinjector:color/material_personalized_color_surface = 0x7f050297
com.iapp.leochen.apkinjector:dimen/mtrl_calendar_navigation_bottom_padding = 0x7f06028b
com.iapp.leochen.apkinjector:dimen/m3_sys_motion_easing_standard_decelerate_control_y2 = 0x7f060219
com.iapp.leochen.apkinjector:color/material_personalized_color_secondary_text_inverse = 0x7f050296
com.iapp.leochen.apkinjector:color/material_personalized_color_secondary_container = 0x7f050294
com.iapp.leochen.apkinjector:color/background_material_dark = 0x7f05001f
com.iapp.leochen.apkinjector:color/m3_sys_color_dark_on_error_container = 0x7f050162
com.iapp.leochen.apkinjector:color/material_personalized_color_secondary = 0x7f050293
com.iapp.leochen.apkinjector:attr/region_widthMoreThan = 0x7f0303a6
com.iapp.leochen.apkinjector:color/material_personalized_color_primary_inverse = 0x7f050290
com.iapp.leochen.apkinjector:color/material_personalized_color_primary_container = 0x7f05028f
com.iapp.leochen.apkinjector:drawable/material_ic_menu_arrow_up_black_24dp = 0x7f0700b9
com.iapp.leochen.apkinjector:attr/layout_constraintWidth_percent = 0x7f0302ac
com.iapp.leochen.apkinjector:dimen/abc_text_size_title_material = 0x7f06004f
com.iapp.leochen.apkinjector:color/material_personalized_color_on_tertiary_container = 0x7f05028b
com.iapp.leochen.apkinjector:color/material_personalized_color_on_tertiary = 0x7f05028a
com.iapp.leochen.apkinjector:color/material_personalized_color_on_surface_variant = 0x7f050289
com.iapp.leochen.apkinjector:color/material_personalized_color_on_secondary = 0x7f050285
com.iapp.leochen.apkinjector:attr/colorBackgroundFloating = 0x7f0300f4
com.iapp.leochen.apkinjector:color/material_slider_active_track_color = 0x7f0502ad
com.iapp.leochen.apkinjector:color/material_personalized_color_on_primary_container = 0x7f050284
com.iapp.leochen.apkinjector:color/material_personalized_color_on_error_container = 0x7f050282
com.iapp.leochen.apkinjector:color/material_personalized_color_on_error = 0x7f050281
com.iapp.leochen.apkinjector:style/Widget.Material3.Button.TextButton.Icon = 0x7f11035f
com.iapp.leochen.apkinjector:id/action_bar_spinner = 0x7f080038
com.iapp.leochen.apkinjector:dimen/mtrl_extended_fab_start_padding_icon = 0x7f0602b1
com.iapp.leochen.apkinjector:color/material_personalized_color_error_container = 0x7f05027f
com.iapp.leochen.apkinjector:macro/m3_comp_navigation_drawer_active_hover_state_layer_color = 0x7f0c007d
com.iapp.leochen.apkinjector:dimen/mtrl_calendar_day_corner = 0x7f060276
com.iapp.leochen.apkinjector:color/m3_sys_color_dark_surface_variant = 0x7f050179
com.iapp.leochen.apkinjector:color/material_personalized_color_error = 0x7f05027e
com.iapp.leochen.apkinjector:attr/buttonBarStyle = 0x7f03008e
com.iapp.leochen.apkinjector:color/material_personalized_color_control_normal = 0x7f05027d
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.MaterialCalendar.MonthNavigationButton = 0x7f110433
com.iapp.leochen.apkinjector:attr/drawerArrowStyle = 0x7f030191
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_dark_primary_container = 0x7f050190
com.iapp.leochen.apkinjector:dimen/material_filled_edittext_font_1_3_padding_bottom = 0x7f060237
com.iapp.leochen.apkinjector:style/ShapeAppearance.Material3.LargeComponent = 0x7f110176
com.iapp.leochen.apkinjector:dimen/m3_chip_icon_size = 0x7f0600f9
com.iapp.leochen.apkinjector:macro/m3_comp_switch_selected_icon_color = 0x7f0c0129
com.iapp.leochen.apkinjector:macro/m3_comp_filled_tonal_icon_button_container_color = 0x7f0c0054
com.iapp.leochen.apkinjector:color/material_personalized_color_control_highlight = 0x7f05027c
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.Chip.Choice = 0x7f11040f
com.iapp.leochen.apkinjector:color/mtrl_switch_thumb_tint = 0x7f0502e0
com.iapp.leochen.apkinjector:anim/btn_checkbox_to_unchecked_icon_null_animation = 0x7f010011
com.iapp.leochen.apkinjector:dimen/m3_bottomappbar_fab_cradle_margin = 0x7f0600c5
com.iapp.leochen.apkinjector:color/material_personalized_color_control_activated = 0x7f05027b
com.iapp.leochen.apkinjector:style/ThemeOverlay.Material3.ActionBar = 0x7f110281
com.iapp.leochen.apkinjector:color/material_personalized_color_background = 0x7f05027a
com.iapp.leochen.apkinjector:style/ShapeAppearance.M3.Comp.NavigationDrawer.ActiveIndicator.Shape = 0x7f11015d
com.iapp.leochen.apkinjector:color/mtrl_navigation_bar_ripple_color = 0x7f0502d5
com.iapp.leochen.apkinjector:attr/badgeWithTextShapeAppearance = 0x7f030060
com.iapp.leochen.apkinjector:color/material_personalized__highlighted_text_inverse = 0x7f050279
com.iapp.leochen.apkinjector:color/material_on_surface_stroke = 0x7f050277
com.iapp.leochen.apkinjector:style/Base.Widget.MaterialComponents.MaterialCalendar.HeaderToggleButton = 0x7f110115
com.iapp.leochen.apkinjector:id/tag_accessibility_heading = 0x7f0801cc
com.iapp.leochen.apkinjector:color/abc_primary_text_disable_only_material_dark = 0x7f050009
com.iapp.leochen.apkinjector:attr/textAppearanceBodySmall = 0x7f03043e
com.iapp.leochen.apkinjector:attr/voiceIcon = 0x7f0304d8
com.iapp.leochen.apkinjector:color/material_on_surface_emphasis_high_type = 0x7f050275
com.iapp.leochen.apkinjector:styleable/MaterialCalendarItem = 0x7f120053
com.iapp.leochen.apkinjector:color/notification_action_color_filter = 0x7f0502ee
com.iapp.leochen.apkinjector:color/material_on_primary_disabled = 0x7f050271
com.iapp.leochen.apkinjector:drawable/ic_info = 0x7f070097
com.iapp.leochen.apkinjector:color/material_on_background_emphasis_medium = 0x7f050270
com.iapp.leochen.apkinjector:color/material_on_background_emphasis_high_type = 0x7f05026f
com.iapp.leochen.apkinjector:dimen/mtrl_calendar_action_height = 0x7f060272
com.iapp.leochen.apkinjector:color/material_on_background_disabled = 0x7f05026e
com.iapp.leochen.apkinjector:dimen/design_navigation_separator_vertical_padding = 0x7f06007d
com.iapp.leochen.apkinjector:attr/statusBarForeground = 0x7f030400
com.iapp.leochen.apkinjector:color/material_harmonized_color_error_container = 0x7f05026b
com.iapp.leochen.apkinjector:style/TextAppearance.Material3.TitleMedium = 0x7f1101f6
com.iapp.leochen.apkinjector:id/open_search_view_background = 0x7f08015d
com.iapp.leochen.apkinjector:dimen/mtrl_btn_padding_bottom = 0x7f060265
com.iapp.leochen.apkinjector:color/m3_switch_thumb_tint = 0x7f050158
com.iapp.leochen.apkinjector:color/material_grey_850 = 0x7f050268
com.iapp.leochen.apkinjector:color/material_grey_800 = 0x7f050267
com.iapp.leochen.apkinjector:id/graph_wrap = 0x7f0800df
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_dark_on_error_container = 0x7f050184
com.iapp.leochen.apkinjector:dimen/m3_snackbar_action_text_color_alpha = 0x7f0601ee
com.iapp.leochen.apkinjector:dimen/design_fab_translation_z_pressed = 0x7f060074
com.iapp.leochen.apkinjector:color/material_grey_50 = 0x7f050265
com.iapp.leochen.apkinjector:color/material_grey_100 = 0x7f050263
com.iapp.leochen.apkinjector:color/material_dynamic_tertiary95 = 0x7f050261
com.iapp.leochen.apkinjector:styleable/LinearProgressIndicator = 0x7f12004b
com.iapp.leochen.apkinjector:color/material_dynamic_tertiary90 = 0x7f050260
com.iapp.leochen.apkinjector:drawable/ic_launcher_foreground = 0x7f07009a
com.iapp.leochen.apkinjector:dimen/abc_dialog_list_padding_bottom_no_buttons = 0x7f060020
com.iapp.leochen.apkinjector:attr/fabCradleRoundedCornerRadius = 0x7f0301cf
com.iapp.leochen.apkinjector:dimen/design_navigation_padding_bottom = 0x7f06007c
com.iapp.leochen.apkinjector:color/material_dynamic_tertiary70 = 0x7f05025e
com.iapp.leochen.apkinjector:style/ThemeOverlay.MaterialComponents.Dark.ActionBar = 0x7f1102cd
com.iapp.leochen.apkinjector:color/material_dynamic_tertiary60 = 0x7f05025d
com.iapp.leochen.apkinjector:color/material_dynamic_tertiary50 = 0x7f05025c
com.iapp.leochen.apkinjector:color/material_dynamic_tertiary40 = 0x7f05025b
com.iapp.leochen.apkinjector:color/material_dynamic_tertiary20 = 0x7f050259
com.iapp.leochen.apkinjector:color/material_dynamic_tertiary100 = 0x7f050258
com.iapp.leochen.apkinjector:color/material_dynamic_secondary99 = 0x7f050255
com.iapp.leochen.apkinjector:id/startVertical = 0x7f0801c0
com.iapp.leochen.apkinjector:color/highlighted_text_material_dark = 0x7f05005e
com.iapp.leochen.apkinjector:color/material_dynamic_secondary80 = 0x7f050252
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_tertiary_fixed_dim = 0x7f0501cb
com.iapp.leochen.apkinjector:color/material_dynamic_secondary50 = 0x7f05024f
com.iapp.leochen.apkinjector:style/Widget.Material3.Search.Toolbar.Button.Navigation = 0x7f1103c6
com.iapp.leochen.apkinjector:id/pooling_container_listener_holder_tag = 0x7f08017a
com.iapp.leochen.apkinjector:color/material_dynamic_secondary40 = 0x7f05024e
com.iapp.leochen.apkinjector:drawable/$mtrl_checkbox_button_icon_checked_unchecked__0 = 0x7f070011
com.iapp.leochen.apkinjector:dimen/material_cursor_width = 0x7f060231
com.iapp.leochen.apkinjector:color/material_dynamic_primary99 = 0x7f050248
com.iapp.leochen.apkinjector:color/material_dynamic_primary70 = 0x7f050244
com.iapp.leochen.apkinjector:color/material_dynamic_primary60 = 0x7f050243
com.iapp.leochen.apkinjector:color/primary_material_dark = 0x7f0502f2
com.iapp.leochen.apkinjector:color/material_dynamic_primary50 = 0x7f050242
com.iapp.leochen.apkinjector:attr/theme = 0x7f03047a
com.iapp.leochen.apkinjector:color/material_dynamic_primary40 = 0x7f050241
com.iapp.leochen.apkinjector:dimen/m3_sys_motion_easing_linear_control_x1 = 0x7f06020a
com.iapp.leochen.apkinjector:color/material_dynamic_primary100 = 0x7f05023e
com.iapp.leochen.apkinjector:styleable/State = 0x7f120082
com.iapp.leochen.apkinjector:color/material_dynamic_neutral_variant99 = 0x7f05023b
com.iapp.leochen.apkinjector:style/ThemeOverlay.Material3.BottomAppBar = 0x7f110287
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_neutral_variant22 = 0x7f0500bc
com.iapp.leochen.apkinjector:drawable/ic_chevron_right = 0x7f070091
com.iapp.leochen.apkinjector:color/material_dynamic_neutral_variant90 = 0x7f050239
com.iapp.leochen.apkinjector:color/material_dynamic_neutral_variant60 = 0x7f050236
com.iapp.leochen.apkinjector:color/material_dynamic_neutral_variant40 = 0x7f050234
com.iapp.leochen.apkinjector:dimen/abc_edit_text_inset_bottom_material = 0x7f06002c
com.iapp.leochen.apkinjector:color/material_dynamic_neutral_variant30 = 0x7f050233
com.iapp.leochen.apkinjector:id/wrap_content = 0x7f080214
com.iapp.leochen.apkinjector:dimen/m3_comp_search_bar_container_elevation = 0x7f06016f
com.iapp.leochen.apkinjector:color/material_dynamic_neutral_variant10 = 0x7f050230
com.iapp.leochen.apkinjector:drawable/$avd_show_password__2 = 0x7f070005
com.iapp.leochen.apkinjector:color/material_dynamic_neutral70 = 0x7f05022a
com.iapp.leochen.apkinjector:color/material_dynamic_neutral60 = 0x7f050229
com.iapp.leochen.apkinjector:color/material_dynamic_neutral50 = 0x7f050228
com.iapp.leochen.apkinjector:style/Base.V14.Theme.Material3.Dark.Dialog = 0x7f11008c
com.iapp.leochen.apkinjector:color/material_dynamic_neutral40 = 0x7f050227
com.iapp.leochen.apkinjector:dimen/m3_btn_text_btn_icon_padding_left = 0x7f0600de
com.iapp.leochen.apkinjector:color/material_dynamic_neutral30 = 0x7f050226
com.iapp.leochen.apkinjector:id/default_activity_button = 0x7f080097
com.iapp.leochen.apkinjector:attr/autoSizeMaxTextSize = 0x7f030040
com.iapp.leochen.apkinjector:color/material_dynamic_neutral20 = 0x7f050225
com.iapp.leochen.apkinjector:color/material_dynamic_neutral10 = 0x7f050223
com.iapp.leochen.apkinjector:style/ThemeOverlay.AppCompat.DayNight.ActionBar = 0x7f11027b
com.iapp.leochen.apkinjector:style/Theme.AppCompat.Light.DarkActionBar = 0x7f11021c
com.iapp.leochen.apkinjector:id/right = 0x7f080185
com.iapp.leochen.apkinjector:color/material_dynamic_neutral0 = 0x7f050222
com.iapp.leochen.apkinjector:style/Widget.Material3.MaterialCalendar.Year = 0x7f1103ae
com.iapp.leochen.apkinjector:layout/mtrl_calendar_vertical = 0x7f0b0056
com.iapp.leochen.apkinjector:color/material_dynamic_color_dark_on_error_container = 0x7f05021d
com.iapp.leochen.apkinjector:color/material_dynamic_color_light_on_error = 0x7f050220
com.iapp.leochen.apkinjector:styleable/ViewPager2 = 0x7f120097
com.iapp.leochen.apkinjector:style/Base.Widget.AppCompat.CompoundButton.CheckBox = 0x7f1100d6
com.iapp.leochen.apkinjector:attr/actionBarTabStyle = 0x7f030009
com.iapp.leochen.apkinjector:color/material_dynamic_color_light_error_container = 0x7f05021f
com.iapp.leochen.apkinjector:drawable/$mtrl_switch_thumb_checked_unchecked__0 = 0x7f070022
com.iapp.leochen.apkinjector:attr/headerLayout = 0x7f03021c
com.iapp.leochen.apkinjector:dimen/mtrl_navigation_bar_item_default_icon_size = 0x7f0602c3
com.iapp.leochen.apkinjector:color/material_dynamic_color_dark_on_error = 0x7f05021c
com.iapp.leochen.apkinjector:dimen/mtrl_calendar_day_width = 0x7f06027b
com.iapp.leochen.apkinjector:color/material_dynamic_color_dark_error = 0x7f05021a
com.iapp.leochen.apkinjector:dimen/mtrl_low_ripple_focused_alpha = 0x7f0602bf
com.iapp.leochen.apkinjector:dimen/mtrl_tooltip_cornerSize = 0x7f060307
com.iapp.leochen.apkinjector:color/material_divider_color = 0x7f050219
com.iapp.leochen.apkinjector:dimen/mtrl_badge_long_text_horizontal_padding = 0x7f06024c
com.iapp.leochen.apkinjector:color/material_deep_teal_500 = 0x7f050218
com.iapp.leochen.apkinjector:attr/lineSpacing = 0x7f0302c4
com.iapp.leochen.apkinjector:color/material_deep_teal_200 = 0x7f050217
com.iapp.leochen.apkinjector:color/material_personalized_color_on_surface_inverse = 0x7f050288
com.iapp.leochen.apkinjector:style/TextAppearance.MaterialComponents.Overline = 0x7f110204
com.iapp.leochen.apkinjector:color/material_cursor_color = 0x7f050216
com.iapp.leochen.apkinjector:color/material_blue_grey_950 = 0x7f050215
com.iapp.leochen.apkinjector:color/material_blue_grey_800 = 0x7f050213
com.iapp.leochen.apkinjector:dimen/m3_comp_switch_disabled_unselected_icon_opacity = 0x7f060193
com.iapp.leochen.apkinjector:style/Base.Widget.AppCompat.Button = 0x7f1100ce
com.iapp.leochen.apkinjector:color/mtrl_navigation_item_background_color = 0x7f0502d6
com.iapp.leochen.apkinjector:style/Base.Widget.AppCompat.Spinner.Underlined = 0x7f1100f8
com.iapp.leochen.apkinjector:color/m3_timepicker_time_input_stroke_color = 0x7f050211
com.iapp.leochen.apkinjector:drawable/mtrl_switch_thumb_pressed_unchecked = 0x7f0700d8
com.iapp.leochen.apkinjector:styleable/OnClick = 0x7f12006e
com.iapp.leochen.apkinjector:dimen/m3_navigation_item_shape_inset_start = 0x7f0601c4
com.iapp.leochen.apkinjector:style/Theme.MaterialComponents.DayNight.Bridge = 0x7f11024d
com.iapp.leochen.apkinjector:anim/m3_bottom_sheet_slide_out = 0x7f010022
com.iapp.leochen.apkinjector:color/m3_timepicker_button_text_color = 0x7f05020a
com.iapp.leochen.apkinjector:color/m3_timepicker_display_background_color = 0x7f05020c
com.iapp.leochen.apkinjector:attr/dayStyle = 0x7f03016e
com.iapp.leochen.apkinjector:dimen/m3_sys_elevation_level1 = 0x7f0601f1
com.iapp.leochen.apkinjector:color/m3_timepicker_button_background_color = 0x7f050208
com.iapp.leochen.apkinjector:color/m3_textfield_stroke_color = 0x7f050207
com.iapp.leochen.apkinjector:styleable/KeyCycle = 0x7f120041
com.iapp.leochen.apkinjector:color/m3_textfield_label_color = 0x7f050206
com.iapp.leochen.apkinjector:drawable/$mtrl_checkbox_button_checked_unchecked__2 = 0x7f07000f
com.iapp.leochen.apkinjector:dimen/m3_comp_input_chip_unselected_outline_width = 0x7f060132
com.iapp.leochen.apkinjector:layout/mtrl_picker_dialog = 0x7f0b005c
com.iapp.leochen.apkinjector:color/m3_textfield_input_text_color = 0x7f050205
com.iapp.leochen.apkinjector:color/m3_textfield_indicator_text_color = 0x7f050204
com.iapp.leochen.apkinjector:dimen/highlight_alpha_material_light = 0x7f060095
com.iapp.leochen.apkinjector:color/m3_textfield_filled_background_color = 0x7f050203
com.iapp.leochen.apkinjector:attr/drawableTint = 0x7f03018e
com.iapp.leochen.apkinjector:color/m3_text_button_ripple_color_selector = 0x7f050202
com.iapp.leochen.apkinjector:id/BOTTOM_END = 0x7f080001
com.iapp.leochen.apkinjector:color/m3_text_button_background_color_selector = 0x7f050200
com.iapp.leochen.apkinjector:color/mtrl_textinput_hovered_box_stroke_color = 0x7f0502ed
com.iapp.leochen.apkinjector:color/m3_tabs_text_color_secondary = 0x7f0501ff
com.iapp.leochen.apkinjector:color/m3_tabs_text_color = 0x7f0501fe
com.iapp.leochen.apkinjector:color/m3_tabs_ripple_color_secondary = 0x7f0501fd
com.iapp.leochen.apkinjector:color/m3_tabs_icon_color_secondary = 0x7f0501fb
com.iapp.leochen.apkinjector:color/m3_sys_color_tertiary_fixed_dim = 0x7f0501f9
com.iapp.leochen.apkinjector:color/switch_thumb_disabled_material_dark = 0x7f0502fe
com.iapp.leochen.apkinjector:color/m3_sys_color_tertiary_fixed = 0x7f0501f8
com.iapp.leochen.apkinjector:color/m3_sys_color_secondary_fixed = 0x7f0501f6
com.iapp.leochen.apkinjector:dimen/m3_comp_fab_primary_small_icon_size = 0x7f060120
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.TextInputEditText.FilledBox.Dense = 0x7f11044e
com.iapp.leochen.apkinjector:drawable/notification_bg_low_normal = 0x7f0700e3
com.iapp.leochen.apkinjector:color/m3_sys_color_primary_fixed_dim = 0x7f0501f5
com.iapp.leochen.apkinjector:macro/m3_comp_switch_selected_hover_track_color = 0x7f0c0128
com.iapp.leochen.apkinjector:id/mtrl_view_tag_bottom_padding = 0x7f080142
com.iapp.leochen.apkinjector:color/m3_sys_color_on_tertiary_fixed_variant = 0x7f0501f3
com.iapp.leochen.apkinjector:dimen/abc_text_size_caption_material = 0x7f060042
com.iapp.leochen.apkinjector:color/m3_sys_color_on_secondary_fixed_variant = 0x7f0501f1
com.iapp.leochen.apkinjector:style/Widget.Material3.MaterialCalendar.Day.Today = 0x7f11039f
com.iapp.leochen.apkinjector:dimen/mtrl_btn_snackbar_margin_horizontal = 0x7f06026a
com.iapp.leochen.apkinjector:color/m3_sys_color_light_surface_dim = 0x7f0501ea
com.iapp.leochen.apkinjector:color/m3_sys_color_light_surface_container_lowest = 0x7f0501e9
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_light_outline = 0x7f0501af
com.iapp.leochen.apkinjector:color/m3_sys_color_light_surface_container_high = 0x7f0501e6
com.iapp.leochen.apkinjector:color/m3_sys_color_light_surface_container = 0x7f0501e5
com.iapp.leochen.apkinjector:dimen/appcompat_dialog_background_inset = 0x7f060051
com.iapp.leochen.apkinjector:dimen/m3_comp_time_picker_time_selector_hover_state_layer_opacity = 0x7f0601a6
com.iapp.leochen.apkinjector:macro/m3_comp_secondary_navigation_tab_container_color = 0x7f0c00fc
com.iapp.leochen.apkinjector:color/m3_sys_color_light_surface_bright = 0x7f0501e4
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.MaterialCalendar.Day.Selected = 0x7f110424
com.iapp.leochen.apkinjector:color/m3_sys_color_light_surface = 0x7f0501e3
com.iapp.leochen.apkinjector:dimen/design_bottom_navigation_label_padding = 0x7f060067
com.iapp.leochen.apkinjector:drawable/abc_list_selector_holo_light = 0x7f070058
com.iapp.leochen.apkinjector:color/material_dynamic_tertiary80 = 0x7f05025f
com.iapp.leochen.apkinjector:anim/btn_radio_to_on_mtrl_ring_outer_path_animation = 0x7f010017
com.iapp.leochen.apkinjector:drawable/abc_btn_check_to_on_mtrl_015 = 0x7f07002f
com.iapp.leochen.apkinjector:color/material_dynamic_neutral_variant20 = 0x7f050232
com.iapp.leochen.apkinjector:dimen/notification_action_text_size = 0x7f06030d
com.iapp.leochen.apkinjector:color/m3_sys_color_light_secondary_container = 0x7f0501e2
com.iapp.leochen.apkinjector:style/Base.Theme.MaterialComponents.Light.DarkActionBar = 0x7f110071
com.iapp.leochen.apkinjector:dimen/mtrl_progress_track_thickness = 0x7f0602e1
com.iapp.leochen.apkinjector:dimen/m3_sys_motion_easing_standard_accelerate_control_y2 = 0x7f060211
com.iapp.leochen.apkinjector:color/m3_sys_color_light_primary = 0x7f0501df
com.iapp.leochen.apkinjector:color/m3_sys_color_light_outline_variant = 0x7f0501de
com.iapp.leochen.apkinjector:string/side_sheet_behavior = 0x7f1000a5
com.iapp.leochen.apkinjector:string/item_view_role_description = 0x7f100036
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_neutral_variant98 = 0x7f0500cc
com.iapp.leochen.apkinjector:color/m3_tabs_ripple_color = 0x7f0501fc
com.iapp.leochen.apkinjector:anim/design_snackbar_out = 0x7f01001b
com.iapp.leochen.apkinjector:color/m3_sys_color_light_outline = 0x7f0501dd
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_tertiary90 = 0x7f0500f2
com.iapp.leochen.apkinjector:color/m3_sys_color_light_on_tertiary_container = 0x7f0501dc
com.iapp.leochen.apkinjector:style/Widget.AppCompat.ActionBar.Solid = 0x7f1102e9
com.iapp.leochen.apkinjector:drawable/material_cursor_drawable = 0x7f0700b0
com.iapp.leochen.apkinjector:color/m3_sys_color_light_on_tertiary = 0x7f0501db
com.iapp.leochen.apkinjector:dimen/m3_comp_progress_indicator_active_indicator_track_space = 0x7f060162
com.iapp.leochen.apkinjector:color/m3_sys_color_light_on_surface = 0x7f0501d9
com.iapp.leochen.apkinjector:attr/swipeRefreshLayoutProgressSpinnerBackgroundColor = 0x7f030413
com.iapp.leochen.apkinjector:color/m3_sys_color_light_on_secondary_container = 0x7f0501d8
com.iapp.leochen.apkinjector:dimen/m3_sys_motion_easing_standard_control_x1 = 0x7f060212
com.iapp.leochen.apkinjector:color/m3_sys_color_light_on_secondary = 0x7f0501d7
com.iapp.leochen.apkinjector:color/m3_sys_color_light_on_primary = 0x7f0501d5
com.iapp.leochen.apkinjector:style/Widget.Design.ScrimInsetsFrameLayout = 0x7f110339
com.iapp.leochen.apkinjector:macro/m3_comp_filled_tonal_button_label_text_color = 0x7f0c0053
com.iapp.leochen.apkinjector:color/m3_sys_color_light_on_error = 0x7f0501d3
com.iapp.leochen.apkinjector:color/m3_sys_color_light_on_background = 0x7f0501d2
com.iapp.leochen.apkinjector:dimen/material_clock_period_toggle_width = 0x7f06022e
com.iapp.leochen.apkinjector:attr/errorAccessibilityLiveRegion = 0x7f0301ae
com.iapp.leochen.apkinjector:dimen/m3_comp_outlined_autocomplete_menu_container_elevation = 0x7f06014d
com.iapp.leochen.apkinjector:color/m3_sys_color_light_error_container = 0x7f0501ce
com.iapp.leochen.apkinjector:dimen/m3_sys_motion_easing_standard_control_x2 = 0x7f060213
com.iapp.leochen.apkinjector:dimen/mtrl_navigation_item_shape_horizontal_margin = 0x7f0602c9
com.iapp.leochen.apkinjector:dimen/design_bottom_navigation_elevation = 0x7f060062
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_primary_fixed_dim = 0x7f0501c7
com.iapp.leochen.apkinjector:style/Widget.Material3.TextInputEditText.OutlinedBox = 0x7f1103dc
com.iapp.leochen.apkinjector:dimen/mtrl_textinput_box_stroke_width_focused = 0x7f060300
com.iapp.leochen.apkinjector:attr/thumbIconTint = 0x7f030481
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_on_tertiary_fixed_variant = 0x7f0501c5
com.iapp.leochen.apkinjector:attr/fastScrollHorizontalThumbDrawable = 0x7f0301d4
com.iapp.leochen.apkinjector:attr/actionModeFindDrawable = 0x7f030018
com.iapp.leochen.apkinjector:attr/layout_goneMarginRight = 0x7f0302b4
com.iapp.leochen.apkinjector:dimen/mtrl_bottomappbar_fab_bottom_margin = 0x7f060254
com.iapp.leochen.apkinjector:color/material_dynamic_secondary70 = 0x7f050251
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_on_tertiary_fixed = 0x7f0501c4
com.iapp.leochen.apkinjector:string/mtrl_picker_confirm = 0x7f100072
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_on_secondary_fixed_variant = 0x7f0501c3
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_on_primary_fixed = 0x7f0501c0
com.iapp.leochen.apkinjector:id/tag_accessibility_clickable_spans = 0x7f0801cb
com.iapp.leochen.apkinjector:color/material_blue_grey_900 = 0x7f050214
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_light_tertiary_container = 0x7f0501bf
com.iapp.leochen.apkinjector:style/Widget.AppCompat.ActionMode = 0x7f1102f0
com.iapp.leochen.apkinjector:dimen/m3_side_sheet_standard_elevation = 0x7f0601e7
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_light_surface_dim = 0x7f0501bc
com.iapp.leochen.apkinjector:color/material_personalized_color_secondary_text = 0x7f050295
com.iapp.leochen.apkinjector:id/mtrl_picker_fullscreen = 0x7f080139
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_light_surface_container_lowest = 0x7f0501bb
com.iapp.leochen.apkinjector:color/notification_icon_bg_color = 0x7f0502ef
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_light_surface_container_low = 0x7f0501ba
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_light_surface_container = 0x7f0501b7
com.iapp.leochen.apkinjector:attr/materialSearchBarStyle = 0x7f030302
com.iapp.leochen.apkinjector:attr/motionDurationShort2 = 0x7f030334
com.iapp.leochen.apkinjector:dimen/mtrl_calendar_pre_l_text_clip_padding = 0x7f06028e
com.iapp.leochen.apkinjector:anim/abc_popup_enter = 0x7f010003
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_light_outline_variant = 0x7f0501b0
com.iapp.leochen.apkinjector:dimen/compat_button_padding_vertical_material = 0x7f060059
com.iapp.leochen.apkinjector:color/material_dynamic_neutral_variant80 = 0x7f050238
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_light_on_tertiary_container = 0x7f0501ae
com.iapp.leochen.apkinjector:macro/m3_comp_text_button_hover_state_layer_color = 0x7f0c0143
com.iapp.leochen.apkinjector:dimen/mtrl_alert_dialog_picker_background_inset = 0x7f06024a
com.iapp.leochen.apkinjector:macro/m3_comp_radio_button_selected_hover_state_layer_color = 0x7f0c00db
com.iapp.leochen.apkinjector:attr/listPreferredItemPaddingLeft = 0x7f0302d2
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_light_on_surface_variant = 0x7f0501ac
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_light_on_secondary_container = 0x7f0501aa
com.iapp.leochen.apkinjector:id/TOP_START = 0x7f08000d
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_light_on_secondary = 0x7f0501a9
com.iapp.leochen.apkinjector:style/Base.ThemeOverlay.Material3.TextInputEditText = 0x7f110084
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_light_on_primary_container = 0x7f0501a8
com.iapp.leochen.apkinjector:string/m3_sys_motion_easing_emphasized_path_data = 0x7f10003f
com.iapp.leochen.apkinjector:macro/m3_comp_dialog_container_color = 0x7f0c0022
com.iapp.leochen.apkinjector:attr/checkedTextViewStyle = 0x7f0300bd
com.iapp.leochen.apkinjector:attr/motionDurationMedium3 = 0x7f030331
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_light_on_error_container = 0x7f0501a6
com.iapp.leochen.apkinjector:string/mtrl_picker_invalid_format_use = 0x7f10007a
com.iapp.leochen.apkinjector:dimen/material_cursor_inset = 0x7f060230
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_light_on_error = 0x7f0501a5
com.iapp.leochen.apkinjector:color/ripple_material_dark = 0x7f0502f8
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_light_on_background = 0x7f0501a4
com.iapp.leochen.apkinjector:id/marquee = 0x7f080109
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_light_inverse_surface = 0x7f0501a3
com.iapp.leochen.apkinjector:attr/iconSize = 0x7f030234
com.iapp.leochen.apkinjector:dimen/mtrl_btn_icon_padding = 0x7f060261
com.iapp.leochen.apkinjector:color/m3_ref_palette_tertiary60 = 0x7f050148
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_light_inverse_on_surface = 0x7f0501a1
com.iapp.leochen.apkinjector:drawable/abc_spinner_mtrl_am_alpha = 0x7f070066
com.iapp.leochen.apkinjector:macro/m3_comp_navigation_drawer_label_text_type = 0x7f0c0091
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_light_error_container = 0x7f0501a0
com.iapp.leochen.apkinjector:attr/textAllCaps = 0x7f030439
com.iapp.leochen.apkinjector:dimen/mtrl_card_checked_icon_size = 0x7f06029c
com.iapp.leochen.apkinjector:macro/m3_comp_time_picker_period_selector_container_shape = 0x7f0c0152
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_dark_tertiary_container = 0x7f05019d
com.iapp.leochen.apkinjector:attr/fontProviderAuthority = 0x7f0301fd
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_dark_tertiary = 0x7f05019c
com.iapp.leochen.apkinjector:style/ThemeOverlay.MaterialComponents.TextInputEditText = 0x7f1102dd
com.iapp.leochen.apkinjector:attr/actionDropDownStyle = 0x7f03000e
com.iapp.leochen.apkinjector:dimen/mtrl_navigation_rail_default_width = 0x7f0602cd
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_dark_surface_container_highest = 0x7f050197
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_dark_surface_container_high = 0x7f050196
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_dark_surface_container = 0x7f050195
com.iapp.leochen.apkinjector:attr/layout_constraintWidth_default = 0x7f0302a9
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_dark_surface_bright = 0x7f050194
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_dark_on_secondary_container = 0x7f050188
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_dark_surface = 0x7f050193
com.iapp.leochen.apkinjector:style/Widget.Material3.FloatingActionButton.Small.Primary = 0x7f110391
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_dark_outline_variant = 0x7f05018e
com.iapp.leochen.apkinjector:dimen/hint_pressed_alpha_material_dark = 0x7f060098
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_dark_outline = 0x7f05018d
com.iapp.leochen.apkinjector:attr/suffixTextColor = 0x7f030411
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_dark_on_tertiary_container = 0x7f05018c
com.iapp.leochen.apkinjector:anim/btn_checkbox_to_checked_box_inner_merged_animation = 0x7f01000c
com.iapp.leochen.apkinjector:attr/motionTarget = 0x7f030350
com.iapp.leochen.apkinjector:dimen/m3_large_fab_size = 0x7f0601ba
com.iapp.leochen.apkinjector:style/Base.Widget.AppCompat.CompoundButton.RadioButton = 0x7f1100d7
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_dark_on_surface = 0x7f050189
com.iapp.leochen.apkinjector:id/off = 0x7f080159
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_dark_on_secondary = 0x7f050187
com.iapp.leochen.apkinjector:dimen/abc_text_size_large_material = 0x7f060048
com.iapp.leochen.apkinjector:style/Base.ThemeOverlay.MaterialComponents.Dialog.Alert = 0x7f110086
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_dark_on_primary_container = 0x7f050186
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_dark_inverse_surface = 0x7f050181
com.iapp.leochen.apkinjector:style/Base.TextAppearance.AppCompat.Widget.Button.Colored = 0x7f11003b
com.iapp.leochen.apkinjector:dimen/m3_comp_radio_button_unselected_focus_state_layer_opacity = 0x7f06016a
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_dark_inverse_primary = 0x7f050180
com.iapp.leochen.apkinjector:drawable/abc_switch_thumb_material = 0x7f07006a
com.iapp.leochen.apkinjector:dimen/m3_comp_navigation_drawer_pressed_state_layer_opacity = 0x7f060143
com.iapp.leochen.apkinjector:style/Theme.MaterialComponents.DayNight.DarkActionBar.Bridge = 0x7f11024f
com.iapp.leochen.apkinjector:attr/colorErrorContainer = 0x7f0300fb
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_dark_secondary = 0x7f050191
com.iapp.leochen.apkinjector:attr/suffixText = 0x7f03040f
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_dark_inverse_on_surface = 0x7f05017f
com.iapp.leochen.apkinjector:style/Widget.AppCompat.ButtonBar = 0x7f1102f9
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_dark_error_container = 0x7f05017e
com.iapp.leochen.apkinjector:color/abc_btn_colored_text_material = 0x7f050003
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_dark_error = 0x7f05017d
com.iapp.leochen.apkinjector:macro/m3_comp_date_picker_modal_header_headline_color = 0x7f0c0015
com.iapp.leochen.apkinjector:id/META = 0x7f080005
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_dark_background = 0x7f05017c
com.iapp.leochen.apkinjector:styleable/BottomNavigationView = 0x7f120016
com.iapp.leochen.apkinjector:color/m3_ref_palette_error10 = 0x7f0500f6
com.iapp.leochen.apkinjector:color/m3_sys_color_dark_tertiary = 0x7f05017a
com.iapp.leochen.apkinjector:style/Theme.AppCompat.Light.Dialog = 0x7f11021d
com.iapp.leochen.apkinjector:color/m3_sys_color_dark_surface_dim = 0x7f050178
com.iapp.leochen.apkinjector:dimen/fastscroll_default_thickness = 0x7f060090
com.iapp.leochen.apkinjector:dimen/m3_badge_with_text_size = 0x7f0600b9
com.iapp.leochen.apkinjector:color/m3_sys_color_dark_surface_container_low = 0x7f050176
com.iapp.leochen.apkinjector:style/Base.V14.Widget.MaterialComponents.AutoCompleteTextView = 0x7f1100a1
com.iapp.leochen.apkinjector:dimen/m3_sys_motion_easing_legacy_accelerate_control_x1 = 0x7f0601fe
com.iapp.leochen.apkinjector:color/material_personalized_color_surface_container = 0x7f050299
com.iapp.leochen.apkinjector:color/abc_hint_foreground_material_dark = 0x7f050007
com.iapp.leochen.apkinjector:color/m3_sys_color_dark_surface_container = 0x7f050173
com.iapp.leochen.apkinjector:style/Widget.AppCompat.RatingBar = 0x7f110322
com.iapp.leochen.apkinjector:dimen/m3_comp_filled_card_hover_state_layer_opacity = 0x7f060127
com.iapp.leochen.apkinjector:style/Base.TextAppearance.AppCompat.Body2 = 0x7f110018
com.iapp.leochen.apkinjector:id/above = 0x7f08000e
com.iapp.leochen.apkinjector:color/m3_sys_color_dark_primary_container = 0x7f05016e
com.iapp.leochen.apkinjector:color/m3_sys_color_dark_primary = 0x7f05016d
com.iapp.leochen.apkinjector:layout/mtrl_calendar_year = 0x7f0b0057
com.iapp.leochen.apkinjector:color/m3_sys_color_dark_outline_variant = 0x7f05016c
com.iapp.leochen.apkinjector:style/Widget.AppCompat.ActionBar.TabBar = 0x7f1102ea
com.iapp.leochen.apkinjector:color/m3_sys_color_dark_outline = 0x7f05016b
com.iapp.leochen.apkinjector:id/legacy = 0x7f080100
com.iapp.leochen.apkinjector:attr/transitionEasing = 0x7f0304c3
com.iapp.leochen.apkinjector:color/m3_sys_color_dark_on_tertiary_container = 0x7f05016a
com.iapp.leochen.apkinjector:color/material_dynamic_secondary0 = 0x7f050249
com.iapp.leochen.apkinjector:integer/m3_card_anim_duration_ms = 0x7f09000d
com.iapp.leochen.apkinjector:dimen/m3_appbar_size_medium = 0x7f0600ab
com.iapp.leochen.apkinjector:id/beginOnFirstDraw = 0x7f08005e
com.iapp.leochen.apkinjector:color/m3_sys_color_dark_on_secondary = 0x7f050165
com.iapp.leochen.apkinjector:drawable/mtrl_checkbox_button_icon_indeterminate_unchecked = 0x7f0700c1
com.iapp.leochen.apkinjector:attr/flow_verticalStyle = 0x7f0301f9
com.iapp.leochen.apkinjector:dimen/m3_comp_scrim_container_opacity = 0x7f06016d
com.iapp.leochen.apkinjector:animator/mtrl_chip_state_list_anim = 0x7f020018
com.iapp.leochen.apkinjector:color/m3_sys_color_dark_on_primary_container = 0x7f050164
com.iapp.leochen.apkinjector:style/Platform.AppCompat = 0x7f110137
com.iapp.leochen.apkinjector:layout/mtrl_alert_dialog_title = 0x7f0b0049
com.iapp.leochen.apkinjector:drawable/abc_seekbar_tick_mark_material = 0x7f070064
com.iapp.leochen.apkinjector:style/Base.Widget.AppCompat.Light.PopupMenu = 0x7f1100e4
com.iapp.leochen.apkinjector:attr/navigationContentDescription = 0x7f030355
com.iapp.leochen.apkinjector:drawable/mtrl_popupmenu_background = 0x7f0700d0
com.iapp.leochen.apkinjector:macro/m3_comp_top_app_bar_large_headline_color = 0x7f0c016b
com.iapp.leochen.apkinjector:color/m3_sys_color_dark_inverse_surface = 0x7f05015f
com.iapp.leochen.apkinjector:color/m3_sys_color_dark_inverse_primary = 0x7f05015e
com.iapp.leochen.apkinjector:id/right_icon = 0x7f080187
com.iapp.leochen.apkinjector:color/m3_sys_color_dark_inverse_on_surface = 0x7f05015d
com.iapp.leochen.apkinjector:style/Base.ThemeOverlay.Material3.AutoCompleteTextView = 0x7f110080
com.iapp.leochen.apkinjector:color/m3_sys_color_dark_error_container = 0x7f05015c
com.iapp.leochen.apkinjector:anim/slide_parallel_right = 0x7f010035
com.iapp.leochen.apkinjector:color/m3_sys_color_dark_background = 0x7f05015a
com.iapp.leochen.apkinjector:color/m3_switch_track_tint = 0x7f050159
com.iapp.leochen.apkinjector:style/Base.V7.Theme.AppCompat = 0x7f1100bb
com.iapp.leochen.apkinjector:color/m3_slider_thumb_color = 0x7f050156
com.iapp.leochen.apkinjector:color/m3_slider_inactive_track_color = 0x7f050154
com.iapp.leochen.apkinjector:attr/crossfade = 0x7f03015d
com.iapp.leochen.apkinjector:dimen/abc_dialog_fixed_height_minor = 0x7f06001d
com.iapp.leochen.apkinjector:macro/m3_comp_slider_label_label_text_color = 0x7f0c0112
com.iapp.leochen.apkinjector:color/m3_slider_active_track_color_legacy = 0x7f050152
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.BottomNavigationView.Colored = 0x7f1103fc
com.iapp.leochen.apkinjector:color/m3_slider_active_track_color = 0x7f050151
com.iapp.leochen.apkinjector:color/mtrl_btn_bg_color_selector = 0x7f0502b7
com.iapp.leochen.apkinjector:dimen/m3_btn_icon_only_default_size = 0x7f0600d4
com.iapp.leochen.apkinjector:color/m3_selection_control_ripple_color_selector = 0x7f05014f
com.iapp.leochen.apkinjector:attr/badgeGravity = 0x7f030052
com.iapp.leochen.apkinjector:color/abc_secondary_text_material_light = 0x7f050012
com.iapp.leochen.apkinjector:dimen/m3_navigation_rail_item_padding_top = 0x7f0601d2
com.iapp.leochen.apkinjector:attr/dynamicColorThemeOverlay = 0x7f030198
com.iapp.leochen.apkinjector:attr/chipSurfaceColor = 0x7f0300d1
com.iapp.leochen.apkinjector:color/m3_ref_palette_tertiary99 = 0x7f05014d
com.iapp.leochen.apkinjector:style/Theme.Material3.Light.NoActionBar = 0x7f110245
com.iapp.leochen.apkinjector:color/mtrl_popupmenu_overlay_color = 0x7f0502dd
com.iapp.leochen.apkinjector:dimen/mtrl_calendar_selection_baseline_to_top_fullscreen = 0x7f06028f
com.iapp.leochen.apkinjector:color/m3_ref_palette_tertiary70 = 0x7f050149
com.iapp.leochen.apkinjector:macro/m3_comp_outlined_text_field_caret_color = 0x7f0c00b0
com.iapp.leochen.apkinjector:attr/layout_constraintVertical_chainStyle = 0x7f0302a6
com.iapp.leochen.apkinjector:color/m3_ref_palette_tertiary50 = 0x7f050147
com.iapp.leochen.apkinjector:dimen/m3_ripple_pressed_alpha = 0x7f0601d8
com.iapp.leochen.apkinjector:color/m3_ref_palette_tertiary40 = 0x7f050146
com.iapp.leochen.apkinjector:attr/drawableBottomCompat = 0x7f030188
com.iapp.leochen.apkinjector:dimen/abc_dialog_fixed_width_minor = 0x7f06001f
com.iapp.leochen.apkinjector:anim/m3_motion_fade_enter = 0x7f010023
com.iapp.leochen.apkinjector:color/m3_ref_palette_tertiary30 = 0x7f050145
com.iapp.leochen.apkinjector:color/m3_ref_palette_tertiary20 = 0x7f050144
com.iapp.leochen.apkinjector:color/m3_ref_palette_tertiary10 = 0x7f050142
com.iapp.leochen.apkinjector:dimen/abc_list_item_height_small_material = 0x7f060032
com.iapp.leochen.apkinjector:dimen/m3_bottomappbar_fab_cradle_rounded_corner_radius = 0x7f0600c6
com.iapp.leochen.apkinjector:color/m3_ref_palette_tertiary0 = 0x7f050141
com.iapp.leochen.apkinjector:color/m3_ref_palette_secondary99 = 0x7f050140
com.iapp.leochen.apkinjector:id/mtrl_motion_snapshot_view = 0x7f080138
com.iapp.leochen.apkinjector:color/m3_sys_color_light_tertiary = 0x7f0501ec
com.iapp.leochen.apkinjector:color/m3_ref_palette_secondary95 = 0x7f05013f
com.iapp.leochen.apkinjector:attr/popupMenuStyle = 0x7f030389
com.iapp.leochen.apkinjector:color/m3_ref_palette_secondary90 = 0x7f05013e
com.iapp.leochen.apkinjector:dimen/mtrl_calendar_days_of_week_height = 0x7f06027c
com.iapp.leochen.apkinjector:attr/divider = 0x7f03017c
com.iapp.leochen.apkinjector:attr/itemShapeInsetStart = 0x7f030261
com.iapp.leochen.apkinjector:color/m3_ref_palette_secondary60 = 0x7f05013b
com.iapp.leochen.apkinjector:style/Widget.Material3.Button.TextButton = 0x7f11035b
com.iapp.leochen.apkinjector:attr/startIconCheckable = 0x7f0303ee
com.iapp.leochen.apkinjector:color/m3_ref_palette_secondary50 = 0x7f05013a
com.iapp.leochen.apkinjector:dimen/m3_sys_motion_easing_linear_control_y2 = 0x7f06020d
com.iapp.leochen.apkinjector:color/m3_ref_palette_secondary30 = 0x7f050138
com.iapp.leochen.apkinjector:color/m3_ref_palette_secondary100 = 0x7f050136
com.iapp.leochen.apkinjector:attr/textLocale = 0x7f030470
com.iapp.leochen.apkinjector:dimen/m3_appbar_scrim_height_trigger_large = 0x7f0600a7
com.iapp.leochen.apkinjector:style/Widget.AppCompat.CompoundButton.Switch = 0x7f1102fd
com.iapp.leochen.apkinjector:id/mtrl_picker_text_input_range_end = 0x7f08013f
com.iapp.leochen.apkinjector:attr/expandedTitleMarginEnd = 0x7f0301bd
com.iapp.leochen.apkinjector:drawable/$mtrl_checkbox_button_icon_checked_unchecked__1 = 0x7f070012
com.iapp.leochen.apkinjector:color/m3_ref_palette_secondary0 = 0x7f050134
com.iapp.leochen.apkinjector:color/highlighted_text_material_light = 0x7f05005f
com.iapp.leochen.apkinjector:color/m3_ref_palette_primary99 = 0x7f050133
com.iapp.leochen.apkinjector:color/m3_ref_palette_primary95 = 0x7f050132
com.iapp.leochen.apkinjector:style/Base.TextAppearance.MaterialComponents.Headline6 = 0x7f110046
com.iapp.leochen.apkinjector:attr/behavior_overlapTop = 0x7f03006e
com.iapp.leochen.apkinjector:dimen/abc_text_size_display_3_material = 0x7f060045
com.iapp.leochen.apkinjector:color/m3_ref_palette_primary90 = 0x7f050131
com.iapp.leochen.apkinjector:color/m3_ref_palette_primary70 = 0x7f05012f
com.iapp.leochen.apkinjector:id/ratio = 0x7f080181
com.iapp.leochen.apkinjector:drawable/material_ic_menu_arrow_down_black_24dp = 0x7f0700b8
com.iapp.leochen.apkinjector:drawable/abc_list_selector_background_transition_holo_dark = 0x7f070053
com.iapp.leochen.apkinjector:color/m3_ref_palette_primary60 = 0x7f05012e
com.iapp.leochen.apkinjector:styleable/CoordinatorLayout = 0x7f12002c
com.iapp.leochen.apkinjector:color/m3_ref_palette_primary50 = 0x7f05012d
com.iapp.leochen.apkinjector:anim/abc_shrink_fade_out_from_bottom = 0x7f010005
com.iapp.leochen.apkinjector:dimen/abc_edit_text_inset_horizontal_material = 0x7f06002d
com.iapp.leochen.apkinjector:color/m3_ref_palette_primary30 = 0x7f05012b
com.iapp.leochen.apkinjector:dimen/mtrl_btn_padding_left = 0x7f060266
com.iapp.leochen.apkinjector:color/m3_ref_palette_primary100 = 0x7f050129
com.iapp.leochen.apkinjector:integer/m3_sys_motion_duration_medium2 = 0x7f090018
com.iapp.leochen.apkinjector:dimen/m3_bottom_nav_item_active_indicator_height = 0x7f0600bc
com.iapp.leochen.apkinjector:color/design_fab_stroke_top_inner_color = 0x7f050052
com.iapp.leochen.apkinjector:color/m3_ref_palette_primary10 = 0x7f050128
com.iapp.leochen.apkinjector:style/TextAppearance.Material3.SearchView.Prefix = 0x7f1101f4
com.iapp.leochen.apkinjector:color/m3_ref_palette_primary0 = 0x7f050127
com.iapp.leochen.apkinjector:attr/layout_constraintDimensionRatio = 0x7f03028b
com.iapp.leochen.apkinjector:attr/colorOnSecondary = 0x7f030106
com.iapp.leochen.apkinjector:dimen/material_font_1_3_box_collapsed_padding_top = 0x7f06023b
com.iapp.leochen.apkinjector:integer/mtrl_badge_max_character_count = 0x7f09002d
com.iapp.leochen.apkinjector:color/m3_ref_palette_neutral_variant95 = 0x7f050125
com.iapp.leochen.apkinjector:color/m3_ref_palette_neutral_variant80 = 0x7f050123
com.iapp.leochen.apkinjector:color/m3_ref_palette_neutral_variant30 = 0x7f05011e
com.iapp.leochen.apkinjector:style/ThemeOverlay.Design.TextInputEditText = 0x7f11027f
com.iapp.leochen.apkinjector:color/material_personalized_primary_inverse_text_disable_only = 0x7f0502aa
com.iapp.leochen.apkinjector:style/ThemeOverlay.MaterialComponents.DayNight.BottomSheetDialog = 0x7f1102ce
com.iapp.leochen.apkinjector:attr/colorButtonNormal = 0x7f0300f5
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_primary95 = 0x7f0500d9
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_primary_fixed = 0x7f0501c6
com.iapp.leochen.apkinjector:style/Base.Theme.MaterialComponents.Light = 0x7f11006f
com.iapp.leochen.apkinjector:macro/m3_comp_time_input_time_input_field_container_shape = 0x7f0c0147
com.iapp.leochen.apkinjector:color/mtrl_outlined_icon_tint = 0x7f0502db
com.iapp.leochen.apkinjector:color/m3_ref_palette_neutral98 = 0x7f050118
com.iapp.leochen.apkinjector:drawable/abc_ratingbar_indicator_material = 0x7f07005b
com.iapp.leochen.apkinjector:attr/closeIconSize = 0x7f0300e2
com.iapp.leochen.apkinjector:color/design_dark_default_color_surface = 0x7f05003e
com.iapp.leochen.apkinjector:color/m3_ref_palette_neutral96 = 0x7f050117
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_secondary90 = 0x7f0500e5
com.iapp.leochen.apkinjector:color/material_personalized_color_on_background = 0x7f050280
com.iapp.leochen.apkinjector:drawable/abc_scrubber_control_to_pressed_mtrl_000 = 0x7f07005f
com.iapp.leochen.apkinjector:color/m3_ref_palette_neutral92 = 0x7f050114
com.iapp.leochen.apkinjector:color/m3_ref_palette_neutral90 = 0x7f050113
com.iapp.leochen.apkinjector:color/m3_ref_palette_neutral70 = 0x7f050110
com.iapp.leochen.apkinjector:color/material_dynamic_tertiary30 = 0x7f05025a
com.iapp.leochen.apkinjector:dimen/mtrl_calendar_action_confirm_button_min_width = 0x7f060271
com.iapp.leochen.apkinjector:color/m3_ref_palette_neutral40 = 0x7f05010c
com.iapp.leochen.apkinjector:color/m3_ref_palette_neutral4 = 0x7f05010b
com.iapp.leochen.apkinjector:style/Widget.AppCompat.Light.ListPopupWindow = 0x7f110312
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_secondary_fixed = 0x7f0501c8
com.iapp.leochen.apkinjector:macro/m3_comp_top_app_bar_medium_headline_color = 0x7f0c016d
com.iapp.leochen.apkinjector:color/m3_ref_palette_primary20 = 0x7f05012a
com.iapp.leochen.apkinjector:dimen/m3_comp_navigation_bar_container_height = 0x7f060139
com.iapp.leochen.apkinjector:animator/m3_btn_elevated_btn_state_list_anim = 0x7f02000a
com.iapp.leochen.apkinjector:color/m3_ref_palette_neutral20 = 0x7f050107
com.iapp.leochen.apkinjector:attr/motionDurationLong1 = 0x7f03032b
com.iapp.leochen.apkinjector:color/m3_ref_palette_neutral17 = 0x7f050106
com.iapp.leochen.apkinjector:style/TextAppearance.AppCompat.Caption = 0x7f110198
com.iapp.leochen.apkinjector:id/transition_scene_layoutid_cache = 0x7f0801fb
com.iapp.leochen.apkinjector:dimen/mtrl_switch_text_padding = 0x7f0602f6
com.iapp.leochen.apkinjector:color/m3_ref_palette_neutral0 = 0x7f050102
com.iapp.leochen.apkinjector:color/m3_ref_palette_error80 = 0x7f0500fe
com.iapp.leochen.apkinjector:dimen/m3_back_progress_bottom_container_max_scale_x_distance = 0x7f0600ac
com.iapp.leochen.apkinjector:dimen/m3_comp_suggestion_chip_with_leading_icon_leading_icon_size = 0x7f06018e
com.iapp.leochen.apkinjector:attr/thumbTrackGapSize = 0x7f030489
com.iapp.leochen.apkinjector:color/m3_ref_palette_error70 = 0x7f0500fd
com.iapp.leochen.apkinjector:style/Widget.Material3.MaterialCalendar.DayTextView = 0x7f1103a1
com.iapp.leochen.apkinjector:color/m3_ref_palette_error60 = 0x7f0500fc
com.iapp.leochen.apkinjector:color/m3_ref_palette_error40 = 0x7f0500fa
com.iapp.leochen.apkinjector:color/m3_ref_palette_error20 = 0x7f0500f8
com.iapp.leochen.apkinjector:id/fill_horizontal = 0x7f0800cb
com.iapp.leochen.apkinjector:color/material_personalized_color_text_hint_foreground_inverse = 0x7f0502a3
com.iapp.leochen.apkinjector:styleable/AppCompatTextView = 0x7f120011
com.iapp.leochen.apkinjector:color/material_personalized_color_primary = 0x7f05028e
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_tertiary80 = 0x7f0500f1
com.iapp.leochen.apkinjector:color/design_dark_default_color_on_surface = 0x7f050038
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_tertiary70 = 0x7f0500f0
com.iapp.leochen.apkinjector:id/sharedValueUnset = 0x7f0801a3
com.iapp.leochen.apkinjector:color/m3_sys_color_dark_surface_container_lowest = 0x7f050177
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_tertiary60 = 0x7f0500ef
com.iapp.leochen.apkinjector:drawable/abc_btn_borderless_material = 0x7f07002b
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_tertiary50 = 0x7f0500ee
com.iapp.leochen.apkinjector:id/textinput_helper_text = 0x7f0801e5
com.iapp.leochen.apkinjector:attr/chipStrokeWidth = 0x7f0300cf
com.iapp.leochen.apkinjector:drawable/abc_ic_voice_search_api_material = 0x7f07004a
com.iapp.leochen.apkinjector:attr/paddingLeftSystemWindowInsets = 0x7f03036d
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_tertiary40 = 0x7f0500ed
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_tertiary20 = 0x7f0500eb
com.iapp.leochen.apkinjector:style/ThemeOverlay.MaterialComponents.Dark = 0x7f1102cc
com.iapp.leochen.apkinjector:macro/m3_comp_navigation_rail_inactive_label_text_color = 0x7f0c009d
com.iapp.leochen.apkinjector:macro/m3_comp_dialog_container_shape = 0x7f0c0023
com.iapp.leochen.apkinjector:dimen/mtrl_snackbar_background_overlay_color_alpha = 0x7f0602f2
com.iapp.leochen.apkinjector:style/Widget.AppCompat.ListMenuView = 0x7f110318
com.iapp.leochen.apkinjector:attr/thumbStrokeWidth = 0x7f030485
com.iapp.leochen.apkinjector:dimen/design_navigation_icon_size = 0x7f060077
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_secondary99 = 0x7f0500e7
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.AppBarLayout.Primary = 0x7f1103f0
com.iapp.leochen.apkinjector:color/material_slider_thumb_color = 0x7f0502b1
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_dark_primary = 0x7f05018f
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_secondary60 = 0x7f0500e2
com.iapp.leochen.apkinjector:interpolator/btn_checkbox_checked_mtrl_animation_interpolator_0 = 0x7f0a0000
com.iapp.leochen.apkinjector:attr/expanded = 0x7f0301b8
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_secondary50 = 0x7f0500e1
com.iapp.leochen.apkinjector:dimen/m3_sys_motion_easing_legacy_accelerate_control_y1 = 0x7f060200
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.TabLayout = 0x7f11044a
com.iapp.leochen.apkinjector:attr/showMotionSpec = 0x7f0303ce
com.iapp.leochen.apkinjector:attr/paddingStart = 0x7f03036f
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_secondary100 = 0x7f0500dd
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_secondary10 = 0x7f0500dc
com.iapp.leochen.apkinjector:attr/counterMaxLength = 0x7f030158
com.iapp.leochen.apkinjector:attr/tabMaxWidth = 0x7f030425
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_primary90 = 0x7f0500d8
com.iapp.leochen.apkinjector:attr/layout_constraintHeight_min = 0x7f030294
com.iapp.leochen.apkinjector:attr/cardMaxElevation = 0x7f03009e
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_primary80 = 0x7f0500d7
com.iapp.leochen.apkinjector:macro/m3_comp_navigation_drawer_active_focus_icon_color = 0x7f0c0078
com.iapp.leochen.apkinjector:attr/checkMarkTint = 0x7f0300b0
com.iapp.leochen.apkinjector:string/mtrl_picker_announce_current_selection = 0x7f10006f
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_primary0 = 0x7f0500ce
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_neutral_variant96 = 0x7f0500cb
com.iapp.leochen.apkinjector:attr/cardForegroundColor = 0x7f03009d
com.iapp.leochen.apkinjector:color/m3_ref_palette_neutral50 = 0x7f05010d
com.iapp.leochen.apkinjector:layout/mtrl_calendar_days_of_week = 0x7f0b0050
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_neutral_variant95 = 0x7f0500ca
com.iapp.leochen.apkinjector:dimen/mtrl_exposed_dropdown_menu_popup_vertical_padding = 0x7f0602a5
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_dark_on_surface_variant = 0x7f05018a
com.iapp.leochen.apkinjector:bool/abc_config_actionMenuItemAllCaps = 0x7f040001
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_neutral_variant94 = 0x7f0500c9
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_neutral_variant70 = 0x7f0500c4
com.iapp.leochen.apkinjector:styleable/NavigationRailView = 0x7f12006c
com.iapp.leochen.apkinjector:style/Widget.Material3.NavigationRailView = 0x7f1103bd
com.iapp.leochen.apkinjector:dimen/material_input_text_to_prefix_suffix_padding = 0x7f060240
com.iapp.leochen.apkinjector:attr/onPositiveCross = 0x7f030364
com.iapp.leochen.apkinjector:attr/constraints = 0x7f030137
com.iapp.leochen.apkinjector:style/Base.Widget.Material3.CollapsingToolbar = 0x7f110103
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_neutral_variant6 = 0x7f0500c2
com.iapp.leochen.apkinjector:style/Platform.Widget.AppCompat.Spinner = 0x7f110144
com.iapp.leochen.apkinjector:layout/select_dialog_item_material = 0x7f0b006d
com.iapp.leochen.apkinjector:drawable/material_ic_clear_black_24dp = 0x7f0700b2
com.iapp.leochen.apkinjector:attr/flow_maxElementsWrap = 0x7f0301f4
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_neutral_variant40 = 0x7f0500c0
com.iapp.leochen.apkinjector:attr/colorSecondaryFixed = 0x7f03011d
com.iapp.leochen.apkinjector:drawable/tooltip_frame_dark = 0x7f0700ee
com.iapp.leochen.apkinjector:dimen/m3_comp_input_chip_container_height = 0x7f060131
com.iapp.leochen.apkinjector:attr/state_collapsible = 0x7f0303f8
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_neutral_variant4 = 0x7f0500bf
com.iapp.leochen.apkinjector:dimen/m3_extended_fab_end_padding = 0x7f0601b0
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_neutral_variant24 = 0x7f0500bd
com.iapp.leochen.apkinjector:attr/dayInvalidStyle = 0x7f03016c
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_neutral99 = 0x7f0500b5
com.iapp.leochen.apkinjector:attr/mock_showDiagonals = 0x7f030324
com.iapp.leochen.apkinjector:drawable/$mtrl_checkbox_button_icon_indeterminate_unchecked__1 = 0x7f070016
com.iapp.leochen.apkinjector:attr/scrimBackground = 0x7f0303b0
com.iapp.leochen.apkinjector:attr/layout_anchor = 0x7f03027a
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_primary50 = 0x7f0500d4
com.iapp.leochen.apkinjector:attr/counterOverflowTextColor = 0x7f03015a
com.iapp.leochen.apkinjector:style/TextAppearance.Material3.SearchView = 0x7f1101f3
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_neutral96 = 0x7f0500b3
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_neutral98 = 0x7f0500b4
com.iapp.leochen.apkinjector:id/tabMode = 0x7f0801c9
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_primary30 = 0x7f0500d2
com.iapp.leochen.apkinjector:style/Widget.Material3.Toolbar.OnSurface = 0x7f1103e8
com.iapp.leochen.apkinjector:style/ThemeOverlay.Material3.BottomAppBar.Legacy = 0x7f110288
com.iapp.leochen.apkinjector:attr/behavior_autoShrink = 0x7f030068
com.iapp.leochen.apkinjector:attr/materialButtonStyle = 0x7f0302e4
com.iapp.leochen.apkinjector:color/m3_ref_palette_neutral_variant10 = 0x7f05011b
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_neutral94 = 0x7f0500b1
com.iapp.leochen.apkinjector:color/m3_timepicker_secondary_text_button_text_color = 0x7f050210
com.iapp.leochen.apkinjector:attr/actionModeStyle = 0x7f03001e
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_neutral90 = 0x7f0500af
com.iapp.leochen.apkinjector:color/design_fab_stroke_end_inner_color = 0x7f050050
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_neutral87 = 0x7f0500ae
com.iapp.leochen.apkinjector:dimen/m3_comp_fab_primary_small_container_height = 0x7f06011f
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_neutral60 = 0x7f0500ab
com.iapp.leochen.apkinjector:id/italic = 0x7f0800f8
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_neutral4 = 0x7f0500a7
com.iapp.leochen.apkinjector:attr/tickRadiusInactive = 0x7f030492
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_neutral24 = 0x7f0500a5
com.iapp.leochen.apkinjector:attr/SharedValueId = 0x7f030001
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_neutral22 = 0x7f0500a4
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_neutral20 = 0x7f0500a3
com.iapp.leochen.apkinjector:attr/materialCalendarDayOfWeekLabel = 0x7f0302e7
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_neutral17 = 0x7f0500a2
com.iapp.leochen.apkinjector:id/collapseActionView = 0x7f080080
com.iapp.leochen.apkinjector:color/m3_ref_palette_black = 0x7f05009d
com.iapp.leochen.apkinjector:attr/chipIcon = 0x7f0300c2
com.iapp.leochen.apkinjector:color/m3_radiobutton_ripple_tint = 0x7f05009c
com.iapp.leochen.apkinjector:color/m3_fab_ripple_color_selector = 0x7f05008a
com.iapp.leochen.apkinjector:color/m3_navigation_rail_item_with_indicator_label_tint = 0x7f050097
com.iapp.leochen.apkinjector:color/m3_default_color_secondary_text = 0x7f05007b
com.iapp.leochen.apkinjector:color/m3_navigation_rail_item_with_indicator_icon_tint = 0x7f050096
com.iapp.leochen.apkinjector:color/abc_tint_spinner = 0x7f050017
com.iapp.leochen.apkinjector:color/m3_navigation_bar_item_with_indicator_label_tint = 0x7f050090
com.iapp.leochen.apkinjector:color/m3_filled_icon_button_container_color_selector = 0x7f05008b
com.iapp.leochen.apkinjector:dimen/abc_text_size_button_material = 0x7f060041
com.iapp.leochen.apkinjector:dimen/m3_comp_outlined_button_disabled_outline_opacity = 0x7f06014e
com.iapp.leochen.apkinjector:macro/m3_comp_navigation_bar_active_focus_state_layer_color = 0x7f0c0061
com.iapp.leochen.apkinjector:id/material_timepicker_container = 0x7f08011c
com.iapp.leochen.apkinjector:color/material_dynamic_neutral_variant0 = 0x7f05022f
com.iapp.leochen.apkinjector:attr/behavior_halfExpandedRatio = 0x7f03006c
com.iapp.leochen.apkinjector:color/m3_fab_efab_background_color_selector = 0x7f050088
com.iapp.leochen.apkinjector:attr/contentInsetRight = 0x7f03013d
com.iapp.leochen.apkinjector:attr/endIconMode = 0x7f0301a6
com.iapp.leochen.apkinjector:dimen/m3_nav_badge_with_text_vertical_offset = 0x7f0601bd
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_tertiary0 = 0x7f0500e8
com.iapp.leochen.apkinjector:color/material_dynamic_secondary95 = 0x7f050254
com.iapp.leochen.apkinjector:color/m3_elevated_chip_background_color = 0x7f050087
com.iapp.leochen.apkinjector:attr/fastScrollHorizontalTrackDrawable = 0x7f0301d5
com.iapp.leochen.apkinjector:color/m3_ref_palette_neutral_variant20 = 0x7f05011d
com.iapp.leochen.apkinjector:color/m3_sys_color_dark_error = 0x7f05015b
com.iapp.leochen.apkinjector:color/m3_ref_palette_neutral_variant90 = 0x7f050124
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_neutral_variant99 = 0x7f0500cd
com.iapp.leochen.apkinjector:anim/btn_radio_to_off_mtrl_dot_group_animation = 0x7f010012
com.iapp.leochen.apkinjector:layout/mtrl_auto_complete_simple_item = 0x7f0b004d
com.iapp.leochen.apkinjector:id/dragClockwise = 0x7f0800a8
com.iapp.leochen.apkinjector:color/material_on_surface_emphasis_medium = 0x7f050276
com.iapp.leochen.apkinjector:color/m3_dynamic_default_color_secondary_text = 0x7f050082
com.iapp.leochen.apkinjector:color/m3_sys_color_dark_on_tertiary = 0x7f050169
com.iapp.leochen.apkinjector:drawable/m3_popupmenu_background_overlay = 0x7f0700a9
com.iapp.leochen.apkinjector:macro/m3_comp_fab_secondary_container_color = 0x7f0c003b
com.iapp.leochen.apkinjector:dimen/m3_comp_bottom_app_bar_container_height = 0x7f060102
com.iapp.leochen.apkinjector:attr/layout_scrollEffect = 0x7f0302bb
com.iapp.leochen.apkinjector:color/abc_search_url_text = 0x7f05000d
com.iapp.leochen.apkinjector:attr/paddingStartSystemWindowInsets = 0x7f030370
com.iapp.leochen.apkinjector:color/m3_dynamic_dark_default_color_secondary_text = 0x7f05007d
com.iapp.leochen.apkinjector:color/design_fab_stroke_top_outer_color = 0x7f050053
com.iapp.leochen.apkinjector:attr/dayTodayStyle = 0x7f03016f
com.iapp.leochen.apkinjector:style/Base.TextAppearance.AppCompat.Medium.Inverse = 0x7f110026
com.iapp.leochen.apkinjector:color/m3_dark_primary_text_disable_only = 0x7f050079
com.iapp.leochen.apkinjector:style/Widget.Material3.Chip.Assist.Elevated = 0x7f110369
com.iapp.leochen.apkinjector:dimen/mtrl_progress_circular_size_medium = 0x7f0602db
com.iapp.leochen.apkinjector:color/m3_dark_hint_foreground = 0x7f050078
com.iapp.leochen.apkinjector:color/material_on_primary_emphasis_high_type = 0x7f050272
com.iapp.leochen.apkinjector:attr/trackDecorationTintMode = 0x7f0304ba
com.iapp.leochen.apkinjector:color/design_dark_default_color_primary_dark = 0x7f05003a
com.iapp.leochen.apkinjector:color/m3_dark_default_color_primary_text = 0x7f050075
com.iapp.leochen.apkinjector:attr/title = 0x7f030497
com.iapp.leochen.apkinjector:dimen/material_helper_text_font_1_3_padding_top = 0x7f06023f
com.iapp.leochen.apkinjector:style/ShapeAppearance.Material3.NavigationBarView.ActiveIndicator = 0x7f110178
com.iapp.leochen.apkinjector:attr/customFloatValue = 0x7f030166
com.iapp.leochen.apkinjector:dimen/m3_comp_suggestion_chip_container_height = 0x7f06018a
com.iapp.leochen.apkinjector:interpolator/mtrl_fast_out_linear_in = 0x7f0a000e
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_light_error = 0x7f05019f
com.iapp.leochen.apkinjector:color/m3_checkbox_button_tint = 0x7f05006f
com.iapp.leochen.apkinjector:attr/materialCalendarHeaderConfirmButton = 0x7f0302ea
com.iapp.leochen.apkinjector:dimen/tooltip_vertical_padding = 0x7f060320
com.iapp.leochen.apkinjector:style/TextAppearance.MaterialComponents.Headline3 = 0x7f110200
com.iapp.leochen.apkinjector:style/ShapeAppearance.MaterialComponents = 0x7f11017b
com.iapp.leochen.apkinjector:integer/m3_btn_anim_delay_ms = 0x7f09000a
com.iapp.leochen.apkinjector:dimen/mtrl_btn_text_btn_icon_padding = 0x7f06026c
com.iapp.leochen.apkinjector:color/m3_card_ripple_color = 0x7f05006c
com.iapp.leochen.apkinjector:color/m3_card_foreground_color = 0x7f05006b
com.iapp.leochen.apkinjector:drawable/mtrl_checkbox_button_unchecked_checked = 0x7f0700c4
com.iapp.leochen.apkinjector:attr/dividerPadding = 0x7f030181
com.iapp.leochen.apkinjector:attr/animateMenuItems = 0x7f030032
com.iapp.leochen.apkinjector:color/m3_calendar_item_stroke_color = 0x7f05006a
com.iapp.leochen.apkinjector:attr/flow_verticalBias = 0x7f0301f7
com.iapp.leochen.apkinjector:macro/m3_comp_bottom_app_bar_container_color = 0x7f0c0005
com.iapp.leochen.apkinjector:color/m3_button_outline_color_selector = 0x7f050066
com.iapp.leochen.apkinjector:id/beginning = 0x7f08005f
com.iapp.leochen.apkinjector:color/m3_bottom_sheet_drag_handle_color = 0x7f050063
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_neutral30 = 0x7f0500a6
com.iapp.leochen.apkinjector:style/TextAppearance.M3.Sys.Typescale.HeadlineSmall = 0x7f1101dc
com.iapp.leochen.apkinjector:color/m3_hint_foreground = 0x7f05008d
com.iapp.leochen.apkinjector:color/m3_sys_color_primary_fixed = 0x7f0501f4
com.iapp.leochen.apkinjector:style/MaterialAlertDialog.MaterialComponents.Picker.Date.Calendar = 0x7f11012f
com.iapp.leochen.apkinjector:color/foreground_material_light = 0x7f05005d
com.iapp.leochen.apkinjector:attr/buttonCompat = 0x7f03008f
com.iapp.leochen.apkinjector:attr/customBoolean = 0x7f030162
com.iapp.leochen.apkinjector:color/foreground_material_dark = 0x7f05005c
com.iapp.leochen.apkinjector:color/design_snackbar_background_color = 0x7f050055
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_primary40 = 0x7f0500d3
com.iapp.leochen.apkinjector:color/design_fab_stroke_end_outer_color = 0x7f050051
com.iapp.leochen.apkinjector:macro/m3_comp_top_app_bar_small_container_color = 0x7f0c016f
com.iapp.leochen.apkinjector:dimen/m3_searchview_height = 0x7f0601e4
com.iapp.leochen.apkinjector:style/Widget.Material3.MaterialCalendar.HeaderDivider = 0x7f1103a4
com.iapp.leochen.apkinjector:color/design_fab_shadow_start_color = 0x7f05004f
com.iapp.leochen.apkinjector:attr/indicatorDirectionCircular = 0x7f030243
com.iapp.leochen.apkinjector:style/Theme.AppCompat.Dialog.Alert = 0x7f110217
com.iapp.leochen.apkinjector:id/with_icon = 0x7f080211
com.iapp.leochen.apkinjector:dimen/design_bottom_navigation_height = 0x7f060063
com.iapp.leochen.apkinjector:color/design_default_color_surface = 0x7f05004b
com.iapp.leochen.apkinjector:attr/maxWidth = 0x7f030315
com.iapp.leochen.apkinjector:color/design_default_color_secondary = 0x7f050049
com.iapp.leochen.apkinjector:dimen/mtrl_calendar_year_horizontal_padding = 0x7f060298
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_secondary20 = 0x7f0500de
com.iapp.leochen.apkinjector:color/m3_ref_palette_neutral_variant60 = 0x7f050121
com.iapp.leochen.apkinjector:color/design_default_color_primary_variant = 0x7f050048
com.iapp.leochen.apkinjector:style/Widget.Material3.AutoCompleteTextView.OutlinedBox.Dense = 0x7f110345
com.iapp.leochen.apkinjector:color/design_default_color_background = 0x7f05003f
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.Button.UnelevatedButton.Icon = 0x7f11040b
com.iapp.leochen.apkinjector:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse = 0x7f110034
com.iapp.leochen.apkinjector:color/m3_sys_color_dark_secondary = 0x7f05016f
com.iapp.leochen.apkinjector:attr/buttonPanelSideLayout = 0x7f030095
com.iapp.leochen.apkinjector:attr/borderWidth = 0x7f030076
com.iapp.leochen.apkinjector:color/design_dark_default_color_secondary_variant = 0x7f05003d
com.iapp.leochen.apkinjector:color/design_dark_default_color_primary_variant = 0x7f05003b
com.iapp.leochen.apkinjector:color/design_dark_default_color_on_secondary = 0x7f050037
com.iapp.leochen.apkinjector:attr/singleChoiceItemLayout = 0x7f0303d9
com.iapp.leochen.apkinjector:attr/cardUseCompatPadding = 0x7f0300a0
com.iapp.leochen.apkinjector:color/design_dark_default_color_on_background = 0x7f050034
com.iapp.leochen.apkinjector:drawable/mtrl_switch_track = 0x7f0700dc
com.iapp.leochen.apkinjector:macro/m3_comp_extended_fab_primary_label_text_type = 0x7f0c002f
com.iapp.leochen.apkinjector:attr/sideSheetDialogTheme = 0x7f0303d3
com.iapp.leochen.apkinjector:dimen/m3_large_text_vertical_offset_adjustment = 0x7f0601bb
com.iapp.leochen.apkinjector:styleable/Insets = 0x7f12003f
com.iapp.leochen.apkinjector:id/centerInside = 0x7f080071
com.iapp.leochen.apkinjector:color/design_bottom_navigation_shadow_color = 0x7f050030
com.iapp.leochen.apkinjector:color/cardview_shadow_end_color = 0x7f05002e
com.iapp.leochen.apkinjector:integer/m3_sys_shape_corner_full_corner_family = 0x7f090022
com.iapp.leochen.apkinjector:color/design_icon_tint = 0x7f050054
com.iapp.leochen.apkinjector:color/cardview_light_background = 0x7f05002d
com.iapp.leochen.apkinjector:layout/abc_search_dropdown_item_icons_2line = 0x7f0b0018
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_neutral10 = 0x7f05009f
com.iapp.leochen.apkinjector:color/call_notification_answer_color = 0x7f05002a
com.iapp.leochen.apkinjector:dimen/abc_star_small = 0x7f06003d
com.iapp.leochen.apkinjector:color/button_material_dark = 0x7f050028
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_primary10 = 0x7f0500cf
com.iapp.leochen.apkinjector:attr/lastBaselineToBottomHeight = 0x7f030274
com.iapp.leochen.apkinjector:attr/backgroundInsetStart = 0x7f03004b
com.iapp.leochen.apkinjector:color/bright_foreground_material_dark = 0x7f050026
com.iapp.leochen.apkinjector:color/error_color_material_dark = 0x7f05005a
com.iapp.leochen.apkinjector:attr/buttonIconTintMode = 0x7f030094
com.iapp.leochen.apkinjector:color/bright_foreground_inverse_material_light = 0x7f050025
com.iapp.leochen.apkinjector:attr/alertDialogTheme = 0x7f03002c
com.iapp.leochen.apkinjector:id/dependency_ordering = 0x7f080099
com.iapp.leochen.apkinjector:color/background_material_light = 0x7f050020
com.iapp.leochen.apkinjector:attr/paddingTopSystemWindowInsets = 0x7f030372
com.iapp.leochen.apkinjector:attr/strokeColor = 0x7f030402
com.iapp.leochen.apkinjector:macro/m3_comp_switch_unselected_pressed_handle_color = 0x7f0c013b
com.iapp.leochen.apkinjector:color/background_floating_material_dark = 0x7f05001d
com.iapp.leochen.apkinjector:menu/bottom_navigation_menu = 0x7f0d0000
com.iapp.leochen.apkinjector:color/androidx_core_secondary_text_default_material_light = 0x7f05001c
com.iapp.leochen.apkinjector:attr/floatingActionButtonSmallSecondaryStyle = 0x7f0301e1
com.iapp.leochen.apkinjector:color/accent_material_light = 0x7f05001a
com.iapp.leochen.apkinjector:attr/textAppearanceSubtitle2 = 0x7f03045b
com.iapp.leochen.apkinjector:attr/suffixTextAppearance = 0x7f030410
com.iapp.leochen.apkinjector:color/accent_material_dark = 0x7f050019
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_light_surface_container_highest = 0x7f0501b9
com.iapp.leochen.apkinjector:attr/colorOnPrimaryFixed = 0x7f030103
com.iapp.leochen.apkinjector:attr/itemShapeAppearanceOverlay = 0x7f03025d
com.iapp.leochen.apkinjector:color/material_dynamic_color_light_error = 0x7f05021e
com.iapp.leochen.apkinjector:attr/drawableTopCompat = 0x7f030190
com.iapp.leochen.apkinjector:color/abc_primary_text_material_light = 0x7f05000c
com.iapp.leochen.apkinjector:layout/mtrl_picker_header_toggle = 0x7f0b0062
com.iapp.leochen.apkinjector:attr/spanCount = 0x7f0303e1
com.iapp.leochen.apkinjector:drawable/notification_tile_bg = 0x7f0700eb
com.iapp.leochen.apkinjector:attr/queryHint = 0x7f030397
com.iapp.leochen.apkinjector:style/Theme.Material3.Light = 0x7f11023f
com.iapp.leochen.apkinjector:color/abc_primary_text_material_dark = 0x7f05000b
com.iapp.leochen.apkinjector:color/abc_color_highlight_material = 0x7f050004
com.iapp.leochen.apkinjector:color/dim_foreground_disabled_material_light = 0x7f050057
com.iapp.leochen.apkinjector:style/ThemeOverlay.MaterialComponents.Dialog = 0x7f1102cf
com.iapp.leochen.apkinjector:color/dim_foreground_material_light = 0x7f050059
com.iapp.leochen.apkinjector:dimen/mtrl_navigation_rail_text_bottom_margin = 0x7f0602d2
com.iapp.leochen.apkinjector:id/contentPanel = 0x7f080086
com.iapp.leochen.apkinjector:attr/maxNumber = 0x7f030313
com.iapp.leochen.apkinjector:color/abc_background_cache_hint_selector_material_dark = 0x7f050000
com.iapp.leochen.apkinjector:dimen/m3_alert_dialog_icon_size = 0x7f0600a2
com.iapp.leochen.apkinjector:bool/mtrl_btn_textappearance_all_caps = 0x7f040002
com.iapp.leochen.apkinjector:dimen/mtrl_low_ripple_hovered_alpha = 0x7f0602c0
com.iapp.leochen.apkinjector:id/mtrl_child_content_container = 0x7f080136
com.iapp.leochen.apkinjector:bool/abc_action_bar_embed_tabs = 0x7f040000
com.iapp.leochen.apkinjector:dimen/mtrl_slider_tick_min_spacing = 0x7f0602eb
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.Button.OutlinedButton = 0x7f110402
com.iapp.leochen.apkinjector:color/call_notification_decline_color = 0x7f05002b
com.iapp.leochen.apkinjector:style/Base.Widget.MaterialComponents.TextInputEditText = 0x7f11011d
com.iapp.leochen.apkinjector:attr/motion_triggerOnCollision = 0x7f030352
com.iapp.leochen.apkinjector:color/bright_foreground_inverse_material_dark = 0x7f050024
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense.ExposedDropdownMenu = 0x7f110457
com.iapp.leochen.apkinjector:color/m3_sys_color_dark_tertiary_container = 0x7f05017b
com.iapp.leochen.apkinjector:style/TextAppearance.MaterialComponents.Chip = 0x7f1101fd
com.iapp.leochen.apkinjector:attr/yearTodayStyle = 0x7f0304ec
com.iapp.leochen.apkinjector:dimen/m3_btn_elevation = 0x7f0600d0
com.iapp.leochen.apkinjector:attr/windowMinWidthMajor = 0x7f0304e7
com.iapp.leochen.apkinjector:attr/layout_constraintGuide_end = 0x7f03028f
com.iapp.leochen.apkinjector:attr/singleSelection = 0x7f0303db
com.iapp.leochen.apkinjector:attr/actionBarTheme = 0x7f03000b
com.iapp.leochen.apkinjector:attr/dialogCornerRadius = 0x7f030178
com.iapp.leochen.apkinjector:attr/windowFixedHeightMajor = 0x7f0304e3
com.iapp.leochen.apkinjector:style/Base.ThemeOverlay.AppCompat.Light = 0x7f11007f
com.iapp.leochen.apkinjector:attr/windowActionModeOverlay = 0x7f0304e2
com.iapp.leochen.apkinjector:attr/windowActionBarOverlay = 0x7f0304e1
com.iapp.leochen.apkinjector:macro/m3_comp_time_picker_time_selector_label_text_type = 0x7f0c015f
com.iapp.leochen.apkinjector:color/design_fab_shadow_mid_color = 0x7f05004e
com.iapp.leochen.apkinjector:dimen/mtrl_exposed_dropdown_menu_popup_vertical_offset = 0x7f0602a4
com.iapp.leochen.apkinjector:id/info = 0x7f0800f4
com.iapp.leochen.apkinjector:attr/checkedChip = 0x7f0300b4
com.iapp.leochen.apkinjector:attr/expandedTitleMarginStart = 0x7f0301be
com.iapp.leochen.apkinjector:integer/material_motion_duration_short_1 = 0x7f09002a
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_tertiary99 = 0x7f0500f4
com.iapp.leochen.apkinjector:attr/foregroundInsidePadding = 0x7f030209
com.iapp.leochen.apkinjector:attr/waveOffset = 0x7f0304db
com.iapp.leochen.apkinjector:attr/visibilityMode = 0x7f0304d7
com.iapp.leochen.apkinjector:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse = 0x7f110036
com.iapp.leochen.apkinjector:macro/m3_comp_extended_fab_tertiary_container_color = 0x7f0c0034
com.iapp.leochen.apkinjector:attr/barLength = 0x7f030063
com.iapp.leochen.apkinjector:dimen/m3_sys_elevation_level3 = 0x7f0601f3
com.iapp.leochen.apkinjector:attr/compatShadowEnabled = 0x7f030130
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_light_secondary_container = 0x7f0501b4
com.iapp.leochen.apkinjector:attr/viewTransitionOnNegativeCross = 0x7f0304d5
com.iapp.leochen.apkinjector:style/Base.V14.Theme.MaterialComponents.Light = 0x7f110096
com.iapp.leochen.apkinjector:id/action_context_bar = 0x7f08003c
com.iapp.leochen.apkinjector:dimen/m3_comp_assist_chip_elevated_container_elevation = 0x7f0600fb
com.iapp.leochen.apkinjector:attr/switchMinWidth = 0x7f030414
com.iapp.leochen.apkinjector:dimen/abc_alert_dialog_button_bar_height = 0x7f060010
com.iapp.leochen.apkinjector:color/m3_checkbox_button_icon_tint = 0x7f05006e
com.iapp.leochen.apkinjector:attr/viewTransitionOnCross = 0x7f0304d4
com.iapp.leochen.apkinjector:style/Widget.Design.TextInputLayout = 0x7f11033d
com.iapp.leochen.apkinjector:style/Widget.AppCompat.DrawerArrowToggle = 0x7f1102fe
com.iapp.leochen.apkinjector:id/scroll = 0x7f080190
com.iapp.leochen.apkinjector:attr/staggered = 0x7f0303ed
com.iapp.leochen.apkinjector:drawable/notification_icon_background = 0x7f0700e7
com.iapp.leochen.apkinjector:attr/viewInflaterClass = 0x7f0304d2
com.iapp.leochen.apkinjector:style/Base.ThemeOverlay.MaterialComponents.Dialog.Alert.Framework = 0x7f110087
com.iapp.leochen.apkinjector:attr/verticalOffsetWithText = 0x7f0304d1
com.iapp.leochen.apkinjector:dimen/m3_comp_navigation_drawer_container_width = 0x7f06013e
com.iapp.leochen.apkinjector:style/Theme.Material3.Dark.DialogWhenLarge = 0x7f11022e
com.iapp.leochen.apkinjector:string/searchview_navigation_content_description = 0x7f1000a3
com.iapp.leochen.apkinjector:attr/colorOnBackground = 0x7f0300fc
com.iapp.leochen.apkinjector:dimen/m3_appbar_scrim_height_trigger_medium = 0x7f0600a8
com.iapp.leochen.apkinjector:color/m3_ref_palette_neutral6 = 0x7f05010e
com.iapp.leochen.apkinjector:style/MaterialAlertDialog.Material3.Title.Icon = 0x7f110127
com.iapp.leochen.apkinjector:attr/values = 0x7f0304cf
com.iapp.leochen.apkinjector:attr/itemPadding = 0x7f030258
com.iapp.leochen.apkinjector:color/material_grey_300 = 0x7f050264
com.iapp.leochen.apkinjector:style/Base.Theme.AppCompat.Dialog.MinWidth = 0x7f110051
com.iapp.leochen.apkinjector:attr/useCompatPadding = 0x7f0304cc
com.iapp.leochen.apkinjector:macro/m3_comp_date_picker_modal_year_selection_year_selected_label_text_color = 0x7f0c0020
com.iapp.leochen.apkinjector:attr/motionEasingLinear = 0x7f03033d
com.iapp.leochen.apkinjector:attr/upDuration = 0x7f0304cb
com.iapp.leochen.apkinjector:style/Theme.MaterialComponents.Light.Bridge = 0x7f110266
com.iapp.leochen.apkinjector:dimen/cardview_default_radius = 0x7f060054
com.iapp.leochen.apkinjector:id/on = 0x7f08015a
com.iapp.leochen.apkinjector:attr/layout_constraintHeight_percent = 0x7f030295
com.iapp.leochen.apkinjector:macro/m3_comp_sheet_side_detached_container_shape = 0x7f0c0107
com.iapp.leochen.apkinjector:attr/triggerSlack = 0x7f0304c9
com.iapp.leochen.apkinjector:style/Base.Widget.MaterialComponents.Chip = 0x7f110114
com.iapp.leochen.apkinjector:dimen/mtrl_calendar_header_height_fullscreen = 0x7f060282
com.iapp.leochen.apkinjector:macro/m3_comp_search_bar_leading_icon_color = 0x7f0c00eb
com.iapp.leochen.apkinjector:attr/triggerReceiver = 0x7f0304c8
com.iapp.leochen.apkinjector:attr/triggerId = 0x7f0304c7
com.iapp.leochen.apkinjector:attr/layout_goneMarginStart = 0x7f0302b5
com.iapp.leochen.apkinjector:drawable/abc_list_divider_mtrl_alpha = 0x7f07004e
com.iapp.leochen.apkinjector:color/background_floating_material_light = 0x7f05001e
com.iapp.leochen.apkinjector:attr/bottomInsetScrimEnabled = 0x7f030079
com.iapp.leochen.apkinjector:style/Widget.Material3.SearchBar = 0x7f1103c7
com.iapp.leochen.apkinjector:layout/ime_secondary_split_test_activity = 0x7f0b0031
com.iapp.leochen.apkinjector:attr/colorOnErrorContainer = 0x7f030100
com.iapp.leochen.apkinjector:attr/transitionShapeAppearance = 0x7f0304c6
com.iapp.leochen.apkinjector:attr/fontVariationSettings = 0x7f030205
com.iapp.leochen.apkinjector:attr/transitionFlags = 0x7f0304c4
com.iapp.leochen.apkinjector:attr/checkedIconVisible = 0x7f0300bb
com.iapp.leochen.apkinjector:dimen/m3_comp_radio_button_unselected_pressed_state_layer_opacity = 0x7f06016c
com.iapp.leochen.apkinjector:macro/m3_comp_outlined_text_field_focus_outline_color = 0x7f0c00bb
com.iapp.leochen.apkinjector:color/m3_ref_palette_neutral_variant50 = 0x7f050120
com.iapp.leochen.apkinjector:attr/trackThickness = 0x7f0304be
com.iapp.leochen.apkinjector:attr/layout_constraintWidth_min = 0x7f0302ab
com.iapp.leochen.apkinjector:color/primary_text_disabled_material_light = 0x7f0502f7
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_neutral0 = 0x7f05009e
com.iapp.leochen.apkinjector:anim/btn_radio_to_on_mtrl_ring_outer_animation = 0x7f010016
com.iapp.leochen.apkinjector:color/abc_decor_view_status_guard = 0x7f050005
com.iapp.leochen.apkinjector:drawable/gradient_separator = 0x7f070087
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_neutral100 = 0x7f0500a0
com.iapp.leochen.apkinjector:attr/listChoiceBackgroundIndicator = 0x7f0302c6
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_light_secondary = 0x7f0501b3
com.iapp.leochen.apkinjector:attr/trackColor = 0x7f0304b4
com.iapp.leochen.apkinjector:drawable/mtrl_checkbox_button_icon_unchecked_checked = 0x7f0700c2
com.iapp.leochen.apkinjector:color/m3_ref_palette_tertiary95 = 0x7f05014c
com.iapp.leochen.apkinjector:color/m3_sys_color_light_inverse_primary = 0x7f0501d0
com.iapp.leochen.apkinjector:dimen/m3_comp_fab_primary_icon_size = 0x7f06011a
com.iapp.leochen.apkinjector:attr/touchAnchorSide = 0x7f0304b1
com.iapp.leochen.apkinjector:attr/clearsTag = 0x7f0300d9
com.iapp.leochen.apkinjector:attr/checkedIconTint = 0x7f0300ba
com.iapp.leochen.apkinjector:attr/tooltipForegroundColor = 0x7f0304ab
com.iapp.leochen.apkinjector:style/Base.Theme.MaterialComponents.Dialog.Bridge = 0x7f11006b
com.iapp.leochen.apkinjector:drawable/$mtrl_checkbox_button_icon_indeterminate_unchecked__2 = 0x7f070017
com.iapp.leochen.apkinjector:style/Base.TextAppearance.Widget.AppCompat.ExpandedMenu.Item = 0x7f110048
com.iapp.leochen.apkinjector:color/m3_sys_color_light_on_surface_variant = 0x7f0501da
com.iapp.leochen.apkinjector:attr/toolbarSurfaceStyle = 0x7f0304aa
com.iapp.leochen.apkinjector:styleable/ButtonBarLayout = 0x7f120018
com.iapp.leochen.apkinjector:dimen/m3_sys_state_focus_state_layer_opacity = 0x7f06021b
com.iapp.leochen.apkinjector:attr/toolbarStyle = 0x7f0304a9
com.iapp.leochen.apkinjector:id/center_horizontal = 0x7f080072
com.iapp.leochen.apkinjector:attr/toolbarId = 0x7f0304a7
com.iapp.leochen.apkinjector:attr/motionEasingEmphasized = 0x7f030339
com.iapp.leochen.apkinjector:attr/textAppearanceButton = 0x7f03043f
com.iapp.leochen.apkinjector:color/m3_button_foreground_color_selector = 0x7f050065
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.BottomNavigationView = 0x7f1103fb
com.iapp.leochen.apkinjector:drawable/notification_bg_low = 0x7f0700e2
com.iapp.leochen.apkinjector:dimen/abc_action_bar_stacked_tab_max_width = 0x7f06000a
com.iapp.leochen.apkinjector:attr/titleMarginEnd = 0x7f03049d
com.iapp.leochen.apkinjector:attr/checkMarkTintMode = 0x7f0300b1
com.iapp.leochen.apkinjector:macro/m3_comp_switch_unselected_pressed_track_color = 0x7f0c013e
com.iapp.leochen.apkinjector:anim/abc_slide_out_bottom = 0x7f010008
com.iapp.leochen.apkinjector:attr/titleMarginBottom = 0x7f03049c
com.iapp.leochen.apkinjector:attr/titleEnabled = 0x7f03049a
com.iapp.leochen.apkinjector:color/m3_fab_efab_foreground_color_selector = 0x7f050089
com.iapp.leochen.apkinjector:attr/navigationMode = 0x7f030358
com.iapp.leochen.apkinjector:attr/tickMarkTintMode = 0x7f030490
com.iapp.leochen.apkinjector:attr/tickMark = 0x7f03048e
com.iapp.leochen.apkinjector:macro/m3_comp_outlined_button_disabled_outline_color = 0x7f0c00a3
com.iapp.leochen.apkinjector:attr/tickColorActive = 0x7f03048c
com.iapp.leochen.apkinjector:attr/sideSheetModalStyle = 0x7f0303d4
com.iapp.leochen.apkinjector:drawable/abc_spinner_textfield_background_material = 0x7f070067
com.iapp.leochen.apkinjector:layout/abc_cascading_menu_item_layout = 0x7f0b000b
com.iapp.leochen.apkinjector:attr/boxCornerRadiusTopEnd = 0x7f030083
com.iapp.leochen.apkinjector:attr/thumbIconTintMode = 0x7f030482
com.iapp.leochen.apkinjector:integer/bottom_sheet_slide_duration = 0x7f090003
com.iapp.leochen.apkinjector:color/m3_ref_palette_neutral_variant0 = 0x7f05011a
com.iapp.leochen.apkinjector:style/Base.TextAppearance.AppCompat.Display1 = 0x7f11001b
com.iapp.leochen.apkinjector:attr/snackbarStyle = 0x7f0303df
com.iapp.leochen.apkinjector:attr/logo = 0x7f0302d5
com.iapp.leochen.apkinjector:animator/mtrl_fab_transformation_sheet_collapse_spec = 0x7f020020
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.Chip.Filter = 0x7f110411
com.iapp.leochen.apkinjector:attr/thumbIconSize = 0x7f030480
com.iapp.leochen.apkinjector:macro/m3_comp_filled_text_field_error_trailing_icon_color = 0x7f0c004f
com.iapp.leochen.apkinjector:dimen/mtrl_textinput_box_stroke_width_default = 0x7f0602ff
com.iapp.leochen.apkinjector:string/fab_transformation_sheet_behavior = 0x7f100033
com.iapp.leochen.apkinjector:attr/toolbarNavigationButtonStyle = 0x7f0304a8
com.iapp.leochen.apkinjector:dimen/m3_btn_padding_bottom = 0x7f0600d9
com.iapp.leochen.apkinjector:attr/motionEasingEmphasizedAccelerateInterpolator = 0x7f03033a
com.iapp.leochen.apkinjector:attr/thumbIcon = 0x7f03047f
com.iapp.leochen.apkinjector:attr/thumbElevation = 0x7f03047d
com.iapp.leochen.apkinjector:attr/elevationOverlayAccentColor = 0x7f03019d
com.iapp.leochen.apkinjector:attr/thumbColor = 0x7f03047c
com.iapp.leochen.apkinjector:attr/textureWidth = 0x7f030479
com.iapp.leochen.apkinjector:attr/seekBarStyle = 0x7f0303b6
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.MaterialCalendar.HeaderConfirmButton = 0x7f11042a
com.iapp.leochen.apkinjector:id/disjoint = 0x7f0800a6
com.iapp.leochen.apkinjector:attr/textPanY = 0x7f030474
com.iapp.leochen.apkinjector:attr/yearSelectedStyle = 0x7f0304ea
com.iapp.leochen.apkinjector:attr/haloRadius = 0x7f03021b
com.iapp.leochen.apkinjector:style/ThemeOverlay.Material3.AutoCompleteTextView.OutlinedBox.Dense = 0x7f110286
com.iapp.leochen.apkinjector:attr/textOutlineColor = 0x7f030471
com.iapp.leochen.apkinjector:attr/lineHeight = 0x7f0302c3
com.iapp.leochen.apkinjector:attr/collapsingToolbarLayoutLargeSize = 0x7f0300ed
com.iapp.leochen.apkinjector:attr/textInputFilledStyle = 0x7f03046a
com.iapp.leochen.apkinjector:attr/badgeText = 0x7f030058
com.iapp.leochen.apkinjector:attr/textInputFilledDenseStyle = 0x7f030468
com.iapp.leochen.apkinjector:attr/textInputStyle = 0x7f03046f
com.iapp.leochen.apkinjector:color/abc_search_url_text_pressed = 0x7f05000f
com.iapp.leochen.apkinjector:style/ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox.Dense = 0x7f1102df
com.iapp.leochen.apkinjector:attr/textEndPadding = 0x7f030466
com.iapp.leochen.apkinjector:id/antiClockwise = 0x7f080050
com.iapp.leochen.apkinjector:dimen/m3_sys_elevation_level5 = 0x7f0601f5
com.iapp.leochen.apkinjector:attr/maxImageSize = 0x7f030311
com.iapp.leochen.apkinjector:drawable/$mtrl_switch_thumb_checked_pressed__0 = 0x7f070021
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_neutral_variant50 = 0x7f0500c1
com.iapp.leochen.apkinjector:attr/shapeAppearanceCornerMedium = 0x7f0303c0
com.iapp.leochen.apkinjector:attr/textBackgroundZoom = 0x7f030463
com.iapp.leochen.apkinjector:style/Widget.Material3.MaterialCalendar.HeaderCancelButton = 0x7f1103a3
com.iapp.leochen.apkinjector:attr/colorSurfaceContainerHighest = 0x7f030124
com.iapp.leochen.apkinjector:style/Theme.MaterialComponents.DayNight.DarkActionBar = 0x7f11024e
com.iapp.leochen.apkinjector:attr/actionModeCopyDrawable = 0x7f030016
com.iapp.leochen.apkinjector:dimen/m3_extended_fab_icon_padding = 0x7f0601b1
com.iapp.leochen.apkinjector:attr/motionEasingStandardInterpolator = 0x7f030342
com.iapp.leochen.apkinjector:macro/m3_comp_outlined_text_field_label_text_color = 0x7f0c00c2
com.iapp.leochen.apkinjector:attr/textBackgroundPanY = 0x7f030461
com.iapp.leochen.apkinjector:attr/textBackgroundPanX = 0x7f030460
com.iapp.leochen.apkinjector:style/TextAppearance.M3.Sys.Typescale.HeadlineLarge = 0x7f1101da
com.iapp.leochen.apkinjector:style/Base.Theme.MaterialComponents = 0x7f110066
com.iapp.leochen.apkinjector:attr/overlay = 0x7f030369
com.iapp.leochen.apkinjector:attr/textAppearanceTitleSmall = 0x7f03045e
com.iapp.leochen.apkinjector:macro/m3_comp_switch_disabled_unselected_track_color = 0x7f0c011e
com.iapp.leochen.apkinjector:attr/textAppearanceSmallPopupMenu = 0x7f030459
com.iapp.leochen.apkinjector:dimen/m3_comp_navigation_drawer_standard_container_elevation = 0x7f060144
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_neutral_variant92 = 0x7f0500c8
com.iapp.leochen.apkinjector:attr/layout_constrainedWidth = 0x7f030280
com.iapp.leochen.apkinjector:style/Base.Widget.Material3.CompoundButton.Switch = 0x7f110106
com.iapp.leochen.apkinjector:attr/colorOnTertiary = 0x7f03010d
com.iapp.leochen.apkinjector:color/material_dynamic_neutral_variant95 = 0x7f05023a
com.iapp.leochen.apkinjector:layout/m3_alert_dialog = 0x7f0b0034
com.iapp.leochen.apkinjector:attr/textAppearanceOverline = 0x7f030455
com.iapp.leochen.apkinjector:id/textinput_error = 0x7f0801e4
com.iapp.leochen.apkinjector:dimen/abc_action_bar_elevation_material = 0x7f060005
com.iapp.leochen.apkinjector:id/transition_position = 0x7f0801fa
com.iapp.leochen.apkinjector:attr/shortcutMatchRequired = 0x7f0303c7
com.iapp.leochen.apkinjector:style/Base.ThemeOverlay.MaterialComponents.MaterialAlertDialog = 0x7f110089
com.iapp.leochen.apkinjector:id/bottom = 0x7f080063
com.iapp.leochen.apkinjector:attr/textAppearanceListItemSecondary = 0x7f030453
com.iapp.leochen.apkinjector:dimen/abc_text_size_display_4_material = 0x7f060046
com.iapp.leochen.apkinjector:id/jumpToStart = 0x7f0800fb
com.iapp.leochen.apkinjector:attr/textAppearanceLargePopupMenu = 0x7f030450
com.iapp.leochen.apkinjector:attr/textAppearanceLabelSmall = 0x7f03044f
com.iapp.leochen.apkinjector:dimen/design_bottom_navigation_active_item_min_width = 0x7f060060
com.iapp.leochen.apkinjector:attr/itemActiveIndicatorStyle = 0x7f03024e
com.iapp.leochen.apkinjector:attr/materialCalendarHeaderCancelButton = 0x7f0302e9
com.iapp.leochen.apkinjector:styleable/TextEffects = 0x7f12008c
com.iapp.leochen.apkinjector:macro/m3_comp_date_picker_modal_date_label_text_type = 0x7f0c000f
com.iapp.leochen.apkinjector:attr/maxActionInlineWidth = 0x7f03030d
com.iapp.leochen.apkinjector:attr/textAppearanceLabelMedium = 0x7f03044e
com.iapp.leochen.apkinjector:attr/layout_constraintBaseline_toBottomOf = 0x7f030283
com.iapp.leochen.apkinjector:attr/textAppearanceHeadlineSmall = 0x7f03044c
com.iapp.leochen.apkinjector:dimen/m3_badge_vertical_offset = 0x7f0600b6
com.iapp.leochen.apkinjector:attr/closeIconEndPadding = 0x7f0300e1
com.iapp.leochen.apkinjector:color/abc_background_cache_hint_selector_material_light = 0x7f050001
com.iapp.leochen.apkinjector:dimen/mtrl_badge_toolbar_action_menu_item_vertical_offset = 0x7f060251
com.iapp.leochen.apkinjector:attr/textAppearanceHeadline5 = 0x7f030448
com.iapp.leochen.apkinjector:attr/motionEffect_viewTransition = 0x7f03034a
com.iapp.leochen.apkinjector:attr/textBackgroundRotate = 0x7f030462
com.iapp.leochen.apkinjector:dimen/m3_comp_search_view_container_elevation = 0x7f060173
com.iapp.leochen.apkinjector:attr/simpleItems = 0x7f0303d8
com.iapp.leochen.apkinjector:attr/textStartPadding = 0x7f030475
com.iapp.leochen.apkinjector:attr/textAppearanceHeadline3 = 0x7f030446
com.iapp.leochen.apkinjector:style/TextAppearance.Design.CollapsingToolbar.Expanded = 0x7f1101c9
com.iapp.leochen.apkinjector:attr/actionButtonStyle = 0x7f03000d
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_secondary80 = 0x7f0500e4
com.iapp.leochen.apkinjector:attr/layout_constraintBottom_toTopOf = 0x7f030287
com.iapp.leochen.apkinjector:attr/thumbWidth = 0x7f03048a
com.iapp.leochen.apkinjector:dimen/m3_chip_checked_hovered_translation_z = 0x7f0600f3
com.iapp.leochen.apkinjector:styleable/DrawerArrowToggle = 0x7f12002f
com.iapp.leochen.apkinjector:dimen/mtrl_extended_fab_bottom_padding = 0x7f0602a6
com.iapp.leochen.apkinjector:attr/textAppearanceHeadline1 = 0x7f030444
com.iapp.leochen.apkinjector:attr/textAppearanceDisplayMedium = 0x7f030442
com.iapp.leochen.apkinjector:color/design_default_color_error = 0x7f050040
com.iapp.leochen.apkinjector:attr/textAppearanceBody1 = 0x7f03043a
com.iapp.leochen.apkinjector:attr/trackTint = 0x7f0304bf
com.iapp.leochen.apkinjector:attr/telltales_velocityMode = 0x7f030438
com.iapp.leochen.apkinjector:attr/circularProgressIndicatorStyle = 0x7f0300d3
com.iapp.leochen.apkinjector:color/m3_sys_color_light_surface_container_low = 0x7f0501e8
com.iapp.leochen.apkinjector:macro/m3_comp_secondary_navigation_tab_inactive_label_text_color = 0x7f0c00ff
com.iapp.leochen.apkinjector:attr/listItemLayout = 0x7f0302ca
com.iapp.leochen.apkinjector:style/Widget.Material3.Button.IconButton.Filled = 0x7f110356
com.iapp.leochen.apkinjector:attr/telltales_tailColor = 0x7f030436
com.iapp.leochen.apkinjector:style/Base.Widget.Material3.ExtendedFloatingActionButton.Icon = 0x7f110108
com.iapp.leochen.apkinjector:string/m3_sys_motion_easing_emphasized_decelerate = 0x7f10003e
com.iapp.leochen.apkinjector:attr/useDrawerArrowDrawable = 0x7f0304cd
com.iapp.leochen.apkinjector:macro/m3_comp_outlined_button_pressed_outline_color = 0x7f0c00a7
com.iapp.leochen.apkinjector:color/m3_sys_color_dark_on_background = 0x7f050160
com.iapp.leochen.apkinjector:attr/targetId = 0x7f030435
com.iapp.leochen.apkinjector:color/mtrl_switch_thumb_icon_tint = 0x7f0502df
com.iapp.leochen.apkinjector:attr/tabTextColor = 0x7f030433
com.iapp.leochen.apkinjector:attr/scrimAnimationDuration = 0x7f0303af
com.iapp.leochen.apkinjector:attr/tabStyle = 0x7f030431
com.iapp.leochen.apkinjector:attr/layout_constraintTop_toTopOf = 0x7f0302a4
com.iapp.leochen.apkinjector:color/design_default_color_secondary_variant = 0x7f05004a
com.iapp.leochen.apkinjector:style/Widget.Material3.Button.TonalButton.Icon = 0x7f110362
com.iapp.leochen.apkinjector:color/design_error = 0x7f05004c
com.iapp.leochen.apkinjector:attr/tint = 0x7f030494
com.iapp.leochen.apkinjector:style/Base.Theme.Material3.Dark = 0x7f11005a
com.iapp.leochen.apkinjector:attr/tabSecondaryStyle = 0x7f03042e
com.iapp.leochen.apkinjector:attr/tabPaddingStart = 0x7f03042b
com.iapp.leochen.apkinjector:attr/tabPaddingEnd = 0x7f03042a
com.iapp.leochen.apkinjector:attr/tabMode = 0x7f030427
com.iapp.leochen.apkinjector:integer/m3_sys_motion_duration_long2 = 0x7f090014
com.iapp.leochen.apkinjector:drawable/ic_call_answer_low = 0x7f07008c
com.iapp.leochen.apkinjector:styleable/ListPopupWindow = 0x7f12004c
com.iapp.leochen.apkinjector:attr/tabMinWidth = 0x7f030426
com.iapp.leochen.apkinjector:attr/tabInlineLabel = 0x7f030424
com.iapp.leochen.apkinjector:macro/m3_comp_outlined_card_pressed_outline_color = 0x7f0c00af
com.iapp.leochen.apkinjector:attr/iconGravity = 0x7f030232
com.iapp.leochen.apkinjector:attr/dividerHorizontal = 0x7f03017e
com.iapp.leochen.apkinjector:color/abc_tint_switch_track = 0x7f050018
com.iapp.leochen.apkinjector:id/accessibility_custom_action_31 = 0x7f08002a
com.iapp.leochen.apkinjector:attr/tabIndicatorHeight = 0x7f030423
com.iapp.leochen.apkinjector:attr/tabIndicatorColor = 0x7f030420
com.iapp.leochen.apkinjector:attr/tabIndicator = 0x7f03041d
com.iapp.leochen.apkinjector:dimen/mtrl_progress_circular_radius = 0x7f0602d8
com.iapp.leochen.apkinjector:attr/titleTextStyle = 0x7f0304a5
com.iapp.leochen.apkinjector:attr/tabIconTintMode = 0x7f03041c
com.iapp.leochen.apkinjector:attr/textAppearanceHeadline2 = 0x7f030445
com.iapp.leochen.apkinjector:attr/tabGravity = 0x7f03041a
com.iapp.leochen.apkinjector:attr/tabBackground = 0x7f030418
com.iapp.leochen.apkinjector:attr/autoSizeMinTextSize = 0x7f030041
com.iapp.leochen.apkinjector:attr/errorContentDescription = 0x7f0301af
com.iapp.leochen.apkinjector:dimen/mtrl_extended_fab_top_padding = 0x7f0602b2
com.iapp.leochen.apkinjector:attr/switchStyle = 0x7f030416
com.iapp.leochen.apkinjector:style/ShapeAppearance.MaterialComponents.LargeComponent = 0x7f11017d
com.iapp.leochen.apkinjector:attr/fontProviderQuery = 0x7f030202
com.iapp.leochen.apkinjector:attr/suggestionRowLayout = 0x7f030412
com.iapp.leochen.apkinjector:style/Base.Widget.AppCompat.ListPopupWindow = 0x7f1100e7
com.iapp.leochen.apkinjector:dimen/mtrl_card_corner_radius = 0x7f06029d
com.iapp.leochen.apkinjector:attr/errorAccessibilityLabel = 0x7f0301ad
com.iapp.leochen.apkinjector:attr/subtitleTextStyle = 0x7f03040e
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_neutral_variant17 = 0x7f0500ba
com.iapp.leochen.apkinjector:color/material_dynamic_tertiary10 = 0x7f050257
com.iapp.leochen.apkinjector:attr/tabIndicatorAnimationMode = 0x7f03041f
com.iapp.leochen.apkinjector:attr/subtitleTextColor = 0x7f03040d
com.iapp.leochen.apkinjector:id/action_text = 0x7f080044
com.iapp.leochen.apkinjector:dimen/m3_comp_extended_fab_primary_focus_state_layer_opacity = 0x7f06010f
com.iapp.leochen.apkinjector:attr/appBarLayoutStyle = 0x7f030036
com.iapp.leochen.apkinjector:attr/reactiveGuide_animateChange = 0x7f03039e
com.iapp.leochen.apkinjector:color/design_dark_default_color_error = 0x7f050033
com.iapp.leochen.apkinjector:style/Theme.Material3.Light.Dialog = 0x7f110241
com.iapp.leochen.apkinjector:dimen/m3_comp_navigation_bar_hover_state_layer_opacity = 0x7f06013b
com.iapp.leochen.apkinjector:attr/subtitleTextAppearance = 0x7f03040c
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.TextView = 0x7f110459
com.iapp.leochen.apkinjector:color/m3_icon_button_icon_color_selector = 0x7f05008e
com.iapp.leochen.apkinjector:dimen/m3_comp_slider_active_handle_width = 0x7f060183
com.iapp.leochen.apkinjector:drawable/btn_checkbox_checked_to_unchecked_mtrl_animation = 0x7f07007b
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_neutral_variant100 = 0x7f0500b8
com.iapp.leochen.apkinjector:string/hide_bottom_view_on_scroll_behavior = 0x7f100034
com.iapp.leochen.apkinjector:attr/autoCompleteMode = 0x7f03003d
com.iapp.leochen.apkinjector:attr/subheaderTextAppearance = 0x7f030408
com.iapp.leochen.apkinjector:attr/subheaderInsetStart = 0x7f030407
com.iapp.leochen.apkinjector:attr/fontProviderCerts = 0x7f0301fe
com.iapp.leochen.apkinjector:attr/autoSizeTextType = 0x7f030044
com.iapp.leochen.apkinjector:attr/isMaterial3Theme = 0x7f03024c
com.iapp.leochen.apkinjector:id/material_clock_display_and_toggle = 0x7f08010e
com.iapp.leochen.apkinjector:attr/gapBetweenBars = 0x7f03020b
com.iapp.leochen.apkinjector:attr/subheaderInsetEnd = 0x7f030406
com.iapp.leochen.apkinjector:drawable/mtrl_switch_thumb_unchecked = 0x7f0700d9
com.iapp.leochen.apkinjector:attr/onShow = 0x7f030365
com.iapp.leochen.apkinjector:attr/dividerInsetStart = 0x7f030180
com.iapp.leochen.apkinjector:style/Theme.Material3.DayNight.SideSheetDialog = 0x7f110238
com.iapp.leochen.apkinjector:attr/subMenuArrow = 0x7f030404
com.iapp.leochen.apkinjector:attr/backgroundStacked = 0x7f03004f
com.iapp.leochen.apkinjector:id/currentPathText = 0x7f08008e
com.iapp.leochen.apkinjector:attr/centerIfNoTextEnabled = 0x7f0300ad
com.iapp.leochen.apkinjector:style/Widget.AppCompat.EditText = 0x7f110300
com.iapp.leochen.apkinjector:id/x_left = 0x7f080216
com.iapp.leochen.apkinjector:attr/counterTextColor = 0x7f03015c
com.iapp.leochen.apkinjector:attr/colorTertiaryFixedDim = 0x7f03012e
com.iapp.leochen.apkinjector:attr/badgeWithTextShapeAppearanceOverlay = 0x7f030061
com.iapp.leochen.apkinjector:attr/cardViewStyle = 0x7f0300a1
com.iapp.leochen.apkinjector:color/design_dark_default_color_primary = 0x7f050039
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.TimePicker.ImageButton.ShapeAppearance = 0x7f110463
com.iapp.leochen.apkinjector:id/mtrl_anchor_parent = 0x7f08012c
com.iapp.leochen.apkinjector:dimen/mtrl_card_checked_icon_margin = 0x7f06029b
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.MaterialCalendar.Day.Invalid = 0x7f110423
com.iapp.leochen.apkinjector:attr/fontProviderSystemFontFamily = 0x7f030203
com.iapp.leochen.apkinjector:attr/colorOnSurfaceVariant = 0x7f03010c
com.iapp.leochen.apkinjector:attr/strokeWidth = 0x7f030403
com.iapp.leochen.apkinjector:style/Widget.AppCompat.SeekBar = 0x7f110327
com.iapp.leochen.apkinjector:attr/statusBarScrim = 0x7f030401
com.iapp.leochen.apkinjector:color/bright_foreground_material_light = 0x7f050027
com.iapp.leochen.apkinjector:color/m3_button_background_color_selector = 0x7f050064
com.iapp.leochen.apkinjector:attr/layout_constraintWidth = 0x7f0302a8
com.iapp.leochen.apkinjector:attr/state_liftable = 0x7f0303fc
com.iapp.leochen.apkinjector:style/TextAppearance.MaterialComponents.Headline1 = 0x7f1101fe
com.iapp.leochen.apkinjector:attr/state_indeterminate = 0x7f0303fb
com.iapp.leochen.apkinjector:dimen/m3_comp_checkbox_selected_disabled_container_opacity = 0x7f060103
com.iapp.leochen.apkinjector:dimen/m3_comp_suggestion_chip_flat_outline_width = 0x7f06018d
com.iapp.leochen.apkinjector:attr/warmth = 0x7f0304d9
com.iapp.leochen.apkinjector:dimen/m3_sys_motion_easing_standard_decelerate_control_y1 = 0x7f060218
com.iapp.leochen.apkinjector:dimen/m3_comp_time_picker_period_selector_pressed_state_layer_opacity = 0x7f0601a4
com.iapp.leochen.apkinjector:attr/barrierDirection = 0x7f030065
com.iapp.leochen.apkinjector:attr/state_error = 0x7f0303fa
com.iapp.leochen.apkinjector:styleable/MotionEffect = 0x7f120064
com.iapp.leochen.apkinjector:macro/m3_comp_navigation_drawer_active_indicator_color = 0x7f0c007f
com.iapp.leochen.apkinjector:dimen/m3_comp_navigation_bar_container_elevation = 0x7f060138
com.iapp.leochen.apkinjector:attr/drawableSize = 0x7f03018c
com.iapp.leochen.apkinjector:attr/flow_verticalAlign = 0x7f0301f6
com.iapp.leochen.apkinjector:attr/thickness = 0x7f03047b
com.iapp.leochen.apkinjector:drawable/ic_file = 0x7f070094
com.iapp.leochen.apkinjector:attr/checkedIconGravity = 0x7f0300b7
com.iapp.leochen.apkinjector:attr/state_dragged = 0x7f0303f9
com.iapp.leochen.apkinjector:styleable/CardView = 0x7f12001a
com.iapp.leochen.apkinjector:color/m3_button_ripple_color = 0x7f050067
com.iapp.leochen.apkinjector:style/Widget.Material3.Button.TextButton.Dialog.Icon = 0x7f11035e
com.iapp.leochen.apkinjector:attr/state_above_anchor = 0x7f0303f6
com.iapp.leochen.apkinjector:style/Widget.AppCompat.Light.ActionBar = 0x7f110302
com.iapp.leochen.apkinjector:attr/textColorAlertDialogListItem = 0x7f030464
com.iapp.leochen.apkinjector:drawable/abc_star_black_48dp = 0x7f070068
com.iapp.leochen.apkinjector:dimen/m3_side_sheet_modal_elevation = 0x7f0601e6
com.iapp.leochen.apkinjector:attr/motionEasingEmphasizedInterpolator = 0x7f03033c
com.iapp.leochen.apkinjector:attr/content = 0x7f030138
com.iapp.leochen.apkinjector:attr/stateLabels = 0x7f0303f5
com.iapp.leochen.apkinjector:color/m3_ref_palette_primary80 = 0x7f050130
com.iapp.leochen.apkinjector:attr/startIconScaleType = 0x7f0303f2
com.iapp.leochen.apkinjector:dimen/design_fab_translation_z_hovered_focused = 0x7f060073
com.iapp.leochen.apkinjector:style/Widget.Material3.MaterialTimePicker.Display.TextInputLayout = 0x7f1103bb
com.iapp.leochen.apkinjector:attr/constraintRotate = 0x7f030131
com.iapp.leochen.apkinjector:attr/startIconDrawable = 0x7f0303f0
com.iapp.leochen.apkinjector:attr/startIconContentDescription = 0x7f0303ef
com.iapp.leochen.apkinjector:dimen/m3_comp_primary_navigation_tab_inactive_hover_state_layer_opacity = 0x7f06015f
com.iapp.leochen.apkinjector:style/Base.V7.Theme.AppCompat.Light.Dialog = 0x7f1100be
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_light_surface_bright = 0x7f0501b6
com.iapp.leochen.apkinjector:attr/textureHeight = 0x7f030478
com.iapp.leochen.apkinjector:integer/m3_sys_shape_corner_medium_corner_family = 0x7f090024
com.iapp.leochen.apkinjector:drawable/$m3_avd_hide_password__1 = 0x7f070008
com.iapp.leochen.apkinjector:attr/srcCompat = 0x7f0303eb
com.iapp.leochen.apkinjector:color/m3_appbar_overlay_color = 0x7f050060
com.iapp.leochen.apkinjector:attr/fontFamily = 0x7f0301fc
com.iapp.leochen.apkinjector:attr/windowFixedWidthMinor = 0x7f0304e6
com.iapp.leochen.apkinjector:attr/springDamping = 0x7f0303e7
com.iapp.leochen.apkinjector:dimen/m3_comp_search_bar_container_height = 0x7f060170
com.iapp.leochen.apkinjector:macro/m3_comp_outlined_text_field_outline_color = 0x7f0c00c3
com.iapp.leochen.apkinjector:dimen/m3_card_stroke_width = 0x7f0600ec
com.iapp.leochen.apkinjector:attr/splitTrack = 0x7f0303e5
com.iapp.leochen.apkinjector:dimen/m3_navigation_item_horizontal_padding = 0x7f0601c0
com.iapp.leochen.apkinjector:attr/track = 0x7f0304b3
com.iapp.leochen.apkinjector:styleable/Grid = 0x7f12003d
com.iapp.leochen.apkinjector:attr/trackHeight = 0x7f0304bb
com.iapp.leochen.apkinjector:drawable/mtrl_switch_thumb_checked = 0x7f0700d3
com.iapp.leochen.apkinjector:attr/spinnerStyle = 0x7f0303e4
com.iapp.leochen.apkinjector:color/m3_sys_color_dark_on_primary = 0x7f050163
com.iapp.leochen.apkinjector:dimen/m3_carousel_gone_size = 0x7f0600ef
com.iapp.leochen.apkinjector:attr/spinnerDropDownItemStyle = 0x7f0303e3
com.iapp.leochen.apkinjector:layout/design_navigation_item = 0x7f0b0025
com.iapp.leochen.apkinjector:attr/spinBars = 0x7f0303e2
com.iapp.leochen.apkinjector:style/Widget.Material3.MaterialDivider = 0x7f1103b2
com.iapp.leochen.apkinjector:attr/onStateTransition = 0x7f030366
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_neutral_variant12 = 0x7f0500b9
com.iapp.leochen.apkinjector:attr/panelBackground = 0x7f030373
com.iapp.leochen.apkinjector:style/Base.V21.Theme.MaterialComponents = 0x7f1100a6
com.iapp.leochen.apkinjector:color/m3_primary_text_disable_only = 0x7f05009a
com.iapp.leochen.apkinjector:attr/behavior_saveFlags = 0x7f030070
com.iapp.leochen.apkinjector:attr/snackbarTextViewStyle = 0x7f0303e0
com.iapp.leochen.apkinjector:dimen/mtrl_tooltip_minHeight = 0x7f060308
com.iapp.leochen.apkinjector:drawable/material_ic_keyboard_arrow_next_black_24dp = 0x7f0700b5
com.iapp.leochen.apkinjector:attr/snackbarButtonStyle = 0x7f0303de
com.iapp.leochen.apkinjector:color/m3_sys_color_light_inverse_surface = 0x7f0501d1
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.Toolbar.Primary = 0x7f110465
com.iapp.leochen.apkinjector:attr/sliderStyle = 0x7f0303dd
com.iapp.leochen.apkinjector:id/tag_screen_reader_focusable = 0x7f0801d1
com.iapp.leochen.apkinjector:id/accessibility_custom_action_7 = 0x7f08002e
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_primary60 = 0x7f0500d5
com.iapp.leochen.apkinjector:attr/textAppearanceListItem = 0x7f030452
com.iapp.leochen.apkinjector:attr/expandActivityOverflowButtonDrawable = 0x7f0301b7
com.iapp.leochen.apkinjector:attr/carousel_alignment = 0x7f0300a2
com.iapp.leochen.apkinjector:macro/m3_comp_date_picker_modal_range_selection_header_headline_type = 0x7f0c001a
com.iapp.leochen.apkinjector:attr/listMenuViewStyle = 0x7f0302cc
com.iapp.leochen.apkinjector:attr/singleLine = 0x7f0303da
com.iapp.leochen.apkinjector:attr/motionDurationMedium1 = 0x7f03032f
com.iapp.leochen.apkinjector:drawable/$mtrl_checkbox_button_unchecked_checked__2 = 0x7f070020
com.iapp.leochen.apkinjector:drawable/mtrl_tabs_default_indicator = 0x7f0700de
com.iapp.leochen.apkinjector:attr/simpleItemLayout = 0x7f0303d5
com.iapp.leochen.apkinjector:attr/showPaths = 0x7f0303cf
com.iapp.leochen.apkinjector:style/ThemeOverlay.Material3.SideSheetDialog = 0x7f1102b6
com.iapp.leochen.apkinjector:dimen/m3_comp_filter_chip_elevated_container_elevation = 0x7f06012c
com.iapp.leochen.apkinjector:color/abc_decor_view_status_guard_light = 0x7f050006
com.iapp.leochen.apkinjector:color/m3_chip_assist_text_color = 0x7f050070
com.iapp.leochen.apkinjector:id/all = 0x7f08004a
com.iapp.leochen.apkinjector:dimen/mtrl_slider_track_height = 0x7f0602ed
com.iapp.leochen.apkinjector:attr/showMarker = 0x7f0303cd
com.iapp.leochen.apkinjector:attr/showDividers = 0x7f0303cc
com.iapp.leochen.apkinjector:dimen/m3_comp_assist_chip_with_icon_icon_size = 0x7f0600fe
com.iapp.leochen.apkinjector:attr/tickRadiusActive = 0x7f030491
com.iapp.leochen.apkinjector:attr/titleCentered = 0x7f030498
com.iapp.leochen.apkinjector:attr/textInputFilledExposedDropdownMenuStyle = 0x7f030469
com.iapp.leochen.apkinjector:layout/design_navigation_item_subheader = 0x7f0b0028
com.iapp.leochen.apkinjector:attr/showDelay = 0x7f0303cb
com.iapp.leochen.apkinjector:string/material_clock_display_divider = 0x7f100047
com.iapp.leochen.apkinjector:attr/motionDurationMedium4 = 0x7f030332
com.iapp.leochen.apkinjector:attr/shouldRemoveExpandedCorners = 0x7f0303c8
com.iapp.leochen.apkinjector:style/Widget.Material3.FloatingActionButton.Small.Secondary = 0x7f110392
com.iapp.leochen.apkinjector:dimen/m3_comp_sheet_bottom_docked_standard_container_elevation = 0x7f06017d
com.iapp.leochen.apkinjector:attr/height = 0x7f03021d
com.iapp.leochen.apkinjector:attr/showTitle = 0x7f0303d1
com.iapp.leochen.apkinjector:attr/shapeCornerFamily = 0x7f0303c6
com.iapp.leochen.apkinjector:attr/layout_constraintTag = 0x7f0302a1
com.iapp.leochen.apkinjector:attr/shapeAppearanceOverlay = 0x7f0303c4
com.iapp.leochen.apkinjector:style/Theme.MaterialComponents.DayNight.BottomSheetDialog = 0x7f11024c
com.iapp.leochen.apkinjector:attr/shapeAppearanceMediumComponent = 0x7f0303c3
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_light_tertiary = 0x7f0501be
com.iapp.leochen.apkinjector:id/horizontal = 0x7f0800e9
com.iapp.leochen.apkinjector:attr/shapeAppearanceLargeComponent = 0x7f0303c2
com.iapp.leochen.apkinjector:string/abc_searchview_description_query = 0x7f100014
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_on_secondary_fixed = 0x7f0501c2
com.iapp.leochen.apkinjector:attr/shapeAppearanceCornerLarge = 0x7f0303bf
com.iapp.leochen.apkinjector:style/Base.Widget.AppCompat.DrawerArrowToggle = 0x7f1100d9
com.iapp.leochen.apkinjector:color/m3_dynamic_dark_hint_foreground = 0x7f05007f
com.iapp.leochen.apkinjector:attr/shapeAppearanceCornerExtraSmall = 0x7f0303be
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_tertiary100 = 0x7f0500ea
com.iapp.leochen.apkinjector:attr/shapeAppearance = 0x7f0303bc
com.iapp.leochen.apkinjector:attr/selectorSize = 0x7f0303ba
com.iapp.leochen.apkinjector:attr/colorOnSecondaryContainer = 0x7f030107
com.iapp.leochen.apkinjector:attr/selectableItemBackgroundBorderless = 0x7f0303b8
com.iapp.leochen.apkinjector:style/MaterialAlertDialog.MaterialComponents.Title.Text = 0x7f110135
com.iapp.leochen.apkinjector:color/m3_sys_color_light_background = 0x7f0501cc
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_dark_on_tertiary = 0x7f05018b
com.iapp.leochen.apkinjector:attr/selectableItemBackground = 0x7f0303b7
com.iapp.leochen.apkinjector:id/material_value_index = 0x7f080120
com.iapp.leochen.apkinjector:dimen/abc_text_size_headline_material = 0x7f060047
com.iapp.leochen.apkinjector:macro/m3_comp_primary_navigation_tab_with_icon_active_icon_color = 0x7f0c00cf
com.iapp.leochen.apkinjector:color/m3_navigation_bar_item_with_indicator_icon_tint = 0x7f05008f
com.iapp.leochen.apkinjector:style/Widget.Material3.CompoundButton.Switch = 0x7f110381
com.iapp.leochen.apkinjector:attr/constraintSetEnd = 0x7f030133
com.iapp.leochen.apkinjector:attr/searchIcon = 0x7f0303b3
com.iapp.leochen.apkinjector:id/startHorizontal = 0x7f0801be
com.iapp.leochen.apkinjector:drawable/notification_oversize_large_icon_bg = 0x7f0700e8
com.iapp.leochen.apkinjector:attr/cornerFamilyTopLeft = 0x7f03014f
com.iapp.leochen.apkinjector:attr/searchHintIcon = 0x7f0303b2
com.iapp.leochen.apkinjector:dimen/m3_bottom_nav_item_active_indicator_width = 0x7f0600be
com.iapp.leochen.apkinjector:macro/m3_comp_switch_unselected_track_outline_color = 0x7f0c0141
com.iapp.leochen.apkinjector:dimen/compat_notification_large_icon_max_height = 0x7f06005b
com.iapp.leochen.apkinjector:style/Widget.Material3.Snackbar.TextView = 0x7f1103d6
com.iapp.leochen.apkinjector:style/ShapeAppearanceOverlay.Material3.Button = 0x7f110181
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_neutral_variant20 = 0x7f0500bb
com.iapp.leochen.apkinjector:attr/roundPercent = 0x7f0303ac
com.iapp.leochen.apkinjector:dimen/compat_notification_large_icon_max_width = 0x7f06005c
com.iapp.leochen.apkinjector:dimen/m3_btn_disabled_elevation = 0x7f0600cd
com.iapp.leochen.apkinjector:attr/round = 0x7f0303ab
com.iapp.leochen.apkinjector:style/Base.Widget.MaterialComponents.TextInputLayout = 0x7f11011e
com.iapp.leochen.apkinjector:drawable/mtrl_ic_check_mark = 0x7f0700ca
com.iapp.leochen.apkinjector:style/Base.Widget.AppCompat.DrawerArrowToggle.Common = 0x7f1100da
com.iapp.leochen.apkinjector:attr/percentX = 0x7f03037f
com.iapp.leochen.apkinjector:attr/minHeight = 0x7f03031b
com.iapp.leochen.apkinjector:attr/rotationCenterId = 0x7f0303aa
com.iapp.leochen.apkinjector:dimen/mtrl_navigation_rail_icon_margin = 0x7f0602cf
com.iapp.leochen.apkinjector:attr/windowFixedHeightMinor = 0x7f0304e4
com.iapp.leochen.apkinjector:color/m3_chip_ripple_color = 0x7f050072
com.iapp.leochen.apkinjector:attr/closeIconVisible = 0x7f0300e5
com.iapp.leochen.apkinjector:attr/rippleColor = 0x7f0303a9
com.iapp.leochen.apkinjector:attr/textFillColor = 0x7f030467
com.iapp.leochen.apkinjector:color/error_color_material_light = 0x7f05005b
com.iapp.leochen.apkinjector:attr/reverseLayout = 0x7f0303a8
com.iapp.leochen.apkinjector:color/material_dynamic_neutral95 = 0x7f05022d
com.iapp.leochen.apkinjector:macro/m3_comp_dialog_headline_type = 0x7f0c0025
com.iapp.leochen.apkinjector:attr/moveWhenScrollAtTop = 0x7f030353
com.iapp.leochen.apkinjector:attr/itemHorizontalPadding = 0x7f030251
com.iapp.leochen.apkinjector:attr/removeEmbeddedFabElevation = 0x7f0303a7
com.iapp.leochen.apkinjector:attr/waveDecay = 0x7f0304da
com.iapp.leochen.apkinjector:attr/region_widthLessThan = 0x7f0303a5
com.iapp.leochen.apkinjector:attr/motion_postLayoutCollision = 0x7f030351
com.iapp.leochen.apkinjector:attr/tabIndicatorGravity = 0x7f030422
com.iapp.leochen.apkinjector:macro/m3_comp_navigation_bar_active_indicator_color = 0x7f0c0066
com.iapp.leochen.apkinjector:attr/region_heightLessThan = 0x7f0303a3
com.iapp.leochen.apkinjector:attr/reactiveGuide_applyToAllConstraintSets = 0x7f03039f
com.iapp.leochen.apkinjector:attr/chipMinHeight = 0x7f0300c7
com.iapp.leochen.apkinjector:attr/textureEffect = 0x7f030477
com.iapp.leochen.apkinjector:attr/colorOnPrimary = 0x7f030101
com.iapp.leochen.apkinjector:dimen/m3_extended_fab_start_padding = 0x7f0601b3
com.iapp.leochen.apkinjector:attr/drawerLayoutCornerSize = 0x7f030192
com.iapp.leochen.apkinjector:attr/ratingBarStyleIndicator = 0x7f03039c
com.iapp.leochen.apkinjector:dimen/m3_bottom_nav_min_height = 0x7f0600c1
com.iapp.leochen.apkinjector:anim/abc_fade_out = 0x7f010001
com.iapp.leochen.apkinjector:dimen/abc_dialog_title_divider_material = 0x7f060026
com.iapp.leochen.apkinjector:attr/layout_scrollFlags = 0x7f0302bc
com.iapp.leochen.apkinjector:attr/ratingBarStyle = 0x7f03039b
com.iapp.leochen.apkinjector:attr/queryBackground = 0x7f030396
com.iapp.leochen.apkinjector:macro/m3_comp_outlined_text_field_disabled_label_text_color = 0x7f0c00b3
com.iapp.leochen.apkinjector:dimen/m3_sys_elevation_level0 = 0x7f0601f0
com.iapp.leochen.apkinjector:style/TextAppearance.Widget.AppCompat.Toolbar.Title = 0x7f11020b
com.iapp.leochen.apkinjector:attr/indeterminateAnimationType = 0x7f030240
com.iapp.leochen.apkinjector:attr/trackStopIndicatorSize = 0x7f0304bd
com.iapp.leochen.apkinjector:dimen/mtrl_calendar_day_horizontal_padding = 0x7f060278
com.iapp.leochen.apkinjector:style/ThemeOverlay.Material3.TabLayout = 0x7f1102b8
com.iapp.leochen.apkinjector:style/TextAppearance.Material3.BodyLarge = 0x7f1101e5
com.iapp.leochen.apkinjector:color/m3_sys_color_light_error = 0x7f0501cd
com.iapp.leochen.apkinjector:attr/quantizeMotionSteps = 0x7f030395
com.iapp.leochen.apkinjector:attr/autoSizePresetSizes = 0x7f030042
com.iapp.leochen.apkinjector:macro/m3_comp_navigation_bar_active_pressed_icon_color = 0x7f0c0068
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_neutral70 = 0x7f0500ac
com.iapp.leochen.apkinjector:style/Widget.Material3.DrawerLayout = 0x7f110382
com.iapp.leochen.apkinjector:attr/flow_horizontalAlign = 0x7f0301ec
com.iapp.leochen.apkinjector:attr/grid_useRtl = 0x7f030216
com.iapp.leochen.apkinjector:style/Base.Widget.AppCompat.Button.Colored = 0x7f1100d2
com.iapp.leochen.apkinjector:attr/quantizeMotionPhase = 0x7f030394
com.iapp.leochen.apkinjector:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox.Dense = 0x7f1102c6
com.iapp.leochen.apkinjector:attr/progressBarStyle = 0x7f030392
com.iapp.leochen.apkinjector:drawable/$mtrl_checkbox_button_icon_checked_unchecked__2 = 0x7f070013
com.iapp.leochen.apkinjector:style/Theme.Design.Light.BottomSheetDialog = 0x7f110226
com.iapp.leochen.apkinjector:dimen/design_snackbar_padding_vertical = 0x7f060086
com.iapp.leochen.apkinjector:color/material_harmonized_color_error = 0x7f05026a
com.iapp.leochen.apkinjector:color/mtrl_switch_track_tint = 0x7f0502e2
com.iapp.leochen.apkinjector:attr/prefixTextColor = 0x7f03038e
com.iapp.leochen.apkinjector:dimen/m3_comp_filled_card_focus_state_layer_opacity = 0x7f060126
com.iapp.leochen.apkinjector:drawable/ic_mtrl_chip_checked_black = 0x7f07009f
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.PopupMenu.ListPopupWindow = 0x7f110442
com.iapp.leochen.apkinjector:dimen/mtrl_extended_fab_end_padding = 0x7f0602aa
com.iapp.leochen.apkinjector:dimen/abc_list_item_padding_horizontal_material = 0x7f060033
com.iapp.leochen.apkinjector:dimen/design_tab_scrollable_min_width = 0x7f06008a
com.iapp.leochen.apkinjector:attr/prefixTextAppearance = 0x7f03038d
com.iapp.leochen.apkinjector:attr/popupTheme = 0x7f03038a
com.iapp.leochen.apkinjector:attr/placeholderTextColor = 0x7f030385
com.iapp.leochen.apkinjector:styleable/StateSet = 0x7f120085
com.iapp.leochen.apkinjector:drawable/material_ic_keyboard_arrow_right_black_24dp = 0x7f0700b7
com.iapp.leochen.apkinjector:dimen/design_navigation_item_vertical_padding = 0x7f06007a
com.iapp.leochen.apkinjector:attr/placeholderText = 0x7f030383
com.iapp.leochen.apkinjector:attr/perpendicularPath_percent = 0x7f030381
com.iapp.leochen.apkinjector:attr/layout_optimizationLevel = 0x7f0302ba
com.iapp.leochen.apkinjector:drawable/mtrl_switch_thumb = 0x7f0700d2
com.iapp.leochen.apkinjector:attr/path_percent = 0x7f03037c
com.iapp.leochen.apkinjector:styleable/MaterialCheckBox = 0x7f120055
com.iapp.leochen.apkinjector:style/Widget.Material3.CheckedTextView = 0x7f110367
com.iapp.leochen.apkinjector:dimen/m3_comp_time_picker_container_elevation = 0x7f0601a0
com.iapp.leochen.apkinjector:id/NO_DEBUG = 0x7f080006
com.iapp.leochen.apkinjector:macro/m3_comp_extended_fab_tertiary_icon_color = 0x7f0c0035
com.iapp.leochen.apkinjector:attr/textAppearanceDisplayLarge = 0x7f030441
com.iapp.leochen.apkinjector:attr/tabPaddingTop = 0x7f03042c
com.iapp.leochen.apkinjector:attr/pathMotionArc = 0x7f03037b
com.iapp.leochen.apkinjector:layout/abc_tooltip = 0x7f0b001b
com.iapp.leochen.apkinjector:dimen/abc_action_bar_overflow_padding_end_material = 0x7f060007
com.iapp.leochen.apkinjector:attr/defaultState = 0x7f030174
com.iapp.leochen.apkinjector:color/material_personalized_color_on_surface = 0x7f050287
com.iapp.leochen.apkinjector:dimen/mtrl_snackbar_margin = 0x7f0602f3
com.iapp.leochen.apkinjector:dimen/abc_edit_text_inset_top_material = 0x7f06002e
com.iapp.leochen.apkinjector:attr/passwordToggleDrawable = 0x7f030377
com.iapp.leochen.apkinjector:attr/passwordToggleContentDescription = 0x7f030376
com.iapp.leochen.apkinjector:attr/panelMenuListTheme = 0x7f030374
com.iapp.leochen.apkinjector:id/graph = 0x7f0800de
com.iapp.leochen.apkinjector:attr/paddingRightSystemWindowInsets = 0x7f03036e
com.iapp.leochen.apkinjector:attr/itemVerticalPadding = 0x7f03026b
com.iapp.leochen.apkinjector:id/accessibility_custom_action_17 = 0x7f08001a
com.iapp.leochen.apkinjector:style/Widget.Material3.TextInputEditText.FilledBox = 0x7f1103da
com.iapp.leochen.apkinjector:dimen/mtrl_calendar_header_divider_thickness = 0x7f060280
com.iapp.leochen.apkinjector:attr/paddingBottomSystemWindowInsets = 0x7f03036b
com.iapp.leochen.apkinjector:attr/paddingBottomNoButtons = 0x7f03036a
com.iapp.leochen.apkinjector:macro/m3_comp_switch_disabled_unselected_icon_color = 0x7f0c011d
com.iapp.leochen.apkinjector:attr/goIcon = 0x7f03020d
com.iapp.leochen.apkinjector:dimen/m3_carousel_small_item_size_max = 0x7f0600f1
com.iapp.leochen.apkinjector:attr/onTouchUp = 0x7f030367
com.iapp.leochen.apkinjector:style/ThemeOverlay.AppCompat.ActionBar = 0x7f110277
com.iapp.leochen.apkinjector:drawable/m3_tabs_transparent_background = 0x7f0700af
com.iapp.leochen.apkinjector:color/m3_dark_highlighted_text = 0x7f050077
com.iapp.leochen.apkinjector:attr/onNegativeCross = 0x7f030363
com.iapp.leochen.apkinjector:string/mtrl_timepicker_cancel = 0x7f100099
com.iapp.leochen.apkinjector:attr/titlePositionInterpolator = 0x7f0304a1
com.iapp.leochen.apkinjector:layout/material_clockface_view = 0x7f0b003f
com.iapp.leochen.apkinjector:attr/onHide = 0x7f030362
com.iapp.leochen.apkinjector:dimen/m3_sys_motion_easing_standard_control_y2 = 0x7f060215
com.iapp.leochen.apkinjector:drawable/$mtrl_checkbox_button_icon_unchecked_indeterminate__0 = 0x7f07001b
com.iapp.leochen.apkinjector:attr/tabSelectedTextColor = 0x7f030430
com.iapp.leochen.apkinjector:attr/simpleItemSelectedColor = 0x7f0303d6
com.iapp.leochen.apkinjector:id/view_tree_saved_state_registry_owner = 0x7f08020b
com.iapp.leochen.apkinjector:attr/onCross = 0x7f030361
com.iapp.leochen.apkinjector:color/m3_ref_palette_neutral_variant70 = 0x7f050122
com.iapp.leochen.apkinjector:color/m3_navigation_item_background_color = 0x7f050092
com.iapp.leochen.apkinjector:attr/offsetAlignmentMode = 0x7f030360
com.iapp.leochen.apkinjector:attr/number = 0x7f03035e
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_dark_secondary_container = 0x7f050192
com.iapp.leochen.apkinjector:attr/navigationViewStyle = 0x7f03035a
com.iapp.leochen.apkinjector:style/Theme.AppCompat.Light = 0x7f11021b
com.iapp.leochen.apkinjector:dimen/design_bottom_navigation_item_max_width = 0x7f060065
com.iapp.leochen.apkinjector:attr/navigationIconTint = 0x7f030357
com.iapp.leochen.apkinjector:attr/shapeAppearanceCornerSmall = 0x7f0303c1
com.iapp.leochen.apkinjector:integer/m3_sys_motion_duration_short3 = 0x7f09001d
com.iapp.leochen.apkinjector:attr/region_heightMoreThan = 0x7f0303a4
com.iapp.leochen.apkinjector:dimen/m3_comp_slider_stop_indicator_size = 0x7f060188
com.iapp.leochen.apkinjector:macro/m3_comp_navigation_drawer_active_pressed_state_layer_color = 0x7f0c0083
com.iapp.leochen.apkinjector:dimen/m3_btn_disabled_translation_z = 0x7f0600ce
com.iapp.leochen.apkinjector:style/Base.TextAppearance.AppCompat.Title = 0x7f11002f
com.iapp.leochen.apkinjector:attr/navigationIcon = 0x7f030356
com.iapp.leochen.apkinjector:dimen/design_navigation_elevation = 0x7f060075
com.iapp.leochen.apkinjector:dimen/mtrl_slider_track_side_padding = 0x7f0602ee
com.iapp.leochen.apkinjector:attr/motionProgress = 0x7f03034e
com.iapp.leochen.apkinjector:attr/motionPath = 0x7f03034c
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_dark_surface_container_low = 0x7f050198
com.iapp.leochen.apkinjector:id/never = 0x7f08014e
com.iapp.leochen.apkinjector:drawable/abc_list_pressed_holo_light = 0x7f070052
com.iapp.leochen.apkinjector:attr/panelMenuListWidth = 0x7f030375
com.iapp.leochen.apkinjector:attr/behavior_draggable = 0x7f030069
com.iapp.leochen.apkinjector:dimen/mtrl_slider_label_padding = 0x7f0602e6
com.iapp.leochen.apkinjector:attr/motionEffect_translationY = 0x7f030349
com.iapp.leochen.apkinjector:drawable/abc_list_selector_disabled_holo_light = 0x7f070056
com.iapp.leochen.apkinjector:attr/materialSwitchStyle = 0x7f030307
com.iapp.leochen.apkinjector:attr/liftOnScroll = 0x7f0302bf
com.iapp.leochen.apkinjector:attr/motionEffect_strict = 0x7f030347
com.iapp.leochen.apkinjector:macro/m3_comp_time_picker_time_selector_unselected_hover_state_layer_color = 0x7f0c0169
com.iapp.leochen.apkinjector:attr/motionStagger = 0x7f03034f
com.iapp.leochen.apkinjector:attr/motionEasingStandard = 0x7f03033f
com.iapp.leochen.apkinjector:style/Animation.Material3.SideSheetDialog = 0x7f110007
com.iapp.leochen.apkinjector:attr/motionEasingLinearInterpolator = 0x7f03033e
com.iapp.leochen.apkinjector:dimen/m3_sys_motion_easing_linear_control_y1 = 0x7f06020c
com.iapp.leochen.apkinjector:attr/carousel_nextState = 0x7f0300a8
com.iapp.leochen.apkinjector:style/Base.Theme.Material3.Light.SideSheetDialog = 0x7f110065
com.iapp.leochen.apkinjector:style/Animation.MaterialComponents.BottomSheetDialog = 0x7f11000a
com.iapp.leochen.apkinjector:attr/motionEasingEmphasizedDecelerateInterpolator = 0x7f03033b
com.iapp.leochen.apkinjector:attr/actionOverflowMenuStyle = 0x7f030022
com.iapp.leochen.apkinjector:attr/textAppearanceCaption = 0x7f030440
com.iapp.leochen.apkinjector:attr/cornerSizeTopRight = 0x7f030156
com.iapp.leochen.apkinjector:attr/motionEasingAccelerated = 0x7f030337
com.iapp.leochen.apkinjector:attr/motionDurationShort4 = 0x7f030336
com.iapp.leochen.apkinjector:attr/backgroundOverlayColorAlpha = 0x7f03004d
com.iapp.leochen.apkinjector:attr/SharedValue = 0x7f030000
com.iapp.leochen.apkinjector:attr/motionDurationShort3 = 0x7f030335
com.iapp.leochen.apkinjector:attr/motionDurationExtraLong1 = 0x7f030327
com.iapp.leochen.apkinjector:attr/tickColorInactive = 0x7f03048d
com.iapp.leochen.apkinjector:id/BOTTOM_START = 0x7f080002
com.iapp.leochen.apkinjector:style/Widget.AppCompat.ActionButton.Overflow = 0x7f1102ef
com.iapp.leochen.apkinjector:color/material_dynamic_neutral100 = 0x7f050224
com.iapp.leochen.apkinjector:integer/material_motion_duration_medium_2 = 0x7f090029
com.iapp.leochen.apkinjector:attr/motionEffect_alpha = 0x7f030343
com.iapp.leochen.apkinjector:attr/transitionPathRotate = 0x7f0304c5
com.iapp.leochen.apkinjector:attr/telltales_tailScale = 0x7f030437
com.iapp.leochen.apkinjector:dimen/m3_navigation_rail_elevation = 0x7f0601ca
com.iapp.leochen.apkinjector:attr/fontProviderFetchTimeout = 0x7f030200
com.iapp.leochen.apkinjector:interpolator/btn_radio_to_on_mtrl_animation_interpolator_0 = 0x7f0a0005
com.iapp.leochen.apkinjector:attr/motionInterpolator = 0x7f03034b
com.iapp.leochen.apkinjector:attr/motionDurationExtraLong2 = 0x7f030328
com.iapp.leochen.apkinjector:style/Widget.Material3.Button.TextButton.Dialog.Flush = 0x7f11035d
com.iapp.leochen.apkinjector:attr/mock_label = 0x7f030321
com.iapp.leochen.apkinjector:attr/minWidth = 0x7f03031f
com.iapp.leochen.apkinjector:attr/circularflow_viewCenter = 0x7f0300d8
com.iapp.leochen.apkinjector:attr/textOutlineThickness = 0x7f030472
com.iapp.leochen.apkinjector:dimen/mtrl_extended_fab_icon_text_spacing = 0x7f0602ad
com.iapp.leochen.apkinjector:dimen/m3_comp_navigation_drawer_hover_state_layer_opacity = 0x7f060140
com.iapp.leochen.apkinjector:attr/dropDownBackgroundTint = 0x7f030194
com.iapp.leochen.apkinjector:style/Base.V23.Theme.AppCompat.Light = 0x7f1100b1
com.iapp.leochen.apkinjector:style/Base.Theme.MaterialComponents.Bridge = 0x7f110067
com.iapp.leochen.apkinjector:layout/mtrl_alert_dialog_actions = 0x7f0b0048
com.iapp.leochen.apkinjector:attr/textAppearanceHeadlineMedium = 0x7f03044b
com.iapp.leochen.apkinjector:attr/icon = 0x7f030230
com.iapp.leochen.apkinjector:style/Widget.Material3.CircularProgressIndicator.Legacy.Small = 0x7f110378
com.iapp.leochen.apkinjector:id/custom = 0x7f080090
com.iapp.leochen.apkinjector:attr/colorTertiary = 0x7f03012b
com.iapp.leochen.apkinjector:attr/motionDebug = 0x7f030326
com.iapp.leochen.apkinjector:color/abc_tint_edittext = 0x7f050015
com.iapp.leochen.apkinjector:attr/minSeparation = 0x7f03031d
com.iapp.leochen.apkinjector:style/TextAppearance.MaterialComponents.Caption = 0x7f1101fc
com.iapp.leochen.apkinjector:macro/m3_comp_sheet_side_docked_modal_container_color = 0x7f0c0108
com.iapp.leochen.apkinjector:attr/minHideDelay = 0x7f03031c
com.iapp.leochen.apkinjector:attr/grid_horizontalGaps = 0x7f030210
com.iapp.leochen.apkinjector:attr/touchRegionId = 0x7f0304b2
com.iapp.leochen.apkinjector:anim/m3_side_sheet_enter_from_left = 0x7f010025
com.iapp.leochen.apkinjector:style/MaterialAlertDialog.Material3.Title.Panel.CenterStacked = 0x7f11012a
com.iapp.leochen.apkinjector:attr/methodName = 0x7f03031a
com.iapp.leochen.apkinjector:style/Widget.Design.TabLayout = 0x7f11033b
com.iapp.leochen.apkinjector:attr/alertDialogStyle = 0x7f03002b
com.iapp.leochen.apkinjector:attr/errorTextColor = 0x7f0301b6
com.iapp.leochen.apkinjector:color/design_fab_shadow_end_color = 0x7f05004d
com.iapp.leochen.apkinjector:attr/menuGravity = 0x7f030319
com.iapp.leochen.apkinjector:id/material_clock_hand = 0x7f080110
com.iapp.leochen.apkinjector:attr/colorContainer = 0x7f0300f6
com.iapp.leochen.apkinjector:attr/springStopThreshold = 0x7f0303ea
com.iapp.leochen.apkinjector:string/mtrl_picker_navigate_to_current_year_description = 0x7f10007c
com.iapp.leochen.apkinjector:attr/menuAlignmentMode = 0x7f030318
com.iapp.leochen.apkinjector:style/Base.Theme.MaterialComponents.Light.Dialog.FixedSize = 0x7f110076
com.iapp.leochen.apkinjector:macro/m3_comp_text_button_label_text_color = 0x7f0c0144
com.iapp.leochen.apkinjector:id/toggle = 0x7f0801ed
com.iapp.leochen.apkinjector:attr/switchPadding = 0x7f030415
com.iapp.leochen.apkinjector:style/Widget.Material3.MaterialCalendar.HeaderTitle = 0x7f1103a9
com.iapp.leochen.apkinjector:style/Theme.MaterialComponents.Light.NoActionBar.Bridge = 0x7f110273
com.iapp.leochen.apkinjector:attr/textAppearanceBodyLarge = 0x7f03043c
com.iapp.leochen.apkinjector:attr/menu = 0x7f030317
com.iapp.leochen.apkinjector:attr/drawableStartCompat = 0x7f03018d
com.iapp.leochen.apkinjector:attr/textInputOutlinedExposedDropdownMenuStyle = 0x7f03046d
com.iapp.leochen.apkinjector:attr/maxVelocity = 0x7f030314
com.iapp.leochen.apkinjector:dimen/fastscroll_margin = 0x7f060091
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.TextInputLayout.FilledBox.ExposedDropdownMenu = 0x7f110454
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_dark_on_primary = 0x7f050185
com.iapp.leochen.apkinjector:string/mtrl_timepicker_confirm = 0x7f10009a
com.iapp.leochen.apkinjector:attr/maxLines = 0x7f030312
com.iapp.leochen.apkinjector:attr/clockFaceBackgroundColor = 0x7f0300db
com.iapp.leochen.apkinjector:animator/mtrl_btn_state_list_anim = 0x7f020015
com.iapp.leochen.apkinjector:style/Widget.Material3.Badge = 0x7f110346
com.iapp.leochen.apkinjector:attr/listPopupWindowStyle = 0x7f0302cd
com.iapp.leochen.apkinjector:attr/elevationOverlayEnabled = 0x7f03019f
com.iapp.leochen.apkinjector:string/error_a11y_label = 0x7f10002f
com.iapp.leochen.apkinjector:layout/activity_main = 0x7f0b001c
com.iapp.leochen.apkinjector:attr/maxHeight = 0x7f030310
com.iapp.leochen.apkinjector:attr/simpleItemSelectedRippleColor = 0x7f0303d7
com.iapp.leochen.apkinjector:style/Theme.MaterialComponents.DayNight.Dialog.Alert.Bridge = 0x7f110252
com.iapp.leochen.apkinjector:attr/maxCharacterCount = 0x7f03030f
com.iapp.leochen.apkinjector:id/center = 0x7f08006f
com.iapp.leochen.apkinjector:attr/contentPaddingTop = 0x7f030146
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.TimePicker.Button = 0x7f11045b
com.iapp.leochen.apkinjector:attr/carousel_previousState = 0x7f0300a9
com.iapp.leochen.apkinjector:color/material_dynamic_neutral_variant50 = 0x7f050235
com.iapp.leochen.apkinjector:dimen/mtrl_progress_circular_inset_small = 0x7f0602d7
com.iapp.leochen.apkinjector:attr/maxButtonHeight = 0x7f03030e
com.iapp.leochen.apkinjector:style/TextAppearance.AppCompat.Body1 = 0x7f110195
com.iapp.leochen.apkinjector:drawable/ic_launcher_background = 0x7f070099
com.iapp.leochen.apkinjector:style/Widget.Material3.MaterialTimePicker.Button = 0x7f1103b5
com.iapp.leochen.apkinjector:drawable/notification_bg_low_pressed = 0x7f0700e4
com.iapp.leochen.apkinjector:color/m3_sys_color_light_primary_container = 0x7f0501e0
com.iapp.leochen.apkinjector:attr/maxAcceleration = 0x7f03030c
com.iapp.leochen.apkinjector:attr/textAppearanceSearchResultSubtitle = 0x7f030457
com.iapp.leochen.apkinjector:attr/materialTimePickerTitleStyle = 0x7f03030b
com.iapp.leochen.apkinjector:attr/checkedIcon = 0x7f0300b5
com.iapp.leochen.apkinjector:dimen/mtrl_calendar_title_baseline_to_top_fullscreen = 0x7f060295
com.iapp.leochen.apkinjector:macro/m3_comp_snackbar_container_color = 0x7f0c0113
com.iapp.leochen.apkinjector:integer/mtrl_view_invisible = 0x7f090040
com.iapp.leochen.apkinjector:attr/materialSearchViewToolbarStyle = 0x7f030306
com.iapp.leochen.apkinjector:attr/materialSearchViewToolbarHeight = 0x7f030305
com.iapp.leochen.apkinjector:drawable/abc_list_focused_holo = 0x7f07004f
com.iapp.leochen.apkinjector:attr/materialSearchViewStyle = 0x7f030304
com.iapp.leochen.apkinjector:attr/materialSearchViewPrefixStyle = 0x7f030303
com.iapp.leochen.apkinjector:macro/m3_comp_top_app_bar_small_trailing_icon_color = 0x7f0c0174
com.iapp.leochen.apkinjector:attr/materialIconButtonOutlinedStyle = 0x7f030300
com.iapp.leochen.apkinjector:id/TOP_END = 0x7f08000c
com.iapp.leochen.apkinjector:attr/materialIconButtonFilledTonalStyle = 0x7f0302ff
com.iapp.leochen.apkinjector:dimen/m3_comp_navigation_rail_active_indicator_width = 0x7f060146
com.iapp.leochen.apkinjector:attr/materialDividerStyle = 0x7f0302fd
com.iapp.leochen.apkinjector:color/design_box_stroke_color = 0x7f050031
com.iapp.leochen.apkinjector:id/mtrl_calendar_day_selector_frame = 0x7f08012d
com.iapp.leochen.apkinjector:dimen/m3_btn_elevated_btn_elevation = 0x7f0600cf
com.iapp.leochen.apkinjector:attr/drawableLeftCompat = 0x7f03018a
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.ActionBar.Primary = 0x7f1103eb
com.iapp.leochen.apkinjector:attr/tabSelectedTextAppearance = 0x7f03042f
com.iapp.leochen.apkinjector:attr/materialDisplayDividerStyle = 0x7f0302fb
com.iapp.leochen.apkinjector:color/m3_dynamic_highlighted_text = 0x7f050083
com.iapp.leochen.apkinjector:style/TextAppearance.AppCompat.Widget.Button.Borderless.Colored = 0x7f1101bb
com.iapp.leochen.apkinjector:attr/materialClockStyle = 0x7f0302fa
com.iapp.leochen.apkinjector:dimen/abc_text_size_display_1_material = 0x7f060043
com.iapp.leochen.apkinjector:attr/materialAlertDialogTitleTextStyle = 0x7f0302e2
com.iapp.leochen.apkinjector:attr/collapseIcon = 0x7f0300e8
com.iapp.leochen.apkinjector:style/Base.Widget.AppCompat.RatingBar.Small = 0x7f1100f2
com.iapp.leochen.apkinjector:attr/floatingActionButtonSurfaceStyle = 0x7f0301e6
com.iapp.leochen.apkinjector:attr/motionEasingDecelerated = 0x7f030338
com.iapp.leochen.apkinjector:attr/materialCardViewOutlinedStyle = 0x7f0302f7
com.iapp.leochen.apkinjector:color/m3_sys_color_light_inverse_on_surface = 0x7f0501cf
com.iapp.leochen.apkinjector:attr/materialCardViewFilledStyle = 0x7f0302f6
com.iapp.leochen.apkinjector:dimen/m3_btn_icon_only_min_width = 0x7f0600d6
com.iapp.leochen.apkinjector:id/uniform = 0x7f0801ff
com.iapp.leochen.apkinjector:attr/materialCalendarYearNavigationButton = 0x7f0302f4
com.iapp.leochen.apkinjector:style/ThemeOverlay.AppCompat.Dark = 0x7f110278
com.iapp.leochen.apkinjector:id/month_title = 0x7f08012a
com.iapp.leochen.apkinjector:dimen/design_navigation_item_icon_padding = 0x7f060079
com.iapp.leochen.apkinjector:style/Base.V24.Theme.Material3.Light.Dialog = 0x7f1100b5
com.iapp.leochen.apkinjector:attr/thumbTintMode = 0x7f030488
com.iapp.leochen.apkinjector:attr/materialCalendarMonth = 0x7f0302f0
com.iapp.leochen.apkinjector:attr/materialCalendarHeaderToggleButton = 0x7f0302ef
com.iapp.leochen.apkinjector:color/material_timepicker_clockface = 0x7f0502b5
com.iapp.leochen.apkinjector:styleable/AppCompatTextHelper = 0x7f120010
com.iapp.leochen.apkinjector:attr/layout_constraintVertical_weight = 0x7f0302a7
com.iapp.leochen.apkinjector:attr/dialogPreferredPadding = 0x7f030179
com.iapp.leochen.apkinjector:attr/startIconTint = 0x7f0303f3
com.iapp.leochen.apkinjector:styleable/Motion = 0x7f120063
com.iapp.leochen.apkinjector:attr/materialCalendarHeaderTitle = 0x7f0302ee
com.iapp.leochen.apkinjector:anim/btn_radio_to_off_mtrl_ring_outer_animation = 0x7f010013
com.iapp.leochen.apkinjector:drawable/ic_call_answer_video = 0x7f07008d
com.iapp.leochen.apkinjector:attr/materialCalendarHeaderSelection = 0x7f0302ed
com.iapp.leochen.apkinjector:style/Platform.V25.AppCompat = 0x7f110142
com.iapp.leochen.apkinjector:animator/m3_extended_fab_hide_motion_spec = 0x7f020012
com.iapp.leochen.apkinjector:macro/m3_comp_sheet_bottom_docked_drag_handle_color = 0x7f0c0106
com.iapp.leochen.apkinjector:attr/materialCalendarHeaderDivider = 0x7f0302eb
com.iapp.leochen.apkinjector:dimen/m3_comp_slider_disabled_active_track_opacity = 0x7f060184
com.iapp.leochen.apkinjector:attr/cursorColor = 0x7f03015f
com.iapp.leochen.apkinjector:attr/hintAnimationEnabled = 0x7f030227
com.iapp.leochen.apkinjector:string/abc_menu_space_shortcut_label = 0x7f10000f
com.iapp.leochen.apkinjector:attr/motionEffect_translationX = 0x7f030348
com.iapp.leochen.apkinjector:attr/cornerFamilyBottomLeft = 0x7f03014d
com.iapp.leochen.apkinjector:attr/materialButtonToggleGroupStyle = 0x7f0302e5
com.iapp.leochen.apkinjector:style/ThemeOverlay.Material3.DynamicColors.Dark = 0x7f11029c
com.iapp.leochen.apkinjector:id/disableIntraAutoTransition = 0x7f0800a3
com.iapp.leochen.apkinjector:id/accessibility_custom_action_2 = 0x7f08001d
com.iapp.leochen.apkinjector:attr/materialButtonOutlinedStyle = 0x7f0302e3
com.iapp.leochen.apkinjector:color/material_personalized_hint_foreground_inverse = 0x7f0502a9
com.iapp.leochen.apkinjector:attr/duration = 0x7f030197
com.iapp.leochen.apkinjector:drawable/btn_radio_off_to_on_mtrl_animation = 0x7f07007f
com.iapp.leochen.apkinjector:string/path_password_eye_mask_strike_through = 0x7f10009d
com.iapp.leochen.apkinjector:attr/recyclerViewStyle = 0x7f0303a2
com.iapp.leochen.apkinjector:attr/materialAlertDialogTitlePanelStyle = 0x7f0302e1
com.iapp.leochen.apkinjector:attr/materialAlertDialogTitleIconStyle = 0x7f0302e0
com.iapp.leochen.apkinjector:color/material_personalized_color_outline_variant = 0x7f05028d
com.iapp.leochen.apkinjector:attr/sizePercent = 0x7f0303dc
com.iapp.leochen.apkinjector:dimen/m3_comp_input_chip_container_elevation = 0x7f060130
com.iapp.leochen.apkinjector:anim/abc_popup_exit = 0x7f010004
com.iapp.leochen.apkinjector:attr/materialAlertDialogBodyTextStyle = 0x7f0302dd
com.iapp.leochen.apkinjector:style/Widget.Material3.MaterialTimePicker.Clock = 0x7f1103b6
com.iapp.leochen.apkinjector:attr/marginRightSystemWindowInsets = 0x7f0302db
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_neutral_variant80 = 0x7f0500c5
com.iapp.leochen.apkinjector:attr/cornerSizeBottomLeft = 0x7f030153
com.iapp.leochen.apkinjector:attr/logoScaleType = 0x7f0302d8
com.iapp.leochen.apkinjector:attr/extendedFloatingActionButtonStyle = 0x7f0301c6
com.iapp.leochen.apkinjector:attr/extendStrategy = 0x7f0301c3
com.iapp.leochen.apkinjector:color/material_dynamic_primary95 = 0x7f050247
com.iapp.leochen.apkinjector:attr/logoDescription = 0x7f0302d7
com.iapp.leochen.apkinjector:attr/reactiveGuide_valueId = 0x7f0303a1
com.iapp.leochen.apkinjector:attr/logoAdjustViewBounds = 0x7f0302d6
com.iapp.leochen.apkinjector:attr/listPreferredItemPaddingStart = 0x7f0302d4
com.iapp.leochen.apkinjector:attr/passwordToggleTint = 0x7f030379
com.iapp.leochen.apkinjector:attr/listPreferredItemHeightSmall = 0x7f0302d0
com.iapp.leochen.apkinjector:dimen/mtrl_bottomappbar_fab_cradle_vertical_offset = 0x7f060257
com.iapp.leochen.apkinjector:id/baseline = 0x7f08005d
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_neutral95 = 0x7f0500b2
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.CircularProgressIndicator.Small = 0x7f110416
com.iapp.leochen.apkinjector:style/AlertDialog.AppCompat.Light = 0x7f110001
com.iapp.leochen.apkinjector:id/accessibility_custom_action_3 = 0x7f080028
com.iapp.leochen.apkinjector:attr/listPreferredItemHeightLarge = 0x7f0302cf
com.iapp.leochen.apkinjector:attr/thumbTint = 0x7f030487
com.iapp.leochen.apkinjector:string/mtrl_picker_navigate_to_year_description = 0x7f10007d
com.iapp.leochen.apkinjector:attr/bottomSheetDialogTheme = 0x7f03007b
com.iapp.leochen.apkinjector:attr/listPreferredItemHeight = 0x7f0302ce
com.iapp.leochen.apkinjector:style/Theme.MaterialComponents.Light = 0x7f110264
com.iapp.leochen.apkinjector:attr/reactiveGuide_applyToConstraintSet = 0x7f0303a0
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_neutral_variant90 = 0x7f0500c7
com.iapp.leochen.apkinjector:style/Theme.AppCompat.Light.DialogWhenLarge = 0x7f110220
com.iapp.leochen.apkinjector:id/textinput_counter = 0x7f0801e3
com.iapp.leochen.apkinjector:id/SHIFT = 0x7f080007
com.iapp.leochen.apkinjector:color/material_personalized_color_outline = 0x7f05028c
com.iapp.leochen.apkinjector:attr/selectionRequired = 0x7f0303b9
com.iapp.leochen.apkinjector:dimen/m3_alert_dialog_action_top_padding = 0x7f06009e
com.iapp.leochen.apkinjector:attr/listLayout = 0x7f0302cb
com.iapp.leochen.apkinjector:attr/popupWindowStyle = 0x7f03038b
com.iapp.leochen.apkinjector:attr/listDividerAlertDialog = 0x7f0302c9
com.iapp.leochen.apkinjector:dimen/m3_comp_filled_card_icon_size = 0x7f060128
com.iapp.leochen.apkinjector:color/secondary_text_disabled_material_dark = 0x7f0502fc
com.iapp.leochen.apkinjector:attr/textAppearanceSearchResultTitle = 0x7f030458
com.iapp.leochen.apkinjector:id/message = 0x7f080122
com.iapp.leochen.apkinjector:attr/listChoiceIndicatorSingleAnimated = 0x7f0302c8
com.iapp.leochen.apkinjector:string/material_clock_toggle_content_description = 0x7f100048
com.iapp.leochen.apkinjector:drawable/abc_list_longpressed_holo = 0x7f070050
com.iapp.leochen.apkinjector:color/m3_ref_palette_secondary20 = 0x7f050137
com.iapp.leochen.apkinjector:macro/m3_comp_switch_disabled_selected_track_color = 0x7f0c011b
com.iapp.leochen.apkinjector:attr/subheaderColor = 0x7f030405
com.iapp.leochen.apkinjector:attr/layout_constraintVertical_bias = 0x7f0302a5
com.iapp.leochen.apkinjector:attr/linearProgressIndicatorStyle = 0x7f0302c5
com.iapp.leochen.apkinjector:style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Primary = 0x7f11029f
com.iapp.leochen.apkinjector:color/material_personalized__highlighted_text = 0x7f050278
com.iapp.leochen.apkinjector:dimen/m3_card_dragged_z = 0x7f0600e5
com.iapp.leochen.apkinjector:attr/trackDecorationTint = 0x7f0304b9
com.iapp.leochen.apkinjector:attr/state_collapsed = 0x7f0303f7
com.iapp.leochen.apkinjector:macro/m3_comp_search_bar_supporting_text_color = 0x7f0c00ee
com.iapp.leochen.apkinjector:attr/liftOnScrollTargetViewId = 0x7f0302c1
com.iapp.leochen.apkinjector:dimen/m3_comp_filter_chip_container_height = 0x7f06012b
com.iapp.leochen.apkinjector:attr/itemHorizontalTranslationEnabled = 0x7f030252
com.iapp.leochen.apkinjector:dimen/m3_sys_motion_easing_emphasized_accelerate_control_x1 = 0x7f0601f6
com.iapp.leochen.apkinjector:string/material_timepicker_clock_mode_description = 0x7f100057
com.iapp.leochen.apkinjector:attr/layout_scrollInterpolator = 0x7f0302bd
com.iapp.leochen.apkinjector:color/mtrl_fab_icon_text_color_selector = 0x7f0502cc
com.iapp.leochen.apkinjector:attr/measureWithLargestChild = 0x7f030316
com.iapp.leochen.apkinjector:attr/layout_goneMarginBottom = 0x7f0302b1
com.iapp.leochen.apkinjector:color/material_dynamic_secondary30 = 0x7f05024d
com.iapp.leochen.apkinjector:macro/m3_comp_time_input_time_input_field_focus_outline_color = 0x7f0c0148
com.iapp.leochen.apkinjector:attr/colorOnContainerUnchecked = 0x7f0300fe
com.iapp.leochen.apkinjector:attr/layout_dodgeInsetEdges = 0x7f0302ad
com.iapp.leochen.apkinjector:dimen/m3_comp_radio_button_selected_hover_state_layer_opacity = 0x7f060168
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_light_background = 0x7f05019e
com.iapp.leochen.apkinjector:dimen/m3_comp_radio_button_disabled_unselected_icon_opacity = 0x7f060166
com.iapp.leochen.apkinjector:attr/trackTintMode = 0x7f0304c0
com.iapp.leochen.apkinjector:integer/mtrl_card_anim_delay_ms = 0x7f090033
com.iapp.leochen.apkinjector:attr/clockNumberTextColor = 0x7f0300de
com.iapp.leochen.apkinjector:attr/layout_constraintWidth_max = 0x7f0302aa
com.iapp.leochen.apkinjector:style/Base.Widget.AppCompat.Light.ActionBar = 0x7f1100de
com.iapp.leochen.apkinjector:attr/floatingActionButtonSmallSurfaceStyle = 0x7f0301e3
com.iapp.leochen.apkinjector:style/Widget.AppCompat.Light.ActionBar.TabBar.Inverse = 0x7f110306
com.iapp.leochen.apkinjector:dimen/m3_comp_outlined_text_field_focus_outline_width = 0x7f060158
com.iapp.leochen.apkinjector:style/Widget.Material3.CompoundButton.MaterialSwitch = 0x7f11037f
com.iapp.leochen.apkinjector:attr/layout_constraintLeft_creator = 0x7f030299
com.iapp.leochen.apkinjector:attr/boxStrokeErrorColor = 0x7f030086
com.iapp.leochen.apkinjector:id/accessibility_custom_action_13 = 0x7f080016
com.iapp.leochen.apkinjector:macro/m3_comp_menu_container_color = 0x7f0c005d
com.iapp.leochen.apkinjector:color/m3_timepicker_display_text_color = 0x7f05020e
com.iapp.leochen.apkinjector:color/abc_search_url_text_normal = 0x7f05000e
com.iapp.leochen.apkinjector:id/leftToRight = 0x7f0800ff
com.iapp.leochen.apkinjector:attr/layout_constraintHorizontal_chainStyle = 0x7f030297
com.iapp.leochen.apkinjector:id/withText = 0x7f080210
com.iapp.leochen.apkinjector:color/material_slider_inactive_tick_marks_color = 0x7f0502af
com.iapp.leochen.apkinjector:style/Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle = 0x7f110049
com.iapp.leochen.apkinjector:attr/pivotAnchor = 0x7f030382
com.iapp.leochen.apkinjector:color/switch_thumb_normal_material_light = 0x7f050303
com.iapp.leochen.apkinjector:attr/layout_constraintHeight = 0x7f030291
com.iapp.leochen.apkinjector:attr/actionBarSize = 0x7f030005
com.iapp.leochen.apkinjector:style/TextAppearance.M3.Sys.Typescale.LabelMedium = 0x7f1101de
com.iapp.leochen.apkinjector:attr/expandedHintEnabled = 0x7f0301b9
com.iapp.leochen.apkinjector:attr/layout_constraintGuide_percent = 0x7f030290
com.iapp.leochen.apkinjector:color/m3_assist_chip_stroke_color = 0x7f050062
com.iapp.leochen.apkinjector:macro/m3_comp_search_view_header_input_text_type = 0x7f0c00f5
com.iapp.leochen.apkinjector:color/m3_default_color_primary_text = 0x7f05007a
com.iapp.leochen.apkinjector:attr/limitBoundsTo = 0x7f0302c2
com.iapp.leochen.apkinjector:attr/colorControlNormal = 0x7f0300f9
com.iapp.leochen.apkinjector:anim/btn_radio_to_on_mtrl_dot_group_animation = 0x7f010015
com.iapp.leochen.apkinjector:attr/fastScrollVerticalTrackDrawable = 0x7f0301d7
com.iapp.leochen.apkinjector:attr/layout_constraintGuide_begin = 0x7f03028e
com.iapp.leochen.apkinjector:style/ThemeOverlay.Material3.PersonalizedColors = 0x7f1102b4
com.iapp.leochen.apkinjector:id/startToEnd = 0x7f0801bf
com.iapp.leochen.apkinjector:attr/layout_constraintEnd_toStartOf = 0x7f03028d
com.iapp.leochen.apkinjector:id/list_item = 0x7f080105
com.iapp.leochen.apkinjector:color/design_default_color_on_primary = 0x7f050043
com.iapp.leochen.apkinjector:style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Day = 0x7f110190
com.iapp.leochen.apkinjector:attr/layout_collapseParallaxMultiplier = 0x7f03027e
com.iapp.leochen.apkinjector:attr/tooltipStyle = 0x7f0304ad
com.iapp.leochen.apkinjector:color/m3_navigation_bar_ripple_color_selector = 0x7f050091
com.iapp.leochen.apkinjector:dimen/m3_comp_time_picker_period_selector_outline_width = 0x7f0601a3
com.iapp.leochen.apkinjector:dimen/mtrl_alert_dialog_background_inset_top = 0x7f060249
com.iapp.leochen.apkinjector:style/Theme.AppCompat.DialogWhenLarge = 0x7f110219
com.iapp.leochen.apkinjector:attr/backgroundInsetBottom = 0x7f030049
com.iapp.leochen.apkinjector:color/m3_text_button_foreground_color_selector = 0x7f050201
com.iapp.leochen.apkinjector:styleable/include = 0x7f12009a
com.iapp.leochen.apkinjector:attr/layout_constraintBaseline_toTopOf = 0x7f030284
com.iapp.leochen.apkinjector:color/m3_sys_color_light_secondary = 0x7f0501e1
com.iapp.leochen.apkinjector:dimen/m3_comp_radio_button_unselected_hover_state_layer_opacity = 0x7f06016b
com.iapp.leochen.apkinjector:dimen/m3_searchview_divider_size = 0x7f0601e2
com.iapp.leochen.apkinjector:attr/mock_labelBackgroundColor = 0x7f030322
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.TextInputLayout.FilledBox.Dense = 0x7f110452
com.iapp.leochen.apkinjector:macro/m3_comp_switch_unselected_hover_track_outline_color = 0x7f0c0139
com.iapp.leochen.apkinjector:dimen/disabled_alpha_material_dark = 0x7f06008e
com.iapp.leochen.apkinjector:attr/layout_constraintBaseline_toBaselineOf = 0x7f030282
com.iapp.leochen.apkinjector:interpolator/m3_sys_motion_easing_standard_decelerate = 0x7f0a000d
com.iapp.leochen.apkinjector:attr/behavior_hideable = 0x7f03006d
com.iapp.leochen.apkinjector:attr/layout_constraintRight_creator = 0x7f03029c
com.iapp.leochen.apkinjector:color/material_on_primary_emphasis_medium = 0x7f050273
com.iapp.leochen.apkinjector:attr/actionModeCloseContentDescription = 0x7f030014
com.iapp.leochen.apkinjector:attr/windowActionBar = 0x7f0304e0
com.iapp.leochen.apkinjector:integer/design_tab_indicator_anim_duration_ms = 0x7f090007
com.iapp.leochen.apkinjector:attr/indicatorTrackGapSize = 0x7f030247
com.iapp.leochen.apkinjector:macro/m3_comp_fab_primary_icon_color = 0x7f0c0038
com.iapp.leochen.apkinjector:attr/layout_constrainedHeight = 0x7f03027f
com.iapp.leochen.apkinjector:color/mtrl_card_view_foreground = 0x7f0502c1
com.iapp.leochen.apkinjector:drawable/abc_ab_share_pack_mtrl_alpha = 0x7f070029
com.iapp.leochen.apkinjector:dimen/design_snackbar_extra_spacing_horizontal = 0x7f060082
com.iapp.leochen.apkinjector:attr/emojiCompatEnabled = 0x7f0301a0
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_light_surface = 0x7f0501b5
com.iapp.leochen.apkinjector:integer/mtrl_switch_thumb_motion_duration = 0x7f090036
com.iapp.leochen.apkinjector:attr/titleMargin = 0x7f03049b
com.iapp.leochen.apkinjector:dimen/def_drawer_elevation = 0x7f06005d
com.iapp.leochen.apkinjector:attr/layout_collapseMode = 0x7f03027d
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu = 0x7f110458
com.iapp.leochen.apkinjector:color/mtrl_textinput_default_box_stroke_color = 0x7f0502e9
com.iapp.leochen.apkinjector:attr/layout_constraintTop_creator = 0x7f0302a2
com.iapp.leochen.apkinjector:attr/layout_behavior = 0x7f03027c
com.iapp.leochen.apkinjector:attr/commitIcon = 0x7f03012f
com.iapp.leochen.apkinjector:macro/m3_comp_sheet_bottom_docked_container_color = 0x7f0c0104
com.iapp.leochen.apkinjector:drawable/test_level_drawable = 0x7f0700ed
com.iapp.leochen.apkinjector:attr/layout_anchorGravity = 0x7f03027b
com.iapp.leochen.apkinjector:attr/haloColor = 0x7f03021a
com.iapp.leochen.apkinjector:attr/fabAnchorMode = 0x7f0301cc
com.iapp.leochen.apkinjector:attr/layoutDescription = 0x7f030277
com.iapp.leochen.apkinjector:attr/lastItemDecorated = 0x7f030275
com.iapp.leochen.apkinjector:id/search_voice_btn = 0x7f08019e
com.iapp.leochen.apkinjector:color/m3_slider_inactive_track_color_legacy = 0x7f050155
com.iapp.leochen.apkinjector:attr/labelVisibilityMode = 0x7f030272
com.iapp.leochen.apkinjector:color/abc_tint_default = 0x7f050014
com.iapp.leochen.apkinjector:drawable/$avd_show_password__0 = 0x7f070003
com.iapp.leochen.apkinjector:id/exitUntilCollapsed = 0x7f0800be
com.iapp.leochen.apkinjector:animator/mtrl_fab_transformation_sheet_expand_spec = 0x7f020021
com.iapp.leochen.apkinjector:attr/tabPadding = 0x7f030428
com.iapp.leochen.apkinjector:macro/m3_comp_time_picker_period_selector_unselected_focus_state_layer_color = 0x7f0c015a
com.iapp.leochen.apkinjector:attr/arrowHeadLength = 0x7f030039
com.iapp.leochen.apkinjector:attr/itemTextAppearanceInactive = 0x7f030269
com.iapp.leochen.apkinjector:attr/defaultMarginsEnabled = 0x7f030171
com.iapp.leochen.apkinjector:id/skipCollapsed = 0x7f0801a9
com.iapp.leochen.apkinjector:attr/itemTextAppearance = 0x7f030266
com.iapp.leochen.apkinjector:string/abc_searchview_description_voice = 0x7f100017
com.iapp.leochen.apkinjector:attr/textAppearanceLabelLarge = 0x7f03044d
com.iapp.leochen.apkinjector:attr/itemSpacing = 0x7f030263
com.iapp.leochen.apkinjector:color/m3_timepicker_clock_text_color = 0x7f05020b
com.iapp.leochen.apkinjector:dimen/m3_comp_badge_large_size = 0x7f0600ff
com.iapp.leochen.apkinjector:attr/forceDefaultNavigationOnClickListener = 0x7f030208
com.iapp.leochen.apkinjector:attr/itemShapeInsetTop = 0x7f030262
com.iapp.leochen.apkinjector:dimen/m3_card_elevated_dragged_z = 0x7f0600e7
com.iapp.leochen.apkinjector:style/Theme.AppCompat.Light.Dialog.MinWidth = 0x7f11021f
com.iapp.leochen.apkinjector:attr/itemShapeInsetEnd = 0x7f030260
com.iapp.leochen.apkinjector:attr/hideNavigationIcon = 0x7f030224
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.BottomAppBar.PrimarySurface = 0x7f1103fa
com.iapp.leochen.apkinjector:color/design_dark_default_color_on_error = 0x7f050035
com.iapp.leochen.apkinjector:animator/fragment_open_exit = 0x7f020008
com.iapp.leochen.apkinjector:attr/itemShapeInsetBottom = 0x7f03025f
com.iapp.leochen.apkinjector:attr/constraint_referenced_ids = 0x7f030135
com.iapp.leochen.apkinjector:attr/editTextColor = 0x7f03019a
com.iapp.leochen.apkinjector:style/Widget.AppCompat.Button = 0x7f1102f3
com.iapp.leochen.apkinjector:attr/itemShapeAppearance = 0x7f03025c
com.iapp.leochen.apkinjector:attr/itemRippleColor = 0x7f03025b
com.iapp.leochen.apkinjector:color/m3_simple_item_ripple_color = 0x7f050150
com.iapp.leochen.apkinjector:string/bottomsheet_action_expand = 0x7f100020
com.iapp.leochen.apkinjector:string/bottomsheet_action_collapse = 0x7f10001f
com.iapp.leochen.apkinjector:id/action_bar_container = 0x7f080036
com.iapp.leochen.apkinjector:attr/itemPaddingTop = 0x7f03025a
com.iapp.leochen.apkinjector:attr/firstBaselineToTopHeight = 0x7f0301d8
com.iapp.leochen.apkinjector:id/currentPathDisplay = 0x7f08008d
com.iapp.leochen.apkinjector:drawable/$mtrl_switch_thumb_unchecked_checked__0 = 0x7f070026
com.iapp.leochen.apkinjector:attr/itemIconTint = 0x7f030255
com.iapp.leochen.apkinjector:style/Widget.Material3.ExtendedFloatingActionButton.Tertiary = 0x7f11038a
com.iapp.leochen.apkinjector:color/primary_dark_material_dark = 0x7f0502f0
com.iapp.leochen.apkinjector:styleable/ShapeAppearance = 0x7f12007b
com.iapp.leochen.apkinjector:dimen/mtrl_alert_dialog_background_inset_bottom = 0x7f060246
com.iapp.leochen.apkinjector:style/Base.V14.Theme.MaterialComponents.Light.Dialog.Bridge = 0x7f11009a
com.iapp.leochen.apkinjector:attr/itemIconSize = 0x7f030254
com.iapp.leochen.apkinjector:attr/contentPadding = 0x7f030140
com.iapp.leochen.apkinjector:drawable/abc_ic_arrow_drop_right_black_24dp = 0x7f07003f
com.iapp.leochen.apkinjector:attr/imageZoom = 0x7f03023f
com.iapp.leochen.apkinjector:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date = 0x7f1102d6
com.iapp.leochen.apkinjector:macro/m3_comp_dialog_headline_color = 0x7f0c0024
com.iapp.leochen.apkinjector:id/below = 0x7f080060
com.iapp.leochen.apkinjector:attr/itemFillColor = 0x7f030250
com.iapp.leochen.apkinjector:style/Base.V14.Theme.MaterialComponents.Light.Bridge = 0x7f110097
com.iapp.leochen.apkinjector:string/mtrl_checkbox_button_path_name = 0x7f100064
com.iapp.leochen.apkinjector:macro/m3_comp_switch_unselected_track_color = 0x7f0c0140
com.iapp.leochen.apkinjector:attr/insetForeground = 0x7f030249
com.iapp.leochen.apkinjector:id/month_navigation_bar = 0x7f080126
com.iapp.leochen.apkinjector:anim/abc_tooltip_exit = 0x7f01000b
com.iapp.leochen.apkinjector:attr/itemBackground = 0x7f03024f
com.iapp.leochen.apkinjector:style/Base.Widget.Material3.FloatingActionButton.Small = 0x7f11010b
com.iapp.leochen.apkinjector:layout/mtrl_search_bar = 0x7f0b0065
com.iapp.leochen.apkinjector:attr/startIconMinSize = 0x7f0303f1
com.iapp.leochen.apkinjector:style/Widget.AppCompat.ActivityChooserView = 0x7f1102f1
com.iapp.leochen.apkinjector:color/m3_ref_palette_error30 = 0x7f0500f9
com.iapp.leochen.apkinjector:id/path = 0x7f080174
com.iapp.leochen.apkinjector:attr/pressedTranslationZ = 0x7f030390
com.iapp.leochen.apkinjector:attr/errorEnabled = 0x7f0301b0
com.iapp.leochen.apkinjector:style/Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse = 0x7f1100e2
com.iapp.leochen.apkinjector:color/material_dynamic_primary80 = 0x7f050245
com.iapp.leochen.apkinjector:layout/mtrl_layout_snackbar = 0x7f0b0058
com.iapp.leochen.apkinjector:attr/alertDialogButtonGroupStyle = 0x7f030029
com.iapp.leochen.apkinjector:attr/hideOnScroll = 0x7f030226
com.iapp.leochen.apkinjector:style/Widget.AppCompat.Light.ActionBar.TabText.Inverse = 0x7f110308
com.iapp.leochen.apkinjector:attr/barrierAllowsGoneWidgets = 0x7f030064
com.iapp.leochen.apkinjector:attr/isLightTheme = 0x7f03024a
com.iapp.leochen.apkinjector:layout/mtrl_calendar_months = 0x7f0b0055
com.iapp.leochen.apkinjector:dimen/m3_extended_fab_bottom_padding = 0x7f0601af
com.iapp.leochen.apkinjector:attr/itemStrokeWidth = 0x7f030265
com.iapp.leochen.apkinjector:attr/initialActivityCount = 0x7f030248
com.iapp.leochen.apkinjector:style/TextAppearance.AppCompat.Widget.Button = 0x7f1101ba
com.iapp.leochen.apkinjector:style/Base.Animation.AppCompat.Tooltip = 0x7f11000f
com.iapp.leochen.apkinjector:attr/windowMinWidthMinor = 0x7f0304e8
com.iapp.leochen.apkinjector:style/Widget.Material3.MaterialCalendar.MonthNavigationButton = 0x7f1103ac
com.iapp.leochen.apkinjector:attr/expandedTitleTextAppearance = 0x7f0301c0
com.iapp.leochen.apkinjector:dimen/material_textinput_min_width = 0x7f060243
com.iapp.leochen.apkinjector:attr/daySelectedStyle = 0x7f03016d
com.iapp.leochen.apkinjector:attr/buttonStyle = 0x7f030096
com.iapp.leochen.apkinjector:attr/applyMotionScene = 0x7f030037
com.iapp.leochen.apkinjector:attr/indicatorSize = 0x7f030246
com.iapp.leochen.apkinjector:dimen/design_fab_size_mini = 0x7f060071
com.iapp.leochen.apkinjector:style/ThemeOverlay.MaterialComponents.BottomSheetDialog = 0x7f1102cb
com.iapp.leochen.apkinjector:attr/thumbTextPadding = 0x7f030486
com.iapp.leochen.apkinjector:drawable/abc_list_pressed_holo_dark = 0x7f070051
com.iapp.leochen.apkinjector:id/normal = 0x7f080154
com.iapp.leochen.apkinjector:color/m3_ref_palette_secondary40 = 0x7f050139
com.iapp.leochen.apkinjector:style/ThemeOverlay.Material3.FloatingActionButton.Secondary = 0x7f1102a4
com.iapp.leochen.apkinjector:style/Theme.MaterialComponents.DialogWhenLarge = 0x7f110263
com.iapp.leochen.apkinjector:style/Base.Widget.MaterialComponents.PopupMenu.ContextMenu = 0x7f110118
com.iapp.leochen.apkinjector:attr/colorOnPrimarySurface = 0x7f030105
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_neutral_variant0 = 0x7f0500b6
com.iapp.leochen.apkinjector:attr/iconStartPadding = 0x7f030235
com.iapp.leochen.apkinjector:attr/titleMarginStart = 0x7f03049e
com.iapp.leochen.apkinjector:attr/layout_wrapBehaviorInParent = 0x7f0302be
com.iapp.leochen.apkinjector:attr/minTouchTargetSize = 0x7f03031e
com.iapp.leochen.apkinjector:color/m3_ref_palette_neutral100 = 0x7f050104
com.iapp.leochen.apkinjector:attr/layout_constraintHorizontal_bias = 0x7f030296
com.iapp.leochen.apkinjector:attr/keyPositionType = 0x7f03026c
com.iapp.leochen.apkinjector:style/Base.Widget.AppCompat.ProgressBar = 0x7f1100ee
com.iapp.leochen.apkinjector:attr/collapsingToolbarLayoutMediumStyle = 0x7f0300f0
com.iapp.leochen.apkinjector:dimen/mtrl_calendar_header_toggle_margin_top = 0x7f060286
com.iapp.leochen.apkinjector:attr/iconPadding = 0x7f030233
com.iapp.leochen.apkinjector:style/Base.ThemeOverlay.AppCompat.Dark.ActionBar = 0x7f11007c
com.iapp.leochen.apkinjector:dimen/m3_small_fab_size = 0x7f0601ed
com.iapp.leochen.apkinjector:attr/textPanX = 0x7f030473
com.iapp.leochen.apkinjector:attr/tabIndicatorFullWidth = 0x7f030421
com.iapp.leochen.apkinjector:attr/hoveredFocusedTranslationZ = 0x7f03022f
com.iapp.leochen.apkinjector:dimen/m3_sys_state_dragged_state_layer_opacity = 0x7f06021a
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_primary20 = 0x7f0500d1
com.iapp.leochen.apkinjector:attr/listPreferredItemPaddingEnd = 0x7f0302d1
com.iapp.leochen.apkinjector:dimen/m3_comp_input_chip_with_leading_icon_leading_icon_size = 0x7f060134
com.iapp.leochen.apkinjector:anim/slide_out_left_smooth = 0x7f010031
com.iapp.leochen.apkinjector:drawable/abc_scrubber_control_off_mtrl_alpha = 0x7f07005e
com.iapp.leochen.apkinjector:attr/circularflow_defaultRadius = 0x7f0300d6
com.iapp.leochen.apkinjector:attr/horizontalOffset = 0x7f03022d
com.iapp.leochen.apkinjector:attr/chipCornerRadius = 0x7f0300bf
com.iapp.leochen.apkinjector:attr/hintTextAppearance = 0x7f030229
com.iapp.leochen.apkinjector:attr/hideOnContentScroll = 0x7f030225
com.iapp.leochen.apkinjector:attr/carousel_firstView = 0x7f0300a5
com.iapp.leochen.apkinjector:attr/passwordToggleEnabled = 0x7f030378
com.iapp.leochen.apkinjector:attr/helperTextTextAppearance = 0x7f030220
com.iapp.leochen.apkinjector:attr/helperText = 0x7f03021e
com.iapp.leochen.apkinjector:dimen/m3_extended_fab_min_height = 0x7f0601b2
com.iapp.leochen.apkinjector:attr/flow_lastVerticalStyle = 0x7f0301f3
com.iapp.leochen.apkinjector:color/mtrl_textinput_disabled_color = 0x7f0502ea
com.iapp.leochen.apkinjector:attr/stackFromEnd = 0x7f0303ec
com.iapp.leochen.apkinjector:dimen/mtrl_navigation_bar_item_default_margin = 0x7f0602c4
com.iapp.leochen.apkinjector:attr/paddingEnd = 0x7f03036c
com.iapp.leochen.apkinjector:attr/labelStyle = 0x7f030271
com.iapp.leochen.apkinjector:string/mtrl_picker_range_header_unselected = 0x7f100083
com.iapp.leochen.apkinjector:attr/thumbStrokeColor = 0x7f030484
com.iapp.leochen.apkinjector:color/m3_ref_palette_error99 = 0x7f050101
com.iapp.leochen.apkinjector:attr/cornerFamilyBottomRight = 0x7f03014e
com.iapp.leochen.apkinjector:attr/flow_verticalGap = 0x7f0301f8
com.iapp.leochen.apkinjector:attr/colorPrimaryFixed = 0x7f030116
com.iapp.leochen.apkinjector:styleable/MaterialCalendar = 0x7f120052
com.iapp.leochen.apkinjector:attr/guidelineUseRtl = 0x7f030219
com.iapp.leochen.apkinjector:styleable/KeyTrigger = 0x7f120047
com.iapp.leochen.apkinjector:attr/state_with_icon = 0x7f0303fe
com.iapp.leochen.apkinjector:attr/grid_spans = 0x7f030215
com.iapp.leochen.apkinjector:id/selected = 0x7f0801a0
com.iapp.leochen.apkinjector:attr/chipBackgroundColor = 0x7f0300be
com.iapp.leochen.apkinjector:macro/m3_comp_filled_tonal_button_container_color = 0x7f0c0052
com.iapp.leochen.apkinjector:attr/boxStrokeWidthFocused = 0x7f030088
com.iapp.leochen.apkinjector:string/material_slider_value = 0x7f100055
com.iapp.leochen.apkinjector:attr/altSrc = 0x7f030030
com.iapp.leochen.apkinjector:drawable/m3_tabs_background = 0x7f0700ac
com.iapp.leochen.apkinjector:macro/m3_comp_navigation_bar_active_hover_state_layer_color = 0x7f0c0064
com.iapp.leochen.apkinjector:attr/grid_skips = 0x7f030214
com.iapp.leochen.apkinjector:macro/m3_comp_navigation_bar_inactive_pressed_state_layer_color = 0x7f0c0076
com.iapp.leochen.apkinjector:attr/cornerSizeBottomRight = 0x7f030154
com.iapp.leochen.apkinjector:attr/chipStyle = 0x7f0300d0
com.iapp.leochen.apkinjector:id/fullscreen_header = 0x7f0800d9
com.iapp.leochen.apkinjector:attr/materialCalendarHeaderLayout = 0x7f0302ec
com.iapp.leochen.apkinjector:attr/badgeTextColor = 0x7f03005a
com.iapp.leochen.apkinjector:style/Widget.AppCompat.ButtonBar.AlertDialog = 0x7f1102fa
com.iapp.leochen.apkinjector:attr/buttonBarNegativeButtonStyle = 0x7f03008b
com.iapp.leochen.apkinjector:style/Widget.Material3.SearchBar.Outlined = 0x7f1103c8
com.iapp.leochen.apkinjector:style/Base.Theme.Material3.Dark.Dialog = 0x7f11005c
com.iapp.leochen.apkinjector:dimen/m3_btn_icon_only_icon_padding = 0x7f0600d5
com.iapp.leochen.apkinjector:attr/motionDurationLong4 = 0x7f03032e
com.iapp.leochen.apkinjector:attr/grid_rowWeights = 0x7f030212
com.iapp.leochen.apkinjector:attr/collapseContentDescription = 0x7f0300e7
com.iapp.leochen.apkinjector:style/Theme.Design.Light = 0x7f110225
com.iapp.leochen.apkinjector:attr/backgroundInsetTop = 0x7f03004c
com.iapp.leochen.apkinjector:macro/m3_comp_time_picker_time_selector_unselected_label_text_color = 0x7f0c016a
com.iapp.leochen.apkinjector:macro/m3_comp_search_view_header_leading_icon_color = 0x7f0c00f6
com.iapp.leochen.apkinjector:attr/grid_columns = 0x7f03020f
com.iapp.leochen.apkinjector:style/Base.Widget.AppCompat.Light.ActionBar.TabView = 0x7f1100e3
com.iapp.leochen.apkinjector:dimen/m3_appbar_size_large = 0x7f0600aa
com.iapp.leochen.apkinjector:color/mtrl_btn_text_btn_bg_color_selector = 0x7f0502ba
com.iapp.leochen.apkinjector:style/Widget.Material3.FloatingActionButton.Large.Surface = 0x7f11038d
com.iapp.leochen.apkinjector:attr/grid_columnWeights = 0x7f03020e
com.iapp.leochen.apkinjector:color/m3_sys_color_light_surface_container_highest = 0x7f0501e7
com.iapp.leochen.apkinjector:attr/actionModeCloseButtonStyle = 0x7f030013
com.iapp.leochen.apkinjector:macro/m3_comp_outlined_text_field_supporting_text_type = 0x7f0c00c5
com.iapp.leochen.apkinjector:dimen/m3_btn_dialog_btn_min_width = 0x7f0600cb
com.iapp.leochen.apkinjector:color/mtrl_chip_background_color = 0x7f0502c3
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_light_primary_container = 0x7f0501b2
com.iapp.leochen.apkinjector:attr/layout_keyline = 0x7f0302b8
com.iapp.leochen.apkinjector:attr/fontWeight = 0x7f030206
com.iapp.leochen.apkinjector:style/ShapeAppearance.Material3.SmallComponent = 0x7f110179
com.iapp.leochen.apkinjector:attr/layout_goneMarginLeft = 0x7f0302b3
com.iapp.leochen.apkinjector:attr/fontStyle = 0x7f030204
com.iapp.leochen.apkinjector:string/androidx_startup = 0x7f10001b
com.iapp.leochen.apkinjector:attr/mock_labelColor = 0x7f030323
com.iapp.leochen.apkinjector:attr/indicatorInset = 0x7f030245
com.iapp.leochen.apkinjector:id/transition_current_scene = 0x7f0801f6
com.iapp.leochen.apkinjector:id/search_src_text = 0x7f08019d
com.iapp.leochen.apkinjector:color/material_on_surface_disabled = 0x7f050274
com.iapp.leochen.apkinjector:color/m3_popupmenu_overlay_color = 0x7f050099
com.iapp.leochen.apkinjector:attr/collapsedSize = 0x7f0300e9
com.iapp.leochen.apkinjector:attr/layout_constraintStart_toEndOf = 0x7f03029f
com.iapp.leochen.apkinjector:anim/btn_checkbox_to_unchecked_check_path_merged_animation = 0x7f010010
com.iapp.leochen.apkinjector:color/m3_sys_color_light_on_primary_container = 0x7f0501d6
com.iapp.leochen.apkinjector:string/abc_menu_delete_shortcut_label = 0x7f10000a
com.iapp.leochen.apkinjector:attr/polarRelativeTo = 0x7f030387
com.iapp.leochen.apkinjector:attr/actionViewClass = 0x7f030025
com.iapp.leochen.apkinjector:attr/fontProviderFetchStrategy = 0x7f0301ff
com.iapp.leochen.apkinjector:drawable/mtrl_dialog_background = 0x7f0700c5
com.iapp.leochen.apkinjector:attr/buttonBarButtonStyle = 0x7f03008a
com.iapp.leochen.apkinjector:attr/imageButtonStyle = 0x7f03023b
com.iapp.leochen.apkinjector:attr/layout_insetEdge = 0x7f0302b7
com.iapp.leochen.apkinjector:macro/m3_comp_switch_selected_handle_color = 0x7f0c0124
com.iapp.leochen.apkinjector:color/m3_sys_color_on_tertiary_fixed = 0x7f0501f2
com.iapp.leochen.apkinjector:styleable/MotionLabel = 0x7f120066
com.iapp.leochen.apkinjector:attr/marginLeftSystemWindowInsets = 0x7f0302da
com.iapp.leochen.apkinjector:style/Widget.AppCompat.Light.AutoCompleteTextView = 0x7f110310
com.iapp.leochen.apkinjector:id/scrollable = 0x7f080194
com.iapp.leochen.apkinjector:attr/tooltipText = 0x7f0304ae
com.iapp.leochen.apkinjector:attr/flow_lastVerticalBias = 0x7f0301f2
com.iapp.leochen.apkinjector:attr/buttonTint = 0x7f030098
com.iapp.leochen.apkinjector:attr/flow_lastHorizontalStyle = 0x7f0301f1
com.iapp.leochen.apkinjector:dimen/m3_sys_state_hover_state_layer_opacity = 0x7f06021c
com.iapp.leochen.apkinjector:attr/contentPaddingStart = 0x7f030145
com.iapp.leochen.apkinjector:color/m3_ref_palette_neutral22 = 0x7f050108
com.iapp.leochen.apkinjector:macro/m3_comp_primary_navigation_tab_active_focus_state_layer_color = 0x7f0c00c7
com.iapp.leochen.apkinjector:attr/badgeWithTextWidth = 0x7f030062
com.iapp.leochen.apkinjector:attr/motionDurationExtraLong4 = 0x7f03032a
com.iapp.leochen.apkinjector:attr/fastScrollVerticalThumbDrawable = 0x7f0301d6
com.iapp.leochen.apkinjector:dimen/mtrl_btn_padding_top = 0x7f060268
com.iapp.leochen.apkinjector:attr/behavior_significantVelocityThreshold = 0x7f030071
com.iapp.leochen.apkinjector:attr/percentHeight = 0x7f03037d
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.CollapsingToolbar = 0x7f110417
com.iapp.leochen.apkinjector:attr/flow_horizontalStyle = 0x7f0301ef
com.iapp.leochen.apkinjector:style/Widget.Support.CoordinatorLayout = 0x7f110469
com.iapp.leochen.apkinjector:attr/wavePeriod = 0x7f0304dc
com.iapp.leochen.apkinjector:attr/drawerLayoutStyle = 0x7f030193
com.iapp.leochen.apkinjector:attr/flow_horizontalGap = 0x7f0301ee
com.iapp.leochen.apkinjector:color/switch_thumb_disabled_material_light = 0x7f0502ff
com.iapp.leochen.apkinjector:style/Base.V7.Widget.AppCompat.AutoCompleteTextView = 0x7f1100c0
com.iapp.leochen.apkinjector:attr/flow_firstVerticalStyle = 0x7f0301eb
com.iapp.leochen.apkinjector:color/m3_slider_halo_color_legacy = 0x7f050153
com.iapp.leochen.apkinjector:attr/colorOnTertiaryFixedVariant = 0x7f030110
com.iapp.leochen.apkinjector:macro/m3_comp_switch_unselected_pressed_icon_color = 0x7f0c013c
com.iapp.leochen.apkinjector:macro/m3_comp_navigation_bar_inactive_pressed_label_text_color = 0x7f0c0075
com.iapp.leochen.apkinjector:attr/flow_firstVerticalBias = 0x7f0301ea
com.iapp.leochen.apkinjector:attr/circularflow_radiusInDP = 0x7f0300d7
com.iapp.leochen.apkinjector:dimen/abc_cascading_menus_min_smallest_width = 0x7f060016
com.iapp.leochen.apkinjector:style/Widget.Material3.NavigationRailView.Badge = 0x7f1103bf
com.iapp.leochen.apkinjector:attr/floatingActionButtonStyle = 0x7f0301e5
com.iapp.leochen.apkinjector:macro/m3_comp_outlined_text_field_hover_input_text_color = 0x7f0c00bd
com.iapp.leochen.apkinjector:attr/actionModeSelectAllDrawable = 0x7f03001b
com.iapp.leochen.apkinjector:attr/percentY = 0x7f030380
com.iapp.leochen.apkinjector:attr/floatingActionButtonPrimaryStyle = 0x7f0301de
com.iapp.leochen.apkinjector:style/Base.TextAppearance.AppCompat.Display2 = 0x7f11001c
com.iapp.leochen.apkinjector:attr/floatingActionButtonLargeTertiaryStyle = 0x7f0301dd
com.iapp.leochen.apkinjector:layout/mtrl_calendar_month = 0x7f0b0052
com.iapp.leochen.apkinjector:attr/ttcIndex = 0x7f0304ca
com.iapp.leochen.apkinjector:id/search_bar = 0x7f080196
com.iapp.leochen.apkinjector:attr/floatingActionButtonLargeSurfaceStyle = 0x7f0301dc
com.iapp.leochen.apkinjector:drawable/notification_template_icon_bg = 0x7f0700e9
com.iapp.leochen.apkinjector:id/upButton = 0x7f080202
com.iapp.leochen.apkinjector:attr/layout_constraintCircle = 0x7f030288
com.iapp.leochen.apkinjector:attr/fabCustomSize = 0x7f0301d1
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_tertiary_fixed = 0x7f0501ca
com.iapp.leochen.apkinjector:color/androidx_core_ripple_material_light = 0x7f05001b
com.iapp.leochen.apkinjector:attr/chipGroupStyle = 0x7f0300c1
com.iapp.leochen.apkinjector:attr/fabCradleVerticalOffset = 0x7f0301d0
com.iapp.leochen.apkinjector:style/Widget.Material3.BottomNavigationView.ActiveIndicator = 0x7f11034d
com.iapp.leochen.apkinjector:color/mtrl_filled_background_color = 0x7f0502ce
com.iapp.leochen.apkinjector:color/material_dynamic_secondary90 = 0x7f050253
com.iapp.leochen.apkinjector:attr/queryPatterns = 0x7f030398
com.iapp.leochen.apkinjector:dimen/m3_bottomappbar_horizontal_padding = 0x7f0600ca
com.iapp.leochen.apkinjector:attr/actionBarWidgetTheme = 0x7f03000c
com.iapp.leochen.apkinjector:drawable/mtrl_switch_thumb_checked_unchecked = 0x7f0700d5
com.iapp.leochen.apkinjector:string/abc_capital_on = 0x7f100007
com.iapp.leochen.apkinjector:attr/alertDialogCenterButtons = 0x7f03002a
com.iapp.leochen.apkinjector:attr/behavior_autoHide = 0x7f030067
com.iapp.leochen.apkinjector:attr/fabAnimationMode = 0x7f0301cd
com.iapp.leochen.apkinjector:color/m3_sys_color_dark_on_surface = 0x7f050167
com.iapp.leochen.apkinjector:attr/fabAlignmentModeEndMargin = 0x7f0301cb
com.iapp.leochen.apkinjector:attr/contentPaddingEnd = 0x7f030142
com.iapp.leochen.apkinjector:attr/fabAlignmentMode = 0x7f0301ca
com.iapp.leochen.apkinjector:color/m3_ref_palette_neutral24 = 0x7f050109
com.iapp.leochen.apkinjector:attr/extraMultilineHeightEnabled = 0x7f0301c9
com.iapp.leochen.apkinjector:style/Widget.AppCompat.Light.ActionButton.Overflow = 0x7f11030d
com.iapp.leochen.apkinjector:dimen/m3_navigation_rail_item_min_height = 0x7f0601cf
com.iapp.leochen.apkinjector:color/mtrl_btn_transparent_bg_color = 0x7f0502be
com.iapp.leochen.apkinjector:attr/itemIconPadding = 0x7f030253
com.iapp.leochen.apkinjector:color/m3_navigation_item_icon_tint = 0x7f050093
com.iapp.leochen.apkinjector:dimen/m3_alert_dialog_action_bottom_padding = 0x7f06009d
com.iapp.leochen.apkinjector:dimen/m3_navigation_rail_item_active_indicator_width = 0x7f0601ce
com.iapp.leochen.apkinjector:style/Widget.Material3.Chip.Input.Icon = 0x7f11036e
com.iapp.leochen.apkinjector:color/m3_ref_palette_neutral10 = 0x7f050103
com.iapp.leochen.apkinjector:attr/brightness = 0x7f030089
com.iapp.leochen.apkinjector:attr/badgeVerticalPadding = 0x7f03005b
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.Button.UnelevatedButton = 0x7f11040a
com.iapp.leochen.apkinjector:style/Base.Widget.AppCompat.ActionMode = 0x7f1100cb
com.iapp.leochen.apkinjector:id/fitStart = 0x7f0800d0
com.iapp.leochen.apkinjector:attr/extendedFloatingActionButtonSurfaceStyle = 0x7f0301c7
com.iapp.leochen.apkinjector:macro/m3_comp_switch_disabled_unselected_track_outline_color = 0x7f0c011f
com.iapp.leochen.apkinjector:attr/extendedFloatingActionButtonSecondaryStyle = 0x7f0301c5
com.iapp.leochen.apkinjector:id/mtrl_internal_children_alpha_tag = 0x7f080137
com.iapp.leochen.apkinjector:attr/statusBarBackground = 0x7f0303ff
com.iapp.leochen.apkinjector:attr/layout_constraintHeight_max = 0x7f030293
com.iapp.leochen.apkinjector:attr/fabSize = 0x7f0301d2
com.iapp.leochen.apkinjector:attr/flow_horizontalBias = 0x7f0301ed
com.iapp.leochen.apkinjector:attr/expandedTitleTextColor = 0x7f0301c1
com.iapp.leochen.apkinjector:dimen/m3_comp_primary_navigation_tab_active_pressed_state_layer_opacity = 0x7f06015d
com.iapp.leochen.apkinjector:dimen/m3_navigation_rail_item_active_indicator_margin_horizontal = 0x7f0601cd
com.iapp.leochen.apkinjector:styleable/GradientColor = 0x7f12003b
com.iapp.leochen.apkinjector:style/Base.Widget.AppCompat.PopupMenu = 0x7f1100eb
com.iapp.leochen.apkinjector:attr/expandedTitleMarginTop = 0x7f0301bf
com.iapp.leochen.apkinjector:dimen/abc_search_view_preferred_height = 0x7f060036
com.iapp.leochen.apkinjector:id/stop = 0x7f0801c3
com.iapp.leochen.apkinjector:attr/dragScale = 0x7f030185
com.iapp.leochen.apkinjector:attr/expandedTitleGravity = 0x7f0301ba
com.iapp.leochen.apkinjector:attr/hintEnabled = 0x7f030228
com.iapp.leochen.apkinjector:attr/ifTagNotSet = 0x7f030239
com.iapp.leochen.apkinjector:attr/chainUseRtl = 0x7f0300ae
com.iapp.leochen.apkinjector:animator/design_fab_hide_motion_spec = 0x7f020001
com.iapp.leochen.apkinjector:attr/badgeHeight = 0x7f030053
com.iapp.leochen.apkinjector:attr/labelBehavior = 0x7f030270
com.iapp.leochen.apkinjector:color/material_personalized_color_primary_text_inverse = 0x7f050292
com.iapp.leochen.apkinjector:attr/rangeFillColor = 0x7f03039a
com.iapp.leochen.apkinjector:color/material_dynamic_primary90 = 0x7f050246
com.iapp.leochen.apkinjector:id/material_clock_period_toggle = 0x7f080114
com.iapp.leochen.apkinjector:attr/errorIconTintMode = 0x7f0301b3
com.iapp.leochen.apkinjector:attr/errorIconTint = 0x7f0301b2
com.iapp.leochen.apkinjector:attr/endIconTint = 0x7f0301a8
com.iapp.leochen.apkinjector:attr/errorIconDrawable = 0x7f0301b1
com.iapp.leochen.apkinjector:attr/layoutManager = 0x7f030279
com.iapp.leochen.apkinjector:dimen/notification_media_narrow_margin = 0x7f060313
com.iapp.leochen.apkinjector:dimen/material_filled_edittext_font_2_0_padding_top = 0x7f06023a
com.iapp.leochen.apkinjector:attr/enforceTextAppearance = 0x7f0301ab
com.iapp.leochen.apkinjector:layout/material_clockface_textview = 0x7f0b003e
com.iapp.leochen.apkinjector:color/design_default_color_on_error = 0x7f050042
com.iapp.leochen.apkinjector:attr/quantizeMotionInterpolator = 0x7f030393
com.iapp.leochen.apkinjector:dimen/m3_comp_elevated_card_container_elevation = 0x7f06010a
com.iapp.leochen.apkinjector:attr/endIconTintMode = 0x7f0301a9
com.iapp.leochen.apkinjector:id/scrollIndicatorUp = 0x7f080192
com.iapp.leochen.apkinjector:attr/ratingBarStyleSmall = 0x7f03039d
com.iapp.leochen.apkinjector:attr/submitBackground = 0x7f030409
com.iapp.leochen.apkinjector:attr/endIconMinSize = 0x7f0301a5
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_neutral_variant87 = 0x7f0500c6
com.iapp.leochen.apkinjector:attr/tintNavigationIcon = 0x7f030496
com.iapp.leochen.apkinjector:dimen/m3_badge_offset = 0x7f0600b4
com.iapp.leochen.apkinjector:dimen/abc_text_size_title_material_toolbar = 0x7f060050
com.iapp.leochen.apkinjector:color/material_grey_600 = 0x7f050266
com.iapp.leochen.apkinjector:style/Base.CardView = 0x7f110010
com.iapp.leochen.apkinjector:color/design_default_color_primary = 0x7f050046
com.iapp.leochen.apkinjector:attr/materialAlertDialogTheme = 0x7f0302df
com.iapp.leochen.apkinjector:dimen/abc_action_bar_default_height_material = 0x7f060002
com.iapp.leochen.apkinjector:color/m3_dynamic_dark_highlighted_text = 0x7f05007e
com.iapp.leochen.apkinjector:dimen/mtrl_slider_label_square_side = 0x7f0602e8
com.iapp.leochen.apkinjector:attr/colorSurfaceContainerHigh = 0x7f030123
com.iapp.leochen.apkinjector:attr/actionTextColorAlpha = 0x7f030024
com.iapp.leochen.apkinjector:attr/enableEdgeToEdge = 0x7f0301a1
com.iapp.leochen.apkinjector:attr/trackColorInactive = 0x7f0304b6
com.iapp.leochen.apkinjector:macro/m3_comp_time_picker_period_selector_selected_label_text_color = 0x7f0c0158
com.iapp.leochen.apkinjector:attr/animateCircleAngleTo = 0x7f030031
com.iapp.leochen.apkinjector:attr/badgeRadius = 0x7f030054
com.iapp.leochen.apkinjector:attr/elevation = 0x7f03019c
com.iapp.leochen.apkinjector:color/bright_foreground_disabled_material_light = 0x7f050023
com.iapp.leochen.apkinjector:color/m3_ref_palette_neutral95 = 0x7f050116
com.iapp.leochen.apkinjector:style/Base.Widget.AppCompat.PopupWindow = 0x7f1100ed
com.iapp.leochen.apkinjector:attr/editTextStyle = 0x7f03019b
com.iapp.leochen.apkinjector:attr/helperTextEnabled = 0x7f03021f
com.iapp.leochen.apkinjector:attr/addElevationShadow = 0x7f030028
com.iapp.leochen.apkinjector:color/design_default_color_on_secondary = 0x7f050044
com.iapp.leochen.apkinjector:color/material_personalized_color_on_secondary_container = 0x7f050286
com.iapp.leochen.apkinjector:string/mtrl_switch_thumb_path_checked = 0x7f100092
com.iapp.leochen.apkinjector:attr/enforceMaterialTheme = 0x7f0301aa
com.iapp.leochen.apkinjector:id/up = 0x7f080201
com.iapp.leochen.apkinjector:attr/dropdownListPreferredItemHeight = 0x7f030196
com.iapp.leochen.apkinjector:attr/colorPrimaryVariant = 0x7f03011a
com.iapp.leochen.apkinjector:dimen/m3_comp_fab_primary_large_container_height = 0x7f06011b
com.iapp.leochen.apkinjector:attr/layout_constraintCircleAngle = 0x7f030289
com.iapp.leochen.apkinjector:drawable/$mtrl_switch_thumb_unchecked_checked__1 = 0x7f070027
com.iapp.leochen.apkinjector:attr/cardPreventCornerOverlap = 0x7f03009f
com.iapp.leochen.apkinjector:attr/dropDownListViewStyle = 0x7f030195
com.iapp.leochen.apkinjector:styleable/Constraint = 0x7f120026
com.iapp.leochen.apkinjector:color/design_default_color_primary_dark = 0x7f050047
com.iapp.leochen.apkinjector:color/m3_ref_palette_error100 = 0x7f0500f7
com.iapp.leochen.apkinjector:color/material_dynamic_secondary20 = 0x7f05024c
com.iapp.leochen.apkinjector:attr/textBackground = 0x7f03045f
com.iapp.leochen.apkinjector:style/TextAppearance.MaterialComponents.Body1 = 0x7f1101f9
com.iapp.leochen.apkinjector:anim/m3_bottom_sheet_slide_in = 0x7f010021
com.iapp.leochen.apkinjector:macro/m3_comp_primary_navigation_tab_container_color = 0x7f0c00cb
com.iapp.leochen.apkinjector:attr/motionPathRotate = 0x7f03034d
com.iapp.leochen.apkinjector:dimen/m3_btn_translation_z_hovered = 0x7f0600e3
com.iapp.leochen.apkinjector:attr/textureBlurFactor = 0x7f030476
com.iapp.leochen.apkinjector:attr/largeFontVerticalOffsetAdjustment = 0x7f030273
com.iapp.leochen.apkinjector:id/disableScroll = 0x7f0800a5
com.iapp.leochen.apkinjector:attr/drawableTintMode = 0x7f03018f
com.iapp.leochen.apkinjector:attr/drawableRightCompat = 0x7f03018b
com.iapp.leochen.apkinjector:style/TextAppearance.Widget.AppCompat.Toolbar.Subtitle = 0x7f11020a
com.iapp.leochen.apkinjector:string/icon_content_description = 0x7f100035
com.iapp.leochen.apkinjector:attr/actionModeCloseDrawable = 0x7f030015
com.iapp.leochen.apkinjector:id/open_search_view_status_bar_spacer = 0x7f080167
com.iapp.leochen.apkinjector:attr/dragDirection = 0x7f030184
com.iapp.leochen.apkinjector:macro/m3_comp_outlined_text_field_error_trailing_icon_color = 0x7f0c00b8
com.iapp.leochen.apkinjector:attr/layout_constraintBottom_creator = 0x7f030285
com.iapp.leochen.apkinjector:dimen/mtrl_btn_letter_spacing = 0x7f060263
com.iapp.leochen.apkinjector:attr/layout_goneMarginTop = 0x7f0302b6
com.iapp.leochen.apkinjector:attr/alpha = 0x7f03002e
com.iapp.leochen.apkinjector:attr/materialCardViewStyle = 0x7f0302f8
com.iapp.leochen.apkinjector:color/m3_ref_palette_secondary10 = 0x7f050135
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.CircularProgressIndicator = 0x7f110413
com.iapp.leochen.apkinjector:attr/colorPrimaryInverse = 0x7f030118
com.iapp.leochen.apkinjector:attr/drawPath = 0x7f030187
com.iapp.leochen.apkinjector:attr/homeLayout = 0x7f03022c
com.iapp.leochen.apkinjector:attr/dragThreshold = 0x7f030186
com.iapp.leochen.apkinjector:macro/m3_comp_date_picker_modal_date_selected_label_text_color = 0x7f0c0011
com.iapp.leochen.apkinjector:attr/buttonStyleSmall = 0x7f030097
com.iapp.leochen.apkinjector:attr/marginHorizontal = 0x7f0302d9
com.iapp.leochen.apkinjector:color/m3_ref_palette_neutral_variant40 = 0x7f05011f
com.iapp.leochen.apkinjector:attr/listChoiceIndicatorMultipleAnimated = 0x7f0302c7
com.iapp.leochen.apkinjector:style/ThemeOverlay.Material3.TextInputEditText.FilledBox = 0x7f1102ba
com.iapp.leochen.apkinjector:attr/chipStartPadding = 0x7f0300cd
com.iapp.leochen.apkinjector:style/ThemeOverlay.Material3.Button.TonalButton = 0x7f110292
com.iapp.leochen.apkinjector:attr/carousel_touchUp_dampeningFactor = 0x7f0300ab
com.iapp.leochen.apkinjector:attr/actionOverflowButtonStyle = 0x7f030021
com.iapp.leochen.apkinjector:color/m3_ref_palette_error0 = 0x7f0500f5
com.iapp.leochen.apkinjector:attr/badgeWidth = 0x7f03005d
com.iapp.leochen.apkinjector:dimen/m3_comp_outlined_card_container_elevation = 0x7f060150
com.iapp.leochen.apkinjector:attr/dividerThickness = 0x7f030182
com.iapp.leochen.apkinjector:attr/floatingActionButtonSmallTertiaryStyle = 0x7f0301e4
com.iapp.leochen.apkinjector:dimen/design_bottom_navigation_active_text_size = 0x7f060061
com.iapp.leochen.apkinjector:attr/fastScrollEnabled = 0x7f0301d3
com.iapp.leochen.apkinjector:attr/popupMenuBackground = 0x7f030388
com.iapp.leochen.apkinjector:attr/dividerInsetEnd = 0x7f03017f
com.iapp.leochen.apkinjector:attr/blendSrc = 0x7f030073
com.iapp.leochen.apkinjector:style/Base.Widget.MaterialComponents.PopupMenu.Overflow = 0x7f11011a
com.iapp.leochen.apkinjector:attr/errorShown = 0x7f0301b4
com.iapp.leochen.apkinjector:attr/customDimension = 0x7f030165
com.iapp.leochen.apkinjector:attr/backgroundTint = 0x7f030050
com.iapp.leochen.apkinjector:dimen/m3_comp_switch_disabled_selected_icon_opacity = 0x7f060190
com.iapp.leochen.apkinjector:attr/gestureInsetBottomIgnored = 0x7f03020c
com.iapp.leochen.apkinjector:attr/chipIconEnabled = 0x7f0300c3
com.iapp.leochen.apkinjector:color/m3_ref_palette_primary40 = 0x7f05012c
com.iapp.leochen.apkinjector:style/Widget.Material3.Button.ElevatedButton = 0x7f110352
com.iapp.leochen.apkinjector:attr/deltaPolarAngle = 0x7f030175
com.iapp.leochen.apkinjector:color/secondary_text_default_material_light = 0x7f0502fb
com.iapp.leochen.apkinjector:attr/layout_constraintStart_toStartOf = 0x7f0302a0
com.iapp.leochen.apkinjector:attr/colorOutlineVariant = 0x7f030112
com.iapp.leochen.apkinjector:dimen/mtrl_high_ripple_focused_alpha = 0x7f0602bb
com.iapp.leochen.apkinjector:color/material_dynamic_secondary100 = 0x7f05024b
com.iapp.leochen.apkinjector:style/Widget.Material3.MaterialCalendar.Day.Selected = 0x7f11039e
com.iapp.leochen.apkinjector:color/m3_card_stroke_color = 0x7f05006d
com.iapp.leochen.apkinjector:integer/hide_password_duration = 0x7f090008
com.iapp.leochen.apkinjector:attr/wavePhase = 0x7f0304dd
com.iapp.leochen.apkinjector:id/easeIn = 0x7f0800b0
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_light_surface_container_high = 0x7f0501b8
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_dark_on_background = 0x7f050182
com.iapp.leochen.apkinjector:style/TextAppearance.Design.Hint = 0x7f1101ce
com.iapp.leochen.apkinjector:color/material_dynamic_neutral90 = 0x7f05022c
com.iapp.leochen.apkinjector:styleable/MaterialAutoCompleteTextView = 0x7f12004f
com.iapp.leochen.apkinjector:style/Theme.ApkInjector = 0x7f11020c
com.iapp.leochen.apkinjector:attr/customPixelDimension = 0x7f030169
com.iapp.leochen.apkinjector:style/ThemeOverlay.Material3.Light.Dialog.Alert.Framework = 0x7f1102aa
com.iapp.leochen.apkinjector:attr/curveFit = 0x7f030161
com.iapp.leochen.apkinjector:id/tag_state_description = 0x7f0801d2
com.iapp.leochen.apkinjector:dimen/m3_comp_suggestion_chip_elevated_container_elevation = 0x7f06018b
com.iapp.leochen.apkinjector:style/Theme.Design.BottomSheetDialog = 0x7f110224
com.iapp.leochen.apkinjector:attr/cursorErrorColor = 0x7f030160
com.iapp.leochen.apkinjector:string/mtrl_picker_text_input_date_hint = 0x7f100086
com.iapp.leochen.apkinjector:attr/currentState = 0x7f03015e
com.iapp.leochen.apkinjector:attr/textAppearanceHeadline4 = 0x7f030447
com.iapp.leochen.apkinjector:attr/materialCalendarMonthNavigationButton = 0x7f0302f1
com.iapp.leochen.apkinjector:attr/counterTextAppearance = 0x7f03015b
com.iapp.leochen.apkinjector:style/Base.Widget.AppCompat.ButtonBar = 0x7f1100d4
com.iapp.leochen.apkinjector:color/m3_ref_palette_error90 = 0x7f0500ff
com.iapp.leochen.apkinjector:attr/textAppearanceSubtitle1 = 0x7f03045a
com.iapp.leochen.apkinjector:attr/colorAccent = 0x7f0300f3
com.iapp.leochen.apkinjector:dimen/mtrl_textinput_box_corner_radius_small = 0x7f0602fd
com.iapp.leochen.apkinjector:attr/flow_firstHorizontalStyle = 0x7f0301e9
com.iapp.leochen.apkinjector:attr/counterOverflowTextAppearance = 0x7f030159
com.iapp.leochen.apkinjector:macro/m3_comp_icon_button_unselected_icon_color = 0x7f0c005a
com.iapp.leochen.apkinjector:color/abc_primary_text_disable_only_material_light = 0x7f05000a
com.iapp.leochen.apkinjector:style/Base.TextAppearance.Widget.AppCompat.Toolbar.Title = 0x7f11004a
com.iapp.leochen.apkinjector:attr/springMass = 0x7f0303e8
com.iapp.leochen.apkinjector:dimen/m3_comp_switch_track_width = 0x7f060198
com.iapp.leochen.apkinjector:attr/counterEnabled = 0x7f030157
com.iapp.leochen.apkinjector:macro/m3_comp_outlined_card_focus_outline_color = 0x7f0c00ac
com.iapp.leochen.apkinjector:anim/mtrl_bottom_sheet_slide_out = 0x7f01002a
com.iapp.leochen.apkinjector:attr/collapsedTitleTextAppearance = 0x7f0300eb
com.iapp.leochen.apkinjector:attr/dividerVertical = 0x7f030183
com.iapp.leochen.apkinjector:attr/cornerSize = 0x7f030152
com.iapp.leochen.apkinjector:attr/cornerRadius = 0x7f030151
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_dark_on_error = 0x7f050183
com.iapp.leochen.apkinjector:attr/cornerFamilyTopRight = 0x7f030150
com.iapp.leochen.apkinjector:id/jumpToEnd = 0x7f0800fa
com.iapp.leochen.apkinjector:attr/itemTextAppearanceActiveBoldEnabled = 0x7f030268
com.iapp.leochen.apkinjector:attr/actionBarDivider = 0x7f030002
com.iapp.leochen.apkinjector:dimen/mtrl_navigation_item_icon_size = 0x7f0602c8
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_primary99 = 0x7f0500da
com.iapp.leochen.apkinjector:attr/buttonTintMode = 0x7f030099
com.iapp.leochen.apkinjector:attr/cornerFamily = 0x7f03014c
com.iapp.leochen.apkinjector:attr/boxStrokeColor = 0x7f030085
com.iapp.leochen.apkinjector:attr/flow_wrapMode = 0x7f0301fa
com.iapp.leochen.apkinjector:anim/abc_slide_out_top = 0x7f010009
com.iapp.leochen.apkinjector:id/touch_outside = 0x7f0801f2
com.iapp.leochen.apkinjector:attr/coplanarSiblingViewId = 0x7f03014b
com.iapp.leochen.apkinjector:dimen/m3_comp_progress_indicator_stop_indicator_size = 0x7f060163
com.iapp.leochen.apkinjector:style/Widget.Material3.AutoCompleteTextView.FilledBox.Dense = 0x7f110343
com.iapp.leochen.apkinjector:attr/materialThemeOverlay = 0x7f030308
com.iapp.leochen.apkinjector:animator/fragment_close_exit = 0x7f020004
com.iapp.leochen.apkinjector:attr/coordinatorLayoutStyle = 0x7f03014a
com.iapp.leochen.apkinjector:style/Base.Widget.AppCompat.EditText = 0x7f1100dc
com.iapp.leochen.apkinjector:attr/transformPivotTarget = 0x7f0304c1
com.iapp.leochen.apkinjector:attr/contentScrim = 0x7f030147
com.iapp.leochen.apkinjector:style/Widget.Material3.MaterialTimePicker.ImageButton = 0x7f1103bc
com.iapp.leochen.apkinjector:style/Widget.Material3.Button.ElevatedButton.Icon = 0x7f110353
com.iapp.leochen.apkinjector:dimen/abc_disabled_alpha_material_dark = 0x7f060027
com.iapp.leochen.apkinjector:attr/contentPaddingBottom = 0x7f030141
com.iapp.leochen.apkinjector:dimen/m3_navigation_rail_item_padding_top_with_large_font = 0x7f0601d3
com.iapp.leochen.apkinjector:attr/textColorSearchUrl = 0x7f030465
com.iapp.leochen.apkinjector:dimen/material_time_picker_minimum_screen_height = 0x7f060244
com.iapp.leochen.apkinjector:mipmap/ic_launcher = 0x7f0e0000
com.iapp.leochen.apkinjector:attr/buttonBarNeutralButtonStyle = 0x7f03008c
com.iapp.leochen.apkinjector:attr/materialCardViewElevatedStyle = 0x7f0302f5
com.iapp.leochen.apkinjector:attr/closeItemLayout = 0x7f0300e6
com.iapp.leochen.apkinjector:attr/layout_constraintLeft_toLeftOf = 0x7f03029a
com.iapp.leochen.apkinjector:attr/materialCalendarStyle = 0x7f0302f2
com.iapp.leochen.apkinjector:id/mtrl_calendar_year_selector_frame = 0x7f080134
com.iapp.leochen.apkinjector:attr/contentInsetStartWithNavigation = 0x7f03013f
com.iapp.leochen.apkinjector:string/abc_menu_alt_shortcut_label = 0x7f100008
com.iapp.leochen.apkinjector:attr/horizontalOffsetWithText = 0x7f03022e
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_light_inverse_primary = 0x7f0501a2
com.iapp.leochen.apkinjector:attr/contentInsetStart = 0x7f03013e
com.iapp.leochen.apkinjector:dimen/mtrl_badge_horizontal_edge_offset = 0x7f06024b
com.iapp.leochen.apkinjector:attr/contentInsetEndWithActions = 0x7f03013b
com.iapp.leochen.apkinjector:style/Widget.AppCompat.ListPopupWindow = 0x7f110319
com.iapp.leochen.apkinjector:attr/contentInsetEnd = 0x7f03013a
com.iapp.leochen.apkinjector:dimen/m3_btn_padding_right = 0x7f0600db
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_tertiary95 = 0x7f0500f3
com.iapp.leochen.apkinjector:style/Theme.MaterialComponents.Light.Dialog = 0x7f110269
com.iapp.leochen.apkinjector:anim/m3_side_sheet_exit_to_left = 0x7f010027
com.iapp.leochen.apkinjector:attr/contentDescription = 0x7f030139
com.iapp.leochen.apkinjector:attr/transitionDisable = 0x7f0304c2
com.iapp.leochen.apkinjector:style/Base.Widget.AppCompat.RatingBar = 0x7f1100f0
com.iapp.leochen.apkinjector:id/flip = 0x7f0800d4
com.iapp.leochen.apkinjector:attr/colorSecondaryVariant = 0x7f03011f
com.iapp.leochen.apkinjector:attr/hideAnimationBehavior = 0x7f030222
com.iapp.leochen.apkinjector:macro/m3_comp_secondary_navigation_tab_active_label_text_color = 0x7f0c00fb
com.iapp.leochen.apkinjector:attr/itemShapeFillColor = 0x7f03025e
com.iapp.leochen.apkinjector:id/date_picker_actions = 0x7f080093
com.iapp.leochen.apkinjector:attr/layout_constraintEnd_toEndOf = 0x7f03028c
com.iapp.leochen.apkinjector:drawable/abc_ic_menu_paste_mtrl_am_alpha = 0x7f070046
com.iapp.leochen.apkinjector:dimen/m3_card_disabled_z = 0x7f0600e4
com.iapp.leochen.apkinjector:animator/no_elevation = 0x7f020022
com.iapp.leochen.apkinjector:macro/m3_comp_radio_button_selected_focus_state_layer_color = 0x7f0c00d9
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_neutral_variant10 = 0x7f0500b7
com.iapp.leochen.apkinjector:macro/m3_comp_navigation_rail_container_color = 0x7f0c0099
com.iapp.leochen.apkinjector:attr/actionBarItemBackground = 0x7f030003
com.iapp.leochen.apkinjector:attr/listPreferredItemPaddingRight = 0x7f0302d3
com.iapp.leochen.apkinjector:anim/slide_in_right_smooth = 0x7f01002f
com.iapp.leochen.apkinjector:attr/motionDurationLong2 = 0x7f03032c
com.iapp.leochen.apkinjector:attr/colorSurfaceVariant = 0x7f030129
com.iapp.leochen.apkinjector:attr/chipIconVisible = 0x7f0300c6
com.iapp.leochen.apkinjector:anim/linear_indeterminate_line1_tail_interpolator = 0x7f01001e
com.iapp.leochen.apkinjector:dimen/m3_comp_switch_track_height = 0x7f060197
com.iapp.leochen.apkinjector:attr/colorSurfaceInverse = 0x7f030128
com.iapp.leochen.apkinjector:drawable/abc_menu_hardkey_panel_mtrl_mult = 0x7f070059
com.iapp.leochen.apkinjector:attr/deriveConstraintsFrom = 0x7f030177
com.iapp.leochen.apkinjector:style/Widget.Material3.BottomAppBar.Legacy = 0x7f11034a
com.iapp.leochen.apkinjector:style/ShapeAppearance.M3.Sys.Shape.Corner.Medium = 0x7f11016c
com.iapp.leochen.apkinjector:attr/textAppearanceListItemSmall = 0x7f030454
com.iapp.leochen.apkinjector:string/mtrl_checkbox_button_icon_path_indeterminate = 0x7f100060
com.iapp.leochen.apkinjector:attr/showText = 0x7f0303d0
com.iapp.leochen.apkinjector:style/Theme.Design.NoActionBar = 0x7f110228
com.iapp.leochen.apkinjector:attr/textAppearanceTitleMedium = 0x7f03045d
com.iapp.leochen.apkinjector:dimen/abc_action_bar_default_padding_end_material = 0x7f060003
com.iapp.leochen.apkinjector:id/open_search_bar_text_view = 0x7f08015c
com.iapp.leochen.apkinjector:attr/colorSurfaceDim = 0x7f030127
com.iapp.leochen.apkinjector:attr/windowFixedWidthMajor = 0x7f0304e5
com.iapp.leochen.apkinjector:style/ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox.Dense = 0x7f1102e1
com.iapp.leochen.apkinjector:string/material_motion_easing_emphasized = 0x7f100050
com.iapp.leochen.apkinjector:attr/flow_lastHorizontalBias = 0x7f0301f0
com.iapp.leochen.apkinjector:attr/contentInsetLeft = 0x7f03013c
com.iapp.leochen.apkinjector:attr/materialDividerHeavyStyle = 0x7f0302fc
com.iapp.leochen.apkinjector:styleable/SideSheetBehavior_Layout = 0x7f12007d
com.iapp.leochen.apkinjector:style/Base.TextAppearance.AppCompat.SearchResult = 0x7f110028
com.iapp.leochen.apkinjector:attr/actionProviderClass = 0x7f030023
com.iapp.leochen.apkinjector:id/sharedValueSet = 0x7f0801a2
com.iapp.leochen.apkinjector:id/reverseSawtooth = 0x7f080184
com.iapp.leochen.apkinjector:dimen/mtrl_alert_dialog_background_inset_end = 0x7f060247
com.iapp.leochen.apkinjector:color/m3_sys_color_on_secondary_fixed = 0x7f0501f0
com.iapp.leochen.apkinjector:macro/m3_comp_outlined_card_container_shape = 0x7f0c00a9
com.iapp.leochen.apkinjector:attr/colorSurfaceBright = 0x7f030121
com.iapp.leochen.apkinjector:color/m3_ref_palette_tertiary100 = 0x7f050143
com.iapp.leochen.apkinjector:attr/floatingActionButtonLargePrimaryStyle = 0x7f0301d9
com.iapp.leochen.apkinjector:attr/colorSecondaryContainer = 0x7f03011c
com.iapp.leochen.apkinjector:string/mtrl_checkbox_button_icon_path_group_name = 0x7f10005f
com.iapp.leochen.apkinjector:attr/floatingActionButtonLargeSecondaryStyle = 0x7f0301da
com.iapp.leochen.apkinjector:color/material_dynamic_primary30 = 0x7f050240
com.iapp.leochen.apkinjector:attr/searchViewStyle = 0x7f0303b5
com.iapp.leochen.apkinjector:attr/colorPrimaryFixedDim = 0x7f030117
com.iapp.leochen.apkinjector:id/open_search_view_clear_button = 0x7f08015e
com.iapp.leochen.apkinjector:attr/colorPrimaryDark = 0x7f030115
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_tertiary10 = 0x7f0500e9
com.iapp.leochen.apkinjector:attr/indicatorDirectionLinear = 0x7f030244
com.iapp.leochen.apkinjector:color/abc_secondary_text_material_dark = 0x7f050011
com.iapp.leochen.apkinjector:color/material_dynamic_neutral_variant70 = 0x7f050237
com.iapp.leochen.apkinjector:attr/backgroundTintMode = 0x7f030051
com.iapp.leochen.apkinjector:attr/controlBackground = 0x7f030149
com.iapp.leochen.apkinjector:string/material_motion_easing_accelerated = 0x7f10004e
com.iapp.leochen.apkinjector:attr/motionEasingStandardDecelerateInterpolator = 0x7f030341
com.iapp.leochen.apkinjector:attr/layout_constraintRight_toRightOf = 0x7f03029e
com.iapp.leochen.apkinjector:style/TextAppearance.Design.Tab = 0x7f1101d3
com.iapp.leochen.apkinjector:attr/layout_constraintHeight_default = 0x7f030292
com.iapp.leochen.apkinjector:id/accessibility_custom_action_19 = 0x7f08001c
com.iapp.leochen.apkinjector:attr/colorOnSecondaryFixedVariant = 0x7f030109
com.iapp.leochen.apkinjector:attr/badgeWidePadding = 0x7f03005c
com.iapp.leochen.apkinjector:dimen/m3_navigation_rail_label_padding_horizontal = 0x7f0601d4
com.iapp.leochen.apkinjector:dimen/m3_comp_switch_disabled_track_opacity = 0x7f060191
com.iapp.leochen.apkinjector:color/abc_btn_colored_borderless_text_material = 0x7f050002
com.iapp.leochen.apkinjector:style/Base.Theme.AppCompat.Dialog = 0x7f11004e
com.iapp.leochen.apkinjector:id/snackbar_action = 0x7f0801ac
com.iapp.leochen.apkinjector:attr/colorOnTertiaryFixed = 0x7f03010f
com.iapp.leochen.apkinjector:dimen/material_clock_hand_padding = 0x7f060228
com.iapp.leochen.apkinjector:style/Theme.Material3.Light.Dialog.MinWidth = 0x7f110243
com.iapp.leochen.apkinjector:dimen/item_touch_helper_swipe_escape_velocity = 0x7f06009c
com.iapp.leochen.apkinjector:string/mtrl_picker_invalid_format_example = 0x7f100079
com.iapp.leochen.apkinjector:dimen/abc_action_bar_subtitle_top_margin_material = 0x7f06000c
com.iapp.leochen.apkinjector:dimen/m3_comp_navigation_rail_pressed_state_layer_opacity = 0x7f06014c
com.iapp.leochen.apkinjector:attr/layout_goneMarginEnd = 0x7f0302b2
com.iapp.leochen.apkinjector:id/mtrl_picker_title_text = 0x7f080141
com.iapp.leochen.apkinjector:attr/itemTextAppearanceActive = 0x7f030267
com.iapp.leochen.apkinjector:attr/trackInsideCornerSize = 0x7f0304bc
com.iapp.leochen.apkinjector:dimen/material_clock_face_margin_bottom = 0x7f060225
com.iapp.leochen.apkinjector:anim/slide_out_right = 0x7f010032
com.iapp.leochen.apkinjector:macro/m3_comp_outlined_card_container_color = 0x7f0c00a8
com.iapp.leochen.apkinjector:attr/colorPrimaryContainer = 0x7f030114
com.iapp.leochen.apkinjector:color/material_dynamic_primary0 = 0x7f05023c
com.iapp.leochen.apkinjector:macro/m3_comp_filled_icon_button_toggle_unselected_icon_color = 0x7f0c004a
com.iapp.leochen.apkinjector:attr/checkedButton = 0x7f0300b3
com.iapp.leochen.apkinjector:attr/motionDurationMedium2 = 0x7f030330
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_dark_surface_container_lowest = 0x7f050199
com.iapp.leochen.apkinjector:attr/numericModifiers = 0x7f03035f
com.iapp.leochen.apkinjector:attr/colorOnSecondaryFixed = 0x7f030108
com.iapp.leochen.apkinjector:color/m3_radiobutton_button_tint = 0x7f05009b
com.iapp.leochen.apkinjector:color/m3_tonal_button_ripple_color_selector = 0x7f050212
com.iapp.leochen.apkinjector:color/material_personalized_primary_text_disable_only = 0x7f0502ab
com.iapp.leochen.apkinjector:color/m3_sys_color_light_on_error_container = 0x7f0501d4
com.iapp.leochen.apkinjector:attr/colorControlHighlight = 0x7f0300f8
com.iapp.leochen.apkinjector:string/call_notification_incoming_text = 0x7f100028
com.iapp.leochen.apkinjector:color/material_dynamic_tertiary0 = 0x7f050256
com.iapp.leochen.apkinjector:color/m3_ref_palette_error50 = 0x7f0500fb
com.iapp.leochen.apkinjector:dimen/m3_navigation_drawer_layout_corner_size = 0x7f0601be
com.iapp.leochen.apkinjector:styleable/MotionScene = 0x7f120068
com.iapp.leochen.apkinjector:integer/m3_chip_anim_duration = 0x7f09000e
com.iapp.leochen.apkinjector:attr/chipIconSize = 0x7f0300c4
com.iapp.leochen.apkinjector:drawable/mtrl_ic_indeterminate = 0x7f0700ce
com.iapp.leochen.apkinjector:attr/checkboxStyle = 0x7f0300b2
com.iapp.leochen.apkinjector:id/topSection = 0x7f0801f1
com.iapp.leochen.apkinjector:color/m3_navigation_item_text_color = 0x7f050095
com.iapp.leochen.apkinjector:attr/titleTextAppearance = 0x7f0304a2
com.iapp.leochen.apkinjector:drawable/notification_bg = 0x7f0700e1
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_neutral40 = 0x7f0500a8
com.iapp.leochen.apkinjector:string/m3_ref_typeface_brand_regular = 0x7f100039
com.iapp.leochen.apkinjector:color/mtrl_card_view_ripple = 0x7f0502c2
com.iapp.leochen.apkinjector:attr/textAppearanceLineHeightEnabled = 0x7f030451
com.iapp.leochen.apkinjector:styleable/Variant = 0x7f120094
com.iapp.leochen.apkinjector:attr/windowNoTitle = 0x7f0304e9
com.iapp.leochen.apkinjector:attr/colorOnContainer = 0x7f0300fd
com.iapp.leochen.apkinjector:layout/m3_side_sheet_dialog = 0x7f0b0038
com.iapp.leochen.apkinjector:dimen/m3_badge_with_text_horizontal_offset = 0x7f0600b7
com.iapp.leochen.apkinjector:drawable/m3_avd_show_password = 0x7f0700a6
com.iapp.leochen.apkinjector:style/Base.Theme.Material3.Dark.Dialog.FixedSize = 0x7f11005d
com.iapp.leochen.apkinjector:string/character_counter_overflowed_content_description = 0x7f10002c
com.iapp.leochen.apkinjector:attr/layout_constraintHorizontal_weight = 0x7f030298
com.iapp.leochen.apkinjector:style/TextAppearance.AppCompat.Display4 = 0x7f11019c
com.iapp.leochen.apkinjector:attr/backgroundColor = 0x7f030048
com.iapp.leochen.apkinjector:id/inward = 0x7f0800f6
com.iapp.leochen.apkinjector:dimen/mtrl_btn_padding_right = 0x7f060267
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.Toolbar.Surface = 0x7f110467
com.iapp.leochen.apkinjector:macro/m3_comp_slider_disabled_inactive_track_color = 0x7f0c010e
com.iapp.leochen.apkinjector:attr/chipStandaloneStyle = 0x7f0300cc
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_neutral_variant30 = 0x7f0500be
com.iapp.leochen.apkinjector:style/Base.TextAppearance.AppCompat.Display3 = 0x7f11001d
com.iapp.leochen.apkinjector:drawable/ic_arrow_upward = 0x7f070089
com.iapp.leochen.apkinjector:attr/buttonGravity = 0x7f030090
com.iapp.leochen.apkinjector:style/Base.V21.Theme.MaterialComponents.Light = 0x7f1100a8
com.iapp.leochen.apkinjector:attr/collapsingToolbarLayoutLargeStyle = 0x7f0300ee
com.iapp.leochen.apkinjector:anim/slide_out_left = 0x7f010030
com.iapp.leochen.apkinjector:dimen/mtrl_high_ripple_pressed_alpha = 0x7f0602bd
com.iapp.leochen.apkinjector:style/Base.ThemeOverlay.Material3.SideSheetDialog = 0x7f110083
com.iapp.leochen.apkinjector:attr/colorPrimary = 0x7f030113
com.iapp.leochen.apkinjector:attr/customNavigationLayout = 0x7f030168
com.iapp.leochen.apkinjector:dimen/abc_search_view_preferred_width = 0x7f060037
com.iapp.leochen.apkinjector:attr/waveShape = 0x7f0304de
com.iapp.leochen.apkinjector:attr/layout_constraintBaseline_creator = 0x7f030281
com.iapp.leochen.apkinjector:dimen/material_textinput_default_width = 0x7f060241
com.iapp.leochen.apkinjector:color/material_dynamic_neutral80 = 0x7f05022b
com.iapp.leochen.apkinjector:attr/closeIconStartPadding = 0x7f0300e3
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.TimePicker.Clock = 0x7f11045c
com.iapp.leochen.apkinjector:color/design_dark_default_color_secondary = 0x7f05003c
com.iapp.leochen.apkinjector:color/m3_chip_text_color = 0x7f050074
com.iapp.leochen.apkinjector:style/Theme.MaterialComponents.Light.NoActionBar = 0x7f110272
com.iapp.leochen.apkinjector:attr/floatingActionButtonSmallStyle = 0x7f0301e2
com.iapp.leochen.apkinjector:style/Base.ThemeOverlay.AppCompat = 0x7f110079
com.iapp.leochen.apkinjector:color/m3_navigation_rail_ripple_color_selector = 0x7f050098
com.iapp.leochen.apkinjector:anim/design_bottom_sheet_slide_in = 0x7f010018
com.iapp.leochen.apkinjector:attr/layout_constraintTop_toBottomOf = 0x7f0302a3
com.iapp.leochen.apkinjector:color/mtrl_on_surface_ripple_color = 0x7f0502da
com.iapp.leochen.apkinjector:attr/yearStyle = 0x7f0304eb
com.iapp.leochen.apkinjector:style/Theme.MaterialComponents.Light.DialogWhenLarge = 0x7f110271
com.iapp.leochen.apkinjector:attr/tabIconTint = 0x7f03041b
com.iapp.leochen.apkinjector:style/Widget.Design.NavigationView = 0x7f110338
com.iapp.leochen.apkinjector:dimen/m3_searchbar_height = 0x7f0601db
com.iapp.leochen.apkinjector:id/pathRelative = 0x7f080175
com.iapp.leochen.apkinjector:color/mtrl_chip_surface_color = 0x7f0502c5
com.iapp.leochen.apkinjector:style/ShapeAppearanceOverlay.Material3.SearchBar = 0x7f110189
com.iapp.leochen.apkinjector:attr/indeterminateProgressStyle = 0x7f030241
com.iapp.leochen.apkinjector:interpolator/m3_sys_motion_easing_standard = 0x7f0a000b
com.iapp.leochen.apkinjector:dimen/m3_comp_menu_container_elevation = 0x7f060135
com.iapp.leochen.apkinjector:style/TextAppearance.MaterialComponents.Headline5 = 0x7f110202
com.iapp.leochen.apkinjector:anim/fragment_fast_out_extra_slow_in = 0x7f01001c
com.iapp.leochen.apkinjector:attr/layout_editor_absoluteY = 0x7f0302af
com.iapp.leochen.apkinjector:attr/motionEasingStandardAccelerateInterpolator = 0x7f030340
com.iapp.leochen.apkinjector:anim/btn_checkbox_to_checked_icon_null_animation = 0x7f01000e
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.ActionBar.Surface = 0x7f1103ee
com.iapp.leochen.apkinjector:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Text = 0x7f11014c
com.iapp.leochen.apkinjector:dimen/m3_sys_motion_easing_emphasized_accelerate_control_y2 = 0x7f0601f9
com.iapp.leochen.apkinjector:attr/colorPrimarySurface = 0x7f030119
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_secondary95 = 0x7f0500e6
com.iapp.leochen.apkinjector:attr/layout_constraintBottom_toBottomOf = 0x7f030286
com.iapp.leochen.apkinjector:attr/layout = 0x7f030276
com.iapp.leochen.apkinjector:style/Widget.AppCompat.Light.ActionBar.Solid.Inverse = 0x7f110304
com.iapp.leochen.apkinjector:dimen/tooltip_precise_anchor_threshold = 0x7f06031f
com.iapp.leochen.apkinjector:attr/useMaterialThemeColors = 0x7f0304ce
com.iapp.leochen.apkinjector:attr/clockHandColor = 0x7f0300dc
com.iapp.leochen.apkinjector:id/clear_text = 0x7f08007b
com.iapp.leochen.apkinjector:color/material_personalized_color_on_primary = 0x7f050283
com.iapp.leochen.apkinjector:id/accessibility_custom_action_24 = 0x7f080022
com.iapp.leochen.apkinjector:attr/materialTimePickerStyle = 0x7f030309
com.iapp.leochen.apkinjector:color/material_personalized_color_primary_text = 0x7f050291
com.iapp.leochen.apkinjector:attr/circleRadius = 0x7f0300d2
com.iapp.leochen.apkinjector:style/TextAppearance.AppCompat.Light.SearchResult.Subtitle = 0x7f1101a1
com.iapp.leochen.apkinjector:id/add = 0x7f080047
com.iapp.leochen.apkinjector:attr/grid_verticalGaps = 0x7f030218
com.iapp.leochen.apkinjector:attr/preserveIconSpacing = 0x7f03038f
com.iapp.leochen.apkinjector:id/text_input_end_icon = 0x7f0801e0
com.iapp.leochen.apkinjector:attr/constraintSet = 0x7f030132
com.iapp.leochen.apkinjector:style/Theme.MaterialComponents.Light.Dialog.MinWidth.Bridge = 0x7f110270
com.iapp.leochen.apkinjector:style/TextAppearance.Material3.DisplaySmall = 0x7f1101ea
com.iapp.leochen.apkinjector:drawable/ic_clock_black_24dp = 0x7f070093
com.iapp.leochen.apkinjector:attr/chipEndPadding = 0x7f0300c0
com.iapp.leochen.apkinjector:attr/chipStrokeColor = 0x7f0300ce
com.iapp.leochen.apkinjector:attr/floatingActionButtonLargeStyle = 0x7f0301db
com.iapp.leochen.apkinjector:dimen/m3_comp_filter_chip_with_icon_icon_size = 0x7f06012f
com.iapp.leochen.apkinjector:color/mtrl_btn_text_color_selector = 0x7f0502bd
com.iapp.leochen.apkinjector:styleable/MaterialAlertDialog = 0x7f12004d
com.iapp.leochen.apkinjector:style/Widget.AppCompat.Spinner = 0x7f110329
com.iapp.leochen.apkinjector:color/material_timepicker_clock_text_color = 0x7f0502b4
com.iapp.leochen.apkinjector:attr/itemStrokeColor = 0x7f030264
com.iapp.leochen.apkinjector:style/ShapeAppearance.M3.Sys.Shape.Corner.ExtraSmall = 0x7f110169
com.iapp.leochen.apkinjector:id/accessibility_custom_action_0 = 0x7f080011
com.iapp.leochen.apkinjector:attr/keyboardIcon = 0x7f03026d
com.iapp.leochen.apkinjector:attr/floatingActionButtonSmallPrimaryStyle = 0x7f0301e0
com.iapp.leochen.apkinjector:attr/circularflow_defaultAngle = 0x7f0300d5
com.iapp.leochen.apkinjector:style/Widget.Material3.TextInputLayout.FilledBox.Dense.ExposedDropdownMenu = 0x7f1103e0
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_neutral92 = 0x7f0500b0
com.iapp.leochen.apkinjector:string/clear_text_end_icon_content_description = 0x7f10002e
com.iapp.leochen.apkinjector:attr/activeIndicatorLabelPadding = 0x7f030026
com.iapp.leochen.apkinjector:attr/collapsingToolbarLayoutStyle = 0x7f0300f1
com.iapp.leochen.apkinjector:macro/m3_comp_dialog_supporting_text_type = 0x7f0c0027
com.iapp.leochen.apkinjector:dimen/mtrl_calendar_selection_text_baseline_to_top = 0x7f060292
com.iapp.leochen.apkinjector:styleable/StateListDrawableItem = 0x7f120084
com.iapp.leochen.apkinjector:attr/chipSpacingVertical = 0x7f0300cb
com.iapp.leochen.apkinjector:drawable/design_fab_background = 0x7f070082
com.iapp.leochen.apkinjector:anim/mtrl_card_lowers_interpolator = 0x7f01002b
com.iapp.leochen.apkinjector:attr/boxBackgroundMode = 0x7f03007f
com.iapp.leochen.apkinjector:attr/chipSpacing = 0x7f0300c9
com.iapp.leochen.apkinjector:attr/touchAnchorId = 0x7f0304b0
com.iapp.leochen.apkinjector:attr/chipMinTouchTargetSize = 0x7f0300c8
com.iapp.leochen.apkinjector:string/m3_sys_motion_easing_emphasized = 0x7f10003c
com.iapp.leochen.apkinjector:attr/mock_showLabel = 0x7f030325
com.iapp.leochen.apkinjector:style/Base.Theme.MaterialComponents.Light.Dialog.Alert = 0x7f110074
com.iapp.leochen.apkinjector:attr/checkMarkCompat = 0x7f0300af
com.iapp.leochen.apkinjector:attr/startIconTintMode = 0x7f0303f4
com.iapp.leochen.apkinjector:attr/prefixText = 0x7f03038c
com.iapp.leochen.apkinjector:drawable/$mtrl_checkbox_button_icon_checked_indeterminate__0 = 0x7f070010
com.iapp.leochen.apkinjector:attr/actionModeBackground = 0x7f030012
com.iapp.leochen.apkinjector:attr/collapsedTitleTextColor = 0x7f0300ec
com.iapp.leochen.apkinjector:attr/materialIconButtonStyle = 0x7f030301
com.iapp.leochen.apkinjector:drawable/$avd_show_password__1 = 0x7f070004
com.iapp.leochen.apkinjector:attr/closeIconTint = 0x7f0300e4
com.iapp.leochen.apkinjector:attr/titleTextEllipsize = 0x7f0304a4
com.iapp.leochen.apkinjector:attr/contrast = 0x7f030148
com.iapp.leochen.apkinjector:drawable/abc_textfield_search_material = 0x7f070076
com.iapp.leochen.apkinjector:dimen/m3_btn_text_btn_padding_right = 0x7f0600e1
com.iapp.leochen.apkinjector:anim/design_bottom_sheet_slide_out = 0x7f010019
com.iapp.leochen.apkinjector:id/cache_measures = 0x7f08006a
com.iapp.leochen.apkinjector:attr/viewTransitionOnPositiveCross = 0x7f0304d6
com.iapp.leochen.apkinjector:dimen/mtrl_tooltip_arrowSize = 0x7f060306
com.iapp.leochen.apkinjector:dimen/m3_comp_outlined_card_disabled_outline_opacity = 0x7f060151
com.iapp.leochen.apkinjector:attr/passwordToggleTintMode = 0x7f03037a
com.iapp.leochen.apkinjector:style/Base.V14.Theme.MaterialComponents.Bridge = 0x7f110093
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_light_on_primary = 0x7f0501a7
com.iapp.leochen.apkinjector:animator/m3_elevated_chip_state_list_anim = 0x7f02000f
com.iapp.leochen.apkinjector:attr/checkedIconMargin = 0x7f0300b8
com.iapp.leochen.apkinjector:dimen/mtrl_chip_pressed_translation_z = 0x7f0602a1
com.iapp.leochen.apkinjector:color/m3_sys_color_dark_surface_container_highest = 0x7f050175
com.iapp.leochen.apkinjector:styleable/ForegroundLinearLayout = 0x7f120038
com.iapp.leochen.apkinjector:style/Base.AlertDialog.AppCompat.Light = 0x7f11000c
com.iapp.leochen.apkinjector:id/progress_circular = 0x7f08017e
com.iapp.leochen.apkinjector:dimen/abc_dropdownitem_text_padding_left = 0x7f06002a
com.iapp.leochen.apkinjector:attr/closeIcon = 0x7f0300df
com.iapp.leochen.apkinjector:attr/carousel_emptyViewsBehavior = 0x7f0300a4
com.iapp.leochen.apkinjector:anim/btn_radio_to_off_mtrl_ring_outer_path_animation = 0x7f010014
com.iapp.leochen.apkinjector:attr/topInsetScrimEnabled = 0x7f0304af
com.iapp.leochen.apkinjector:attr/searchPrefixText = 0x7f0303b4
com.iapp.leochen.apkinjector:attr/activityChooserViewStyle = 0x7f030027
com.iapp.leochen.apkinjector:attr/thumbRadius = 0x7f030483
com.iapp.leochen.apkinjector:dimen/m3_comp_filter_chip_flat_unselected_outline_width = 0x7f06012e
com.iapp.leochen.apkinjector:attr/colorOnSurface = 0x7f03010a
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.MaterialCalendar.HeaderDivider = 0x7f11042b
com.iapp.leochen.apkinjector:attr/tabTextAppearance = 0x7f030432
com.iapp.leochen.apkinjector:dimen/m3_btn_icon_btn_padding_right = 0x7f0600d2
com.iapp.leochen.apkinjector:attr/materialCalendarTheme = 0x7f0302f3
com.iapp.leochen.apkinjector:attr/checkedIconEnabled = 0x7f0300b6
com.iapp.leochen.apkinjector:color/tooltip_background_light = 0x7f050305
com.iapp.leochen.apkinjector:id/snapMargins = 0x7f0801af
com.iapp.leochen.apkinjector:attr/carousel_touchUpMode = 0x7f0300aa
com.iapp.leochen.apkinjector:integer/m3_sys_shape_corner_extra_small_corner_family = 0x7f090021
com.iapp.leochen.apkinjector:attr/chipSpacingHorizontal = 0x7f0300ca
com.iapp.leochen.apkinjector:color/m3_sys_color_light_surface_variant = 0x7f0501eb
com.iapp.leochen.apkinjector:attr/paddingTopNoTitle = 0x7f030371
com.iapp.leochen.apkinjector:style/Widget.Material3.NavigationRailView.ActiveIndicator = 0x7f1103be
com.iapp.leochen.apkinjector:attr/carousel_infinite = 0x7f0300a7
com.iapp.leochen.apkinjector:macro/m3_comp_navigation_drawer_active_hover_icon_color = 0x7f0c007b
com.iapp.leochen.apkinjector:attr/carousel_backwardTransition = 0x7f0300a3
com.iapp.leochen.apkinjector:id/main = 0x7f080108
com.iapp.leochen.apkinjector:attr/color = 0x7f0300f2
com.iapp.leochen.apkinjector:id/accelerate = 0x7f08000f
com.iapp.leochen.apkinjector:attr/shapeAppearanceCornerExtraLarge = 0x7f0303bd
com.iapp.leochen.apkinjector:color/material_dynamic_color_light_on_error_container = 0x7f050221
com.iapp.leochen.apkinjector:attr/colorOnTertiaryContainer = 0x7f03010e
com.iapp.leochen.apkinjector:style/Base.V21.Theme.AppCompat.Light = 0x7f1100a4
com.iapp.leochen.apkinjector:attr/actionModeSplitBackground = 0x7f03001d
com.iapp.leochen.apkinjector:color/mtrl_navigation_bar_colored_ripple_color = 0x7f0502d3
com.iapp.leochen.apkinjector:attr/cardCornerRadius = 0x7f03009b
com.iapp.leochen.apkinjector:attr/actionBarTabTextStyle = 0x7f03000a
com.iapp.leochen.apkinjector:color/abc_search_url_text_selected = 0x7f050010
com.iapp.leochen.apkinjector:string/abc_menu_meta_shortcut_label = 0x7f10000d
com.iapp.leochen.apkinjector:drawable/$avd_hide_password__2 = 0x7f070002
com.iapp.leochen.apkinjector:anim/abc_fade_in = 0x7f010000
com.iapp.leochen.apkinjector:color/mtrl_chip_close_icon_tint = 0x7f0502c4
com.iapp.leochen.apkinjector:interpolator/btn_radio_to_off_mtrl_animation_interpolator_0 = 0x7f0a0004
com.iapp.leochen.apkinjector:color/material_harmonized_color_on_error_container = 0x7f05026d
com.iapp.leochen.apkinjector:dimen/m3_comp_text_button_pressed_state_layer_opacity = 0x7f06019e
com.iapp.leochen.apkinjector:string/mtrl_picker_cancel = 0x7f100071
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_tertiary30 = 0x7f0500ec
com.iapp.leochen.apkinjector:attr/textInputLayoutFocusedRectEnabled = 0x7f03046b
com.iapp.leochen.apkinjector:attr/buttonBarPositiveButtonStyle = 0x7f03008d
com.iapp.leochen.apkinjector:attr/borderlessButtonStyle = 0x7f030077
com.iapp.leochen.apkinjector:style/Widget.AppCompat.Light.ActionBar.TabView.Inverse = 0x7f11030a
com.iapp.leochen.apkinjector:macro/m3_comp_time_picker_period_selector_selected_focus_state_layer_color = 0x7f0c0156
com.iapp.leochen.apkinjector:layout/m3_alert_dialog_title = 0x7f0b0036
com.iapp.leochen.apkinjector:color/m3_assist_chip_icon_tint_color = 0x7f050061
com.iapp.leochen.apkinjector:attr/actionModeCutDrawable = 0x7f030017
com.iapp.leochen.apkinjector:style/Widget.Material3.TextInputLayout.FilledBox = 0x7f1103de
com.iapp.leochen.apkinjector:attr/boxStrokeWidth = 0x7f030087
com.iapp.leochen.apkinjector:dimen/design_snackbar_padding_horizontal = 0x7f060085
com.iapp.leochen.apkinjector:style/Widget.Material3.Chip.Filter.Elevated = 0x7f11036b
com.iapp.leochen.apkinjector:attr/textAppearanceTitleLarge = 0x7f03045c
com.iapp.leochen.apkinjector:style/Theme.AppCompat.Dialog = 0x7f110216
com.iapp.leochen.apkinjector:dimen/m3_fab_border_width = 0x7f0601b5
com.iapp.leochen.apkinjector:animator/m3_card_elevated_state_list_anim = 0x7f02000c
com.iapp.leochen.apkinjector:drawable/abc_ic_menu_copy_mtrl_am_alpha = 0x7f070043
com.iapp.leochen.apkinjector:attr/fabCradleMargin = 0x7f0301ce
com.iapp.leochen.apkinjector:anim/linear_indeterminate_line2_tail_interpolator = 0x7f010020
com.iapp.leochen.apkinjector:attr/circularflow_angles = 0x7f0300d4
com.iapp.leochen.apkinjector:attr/iconTintMode = 0x7f030237
com.iapp.leochen.apkinjector:macro/m3_comp_checkbox_selected_icon_color = 0x7f0c000b
com.iapp.leochen.apkinjector:dimen/mtrl_extended_fab_translation_z_pressed = 0x7f0602b5
com.iapp.leochen.apkinjector:attr/clickAction = 0x7f0300da
com.iapp.leochen.apkinjector:animator/m3_extended_fab_show_motion_spec = 0x7f020013
com.iapp.leochen.apkinjector:mipmap/ic_launcher_round = 0x7f0e0001
com.iapp.leochen.apkinjector:attr/boxBackgroundColor = 0x7f03007e
com.iapp.leochen.apkinjector:macro/m3_comp_time_picker_headline_color = 0x7f0c0150
com.iapp.leochen.apkinjector:attr/background = 0x7f030047
com.iapp.leochen.apkinjector:style/Base.Widget.Design.TabLayout = 0x7f1100fd
com.iapp.leochen.apkinjector:attr/bottomSheetStyle = 0x7f03007d
com.iapp.leochen.apkinjector:attr/endIconContentDescription = 0x7f0301a3
com.iapp.leochen.apkinjector:attr/actionBarPopupTheme = 0x7f030004
com.iapp.leochen.apkinjector:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Large = 0x7f11003f
com.iapp.leochen.apkinjector:attr/bottomAppBarStyle = 0x7f030078
com.iapp.leochen.apkinjector:animator/mtrl_extended_fab_show_motion_spec = 0x7f02001c
com.iapp.leochen.apkinjector:style/Widget.Material3.Toolbar.Surface = 0x7f1103e9
com.iapp.leochen.apkinjector:style/Base.ThemeOverlay.MaterialComponents.Light.Dialog.Alert.Framework = 0x7f110088
com.iapp.leochen.apkinjector:attr/percentWidth = 0x7f03037e
com.iapp.leochen.apkinjector:attr/autoAdjustToWithinGrandparentBounds = 0x7f03003c
com.iapp.leochen.apkinjector:macro/m3_comp_assist_chip_label_text_type = 0x7f0c0001
com.iapp.leochen.apkinjector:attr/customIntegerValue = 0x7f030167
com.iapp.leochen.apkinjector:attr/endIconCheckable = 0x7f0301a2
com.iapp.leochen.apkinjector:dimen/m3_carousel_extra_small_item_size = 0x7f0600ee
com.iapp.leochen.apkinjector:attr/colorSurfaceContainer = 0x7f030122
com.iapp.leochen.apkinjector:integer/mtrl_view_gone = 0x7f09003f
com.iapp.leochen.apkinjector:id/transition_image_transform = 0x7f0801f7
com.iapp.leochen.apkinjector:id/line3 = 0x7f080102
com.iapp.leochen.apkinjector:id/SHOW_ALL = 0x7f080008
com.iapp.leochen.apkinjector:macro/m3_comp_filled_text_field_container_color = 0x7f0c004b
com.iapp.leochen.apkinjector:attr/grid_rows = 0x7f030213
com.iapp.leochen.apkinjector:attr/radioButtonStyle = 0x7f030399
com.iapp.leochen.apkinjector:attr/font = 0x7f0301fb
com.iapp.leochen.apkinjector:color/m3_ref_palette_neutral87 = 0x7f050112
com.iapp.leochen.apkinjector:style/Widget.Material3.CompoundButton.RadioButton = 0x7f110380
com.iapp.leochen.apkinjector:style/Base.Widget.AppCompat.ActionBar.Solid = 0x7f1100c4
com.iapp.leochen.apkinjector:attr/borderRoundPercent = 0x7f030075
com.iapp.leochen.apkinjector:color/m3_chip_stroke_color = 0x7f050073
com.iapp.leochen.apkinjector:dimen/design_bottom_navigation_item_min_width = 0x7f060066
com.iapp.leochen.apkinjector:anim/linear_indeterminate_line1_head_interpolator = 0x7f01001d
com.iapp.leochen.apkinjector:style/TextAppearance.AppCompat.Widget.ActionBar.Title = 0x7f1101b4
com.iapp.leochen.apkinjector:attr/animateRelativeTo = 0x7f030034
com.iapp.leochen.apkinjector:layout/select_dialog_singlechoice_material = 0x7f0b006f
com.iapp.leochen.apkinjector:dimen/m3_btn_stroke_size = 0x7f0600dd
com.iapp.leochen.apkinjector:attr/behavior_skipCollapsed = 0x7f030072
com.iapp.leochen.apkinjector:anim/slide_parallel_left = 0x7f010034
com.iapp.leochen.apkinjector:dimen/m3_comp_extended_fab_primary_hover_container_elevation = 0x7f060110
com.iapp.leochen.apkinjector:attr/shapeAppearanceSmallComponent = 0x7f0303c5
com.iapp.leochen.apkinjector:dimen/m3_navigation_menu_divider_horizontal_padding = 0x7f0601c7
com.iapp.leochen.apkinjector:animator/m3_extended_fab_change_size_expand_motion_spec = 0x7f020011
com.iapp.leochen.apkinjector:color/m3_ref_palette_white = 0x7f05014e
com.iapp.leochen.apkinjector:attr/boxCollapsedPaddingTop = 0x7f030080
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_secondary70 = 0x7f0500e3
com.iapp.leochen.apkinjector:dimen/tooltip_precise_anchor_extra_offset = 0x7f06031e
com.iapp.leochen.apkinjector:attr/badgeWithTextHeight = 0x7f03005e
com.iapp.leochen.apkinjector:style/TextAppearance.AppCompat.Widget.Button.Inverse = 0x7f1101bd
com.iapp.leochen.apkinjector:style/RtlOverlay.DialogWindowTitle.AppCompat = 0x7f110145
com.iapp.leochen.apkinjector:attr/actionModePopupWindowStyle = 0x7f03001a
com.iapp.leochen.apkinjector:attr/saturation = 0x7f0303ad
com.iapp.leochen.apkinjector:style/Theme.Material3.Dark.Dialog.MinWidth = 0x7f11022d
com.iapp.leochen.apkinjector:attr/actionLayout = 0x7f03000f
com.iapp.leochen.apkinjector:drawable/design_snackbar_background = 0x7f070086
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_primary70 = 0x7f0500d6
com.iapp.leochen.apkinjector:attr/badgeShapeAppearanceOverlay = 0x7f030056
com.iapp.leochen.apkinjector:animator/mtrl_fab_hide_motion_spec = 0x7f02001e
com.iapp.leochen.apkinjector:drawable/mtrl_checkbox_button_icon_indeterminate_checked = 0x7f0700c0
com.iapp.leochen.apkinjector:attr/colorControlActivated = 0x7f0300f7
com.iapp.leochen.apkinjector:attr/barrierMargin = 0x7f030066
com.iapp.leochen.apkinjector:attr/checkedState = 0x7f0300bc
com.iapp.leochen.apkinjector:color/primary_text_disabled_material_dark = 0x7f0502f6
com.iapp.leochen.apkinjector:id/dragUp = 0x7f0800ae
com.iapp.leochen.apkinjector:drawable/notification_action_background = 0x7f0700e0
com.iapp.leochen.apkinjector:color/m3_button_ripple_color_selector = 0x7f050068
com.iapp.leochen.apkinjector:attr/backHandlingEnabled = 0x7f030046
com.iapp.leochen.apkinjector:style/Widget.Material3.MaterialCalendar.HeaderLayout = 0x7f1103a5
com.iapp.leochen.apkinjector:attr/scrimVisibleHeightTrigger = 0x7f0303b1
com.iapp.leochen.apkinjector:attr/overlapAnchor = 0x7f030368
com.iapp.leochen.apkinjector:dimen/mtrl_navigation_rail_compact_width = 0x7f0602cc
com.iapp.leochen.apkinjector:style/Base.V22.Theme.AppCompat = 0x7f1100ae
com.iapp.leochen.apkinjector:dimen/m3_searchbar_padding_start = 0x7f0601df
com.iapp.leochen.apkinjector:styleable/NavigationView = 0x7f12006d
com.iapp.leochen.apkinjector:attr/collapsingToolbarLayoutMediumSize = 0x7f0300ef
com.iapp.leochen.apkinjector:animator/mtrl_extended_fab_change_size_collapse_motion_spec = 0x7f020019
com.iapp.leochen.apkinjector:attr/autoShowKeyboard = 0x7f03003f
com.iapp.leochen.apkinjector:attr/layout_editor_absoluteX = 0x7f0302ae
com.iapp.leochen.apkinjector:attr/tabIndicatorAnimationDuration = 0x7f03041e
com.iapp.leochen.apkinjector:dimen/material_filled_edittext_font_2_0_padding_bottom = 0x7f060239
com.iapp.leochen.apkinjector:anim/mtrl_bottom_sheet_slide_in = 0x7f010029
com.iapp.leochen.apkinjector:macro/m3_comp_search_bar_hover_supporting_text_color = 0x7f0c00e8
com.iapp.leochen.apkinjector:attr/layout_constraintRight_toLeftOf = 0x7f03029d
com.iapp.leochen.apkinjector:string/mtrl_picker_out_of_range = 0x7f10007e
com.iapp.leochen.apkinjector:dimen/m3_comp_navigation_drawer_modal_container_elevation = 0x7f060142
com.iapp.leochen.apkinjector:style/Base.Widget.AppCompat.Button.ButtonBar.AlertDialog = 0x7f1100d1
com.iapp.leochen.apkinjector:attr/expandedTitleMargin = 0x7f0301bb
com.iapp.leochen.apkinjector:attr/attributeName = 0x7f03003b
com.iapp.leochen.apkinjector:anim/slide_in_left_smooth = 0x7f01002d
com.iapp.leochen.apkinjector:attr/materialTimePickerTheme = 0x7f03030a
com.iapp.leochen.apkinjector:color/black = 0x7f050021
com.iapp.leochen.apkinjector:dimen/m3_sys_motion_easing_standard_accelerate_control_y1 = 0x7f060210
com.iapp.leochen.apkinjector:attr/arrowShaftLength = 0x7f03003a
com.iapp.leochen.apkinjector:animator/design_fab_show_motion_spec = 0x7f020002
com.iapp.leochen.apkinjector:attr/arcMode = 0x7f030038
com.iapp.leochen.apkinjector:dimen/m3_navigation_item_shape_inset_top = 0x7f0601c5
com.iapp.leochen.apkinjector:color/material_personalized_color_surface_container_lowest = 0x7f05029d
com.iapp.leochen.apkinjector:dimen/design_fab_border_width = 0x7f06006e
com.iapp.leochen.apkinjector:id/accessibility_action_clickable_span = 0x7f080010
com.iapp.leochen.apkinjector:color/m3_dynamic_primary_text_disable_only = 0x7f050085
com.iapp.leochen.apkinjector:attr/textInputOutlinedDenseStyle = 0x7f03046c
com.iapp.leochen.apkinjector:dimen/m3_sys_motion_easing_emphasized_decelerate_control_y1 = 0x7f0601fc
com.iapp.leochen.apkinjector:attr/customColorDrawableValue = 0x7f030163
com.iapp.leochen.apkinjector:attr/motionEffect_start = 0x7f030346
com.iapp.leochen.apkinjector:layout/abc_action_bar_title_item = 0x7f0b0000
com.iapp.leochen.apkinjector:dimen/m3_comp_time_picker_time_selector_pressed_state_layer_opacity = 0x7f0601a7
com.iapp.leochen.apkinjector:attr/textAppearancePopupMenuHeader = 0x7f030456
com.iapp.leochen.apkinjector:attr/behavior_peekHeight = 0x7f03006f
com.iapp.leochen.apkinjector:attr/boxCornerRadiusBottomEnd = 0x7f030081
com.iapp.leochen.apkinjector:styleable/Layout = 0x7f120048
com.iapp.leochen.apkinjector:drawable/material_ic_keyboard_arrow_left_black_24dp = 0x7f0700b4
com.iapp.leochen.apkinjector:layout/material_time_chip = 0x7f0b0042
com.iapp.leochen.apkinjector:attr/backgroundInsetEnd = 0x7f03004a
com.iapp.leochen.apkinjector:id/accessibility_custom_action_18 = 0x7f08001b
com.iapp.leochen.apkinjector:attr/colorOnPrimaryContainer = 0x7f030102
com.iapp.leochen.apkinjector:attr/motionDurationShort1 = 0x7f030333
com.iapp.leochen.apkinjector:color/m3_sys_color_secondary_fixed_dim = 0x7f0501f7
com.iapp.leochen.apkinjector:attr/badgeShapeAppearance = 0x7f030055
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.MaterialCalendar.HeaderTitle = 0x7f110430
com.iapp.leochen.apkinjector:attr/itemTextColor = 0x7f03026a
com.iapp.leochen.apkinjector:dimen/mtrl_toolbar_default_height = 0x7f060305
com.iapp.leochen.apkinjector:attr/motionEffect_move = 0x7f030345
com.iapp.leochen.apkinjector:styleable/ClockFaceView = 0x7f120020
com.iapp.leochen.apkinjector:attr/customColorValue = 0x7f030164
com.iapp.leochen.apkinjector:dimen/mtrl_navigation_rail_icon_size = 0x7f0602d0
com.iapp.leochen.apkinjector:style/TextAppearance.MaterialComponents.TimePicker.Title = 0x7f110207
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_on_primary_fixed_variant = 0x7f0501c1
com.iapp.leochen.apkinjector:style/Base.V21.ThemeOverlay.Material3.BottomSheetDialog = 0x7f1100ab
com.iapp.leochen.apkinjector:attr/thumbHeight = 0x7f03047e
com.iapp.leochen.apkinjector:dimen/abc_action_button_min_height_material = 0x7f06000d
com.iapp.leochen.apkinjector:attr/colorOnError = 0x7f0300ff
com.iapp.leochen.apkinjector:color/mtrl_textinput_focused_box_stroke_color = 0x7f0502ec
com.iapp.leochen.apkinjector:color/dim_foreground_disabled_material_dark = 0x7f050056
com.iapp.leochen.apkinjector:style/TextAppearance.Material3.BodyMedium = 0x7f1101e6
com.iapp.leochen.apkinjector:attr/textAppearanceHeadlineLarge = 0x7f03044a
com.iapp.leochen.apkinjector:string/mtrl_picker_toggle_to_text_input_mode = 0x7f10008f
com.iapp.leochen.apkinjector:attr/contentPaddingLeft = 0x7f030143
com.iapp.leochen.apkinjector:attr/setsTag = 0x7f0303bb
com.iapp.leochen.apkinjector:attr/fontProviderPackage = 0x7f030201
com.iapp.leochen.apkinjector:dimen/design_tab_max_width = 0x7f060089
com.iapp.leochen.apkinjector:drawable/abc_btn_check_material = 0x7f07002c
com.iapp.leochen.apkinjector:string/m3_sys_motion_easing_standard_accelerate = 0x7f100045
com.iapp.leochen.apkinjector:id/showCustom = 0x7f0801a5
com.iapp.leochen.apkinjector:attr/textAppearanceBodyMedium = 0x7f03043d
com.iapp.leochen.apkinjector:attr/allowStacking = 0x7f03002d
com.iapp.leochen.apkinjector:id/titleDividerNoCustom = 0x7f0801eb
com.iapp.leochen.apkinjector:attr/cardElevation = 0x7f03009c
com.iapp.leochen.apkinjector:style/Theme.MaterialComponents.Dialog = 0x7f11025b
com.iapp.leochen.apkinjector:animator/mtrl_extended_fab_state_list_animator = 0x7f02001d
com.iapp.leochen.apkinjector:attr/titleMargins = 0x7f0304a0
com.iapp.leochen.apkinjector:id/bestChoice = 0x7f080061
com.iapp.leochen.apkinjector:dimen/m3_navigation_item_active_indicator_label_padding = 0x7f0601bf
com.iapp.leochen.apkinjector:anim/abc_slide_in_bottom = 0x7f010006
com.iapp.leochen.apkinjector:color/m3_efab_ripple_color_selector = 0x7f050086
com.iapp.leochen.apkinjector:drawable/abc_ic_search_api_material = 0x7f070049
com.iapp.leochen.apkinjector:layout/design_navigation_menu = 0x7f0b0029
com.iapp.leochen.apkinjector:attr/tintMode = 0x7f030495
com.iapp.leochen.apkinjector:color/switch_thumb_normal_material_dark = 0x7f050302
com.iapp.leochen.apkinjector:attr/badgeStyle = 0x7f030057
com.iapp.leochen.apkinjector:string/exposed_dropdown_menu_content_description = 0x7f100031
com.iapp.leochen.apkinjector:dimen/mtrl_slider_label_radius = 0x7f0602e7
com.iapp.leochen.apkinjector:macro/m3_comp_extended_fab_primary_icon_color = 0x7f0c002e
com.iapp.leochen.apkinjector:attr/animateNavigationIcon = 0x7f030033
com.iapp.leochen.apkinjector:attr/titleTextColor = 0x7f0304a3
com.iapp.leochen.apkinjector:color/m3_ref_palette_neutral80 = 0x7f050111
com.iapp.leochen.apkinjector:attr/actionModePasteDrawable = 0x7f030019
com.iapp.leochen.apkinjector:animator/fragment_fade_exit = 0x7f020006
com.iapp.leochen.apkinjector:dimen/m3_sys_elevation_level4 = 0x7f0601f4
com.iapp.leochen.apkinjector:attr/autoSizeStepGranularity = 0x7f030043
com.iapp.leochen.apkinjector:style/Widget.Material3.MaterialCalendar.Year.Today = 0x7f1103b0
com.iapp.leochen.apkinjector:style/MaterialAlertDialog.Material3.Body.Text.CenterStacked = 0x7f110126
com.iapp.leochen.apkinjector:attr/editTextBackground = 0x7f030199
com.iapp.leochen.apkinjector:attr/backgroundSplit = 0x7f03004e
com.iapp.leochen.apkinjector:drawable/ic_call_answer = 0x7f07008b
com.iapp.leochen.apkinjector:id/percent = 0x7f080177
com.iapp.leochen.apkinjector:attr/imageRotate = 0x7f03023e
com.iapp.leochen.apkinjector:attr/actionBarTabBarStyle = 0x7f030008
com.iapp.leochen.apkinjector:dimen/mtrl_bottomappbar_fabOffsetEndMode = 0x7f060253
com.iapp.leochen.apkinjector:attr/subtitleCentered = 0x7f03040b
com.iapp.leochen.apkinjector:attr/springStiffness = 0x7f0303e9
com.iapp.leochen.apkinjector:id/animateToStart = 0x7f08004e
com.iapp.leochen.apkinjector:attr/nestedScrollViewStyle = 0x7f03035c
com.iapp.leochen.apkinjector:style/Widget.Material3.MaterialCalendar.DayOfWeekLabel = 0x7f1103a0
com.iapp.leochen.apkinjector:style/ShapeAppearance.M3.Comp.NavigationRail.Container.Shape = 0x7f11015f
com.iapp.leochen.apkinjector:attr/extendedFloatingActionButtonPrimaryStyle = 0x7f0301c4
com.iapp.leochen.apkinjector:id/accessibility_custom_action_29 = 0x7f080027
com.iapp.leochen.apkinjector:attr/itemMaxLines = 0x7f030256
com.iapp.leochen.apkinjector:dimen/design_fab_image_size = 0x7f060070
com.iapp.leochen.apkinjector:style/Base.TextAppearance.AppCompat.Small = 0x7f11002b
com.iapp.leochen.apkinjector:anim/abc_tooltip_enter = 0x7f01000a
com.iapp.leochen.apkinjector:dimen/mtrl_btn_dialog_btn_min_width = 0x7f06025a
com.iapp.leochen.apkinjector:macro/m3_comp_checkbox_selected_error_icon_color = 0x7f0c000a
com.iapp.leochen.apkinjector:animator/fragment_open_enter = 0x7f020007
com.iapp.leochen.apkinjector:macro/m3_comp_outlined_card_disabled_outline_color = 0x7f0c00aa
com.iapp.leochen.apkinjector:attr/state_lifted = 0x7f0303fd
com.iapp.leochen.apkinjector:dimen/abc_dialog_corner_radius_material = 0x7f06001b
com.iapp.leochen.apkinjector:animator/mtrl_extended_fab_hide_motion_spec = 0x7f02001b
com.iapp.leochen.apkinjector:style/Base.Theme.Material3.Dark.BottomSheetDialog = 0x7f11005b
com.iapp.leochen.apkinjector:animator/mtrl_extended_fab_change_size_expand_motion_spec = 0x7f02001a
com.iapp.leochen.apkinjector:color/m3_timepicker_button_ripple_color = 0x7f050209
com.iapp.leochen.apkinjector:styleable/ActionBarLayout = 0x7f120001
com.iapp.leochen.apkinjector:id/action_bar = 0x7f080034
com.iapp.leochen.apkinjector:attr/borderRound = 0x7f030074
com.iapp.leochen.apkinjector:macro/m3_comp_top_app_bar_small_leading_icon_color = 0x7f0c0172
com.iapp.leochen.apkinjector:attr/autoCompleteTextViewStyle = 0x7f03003e
com.iapp.leochen.apkinjector:dimen/m3_sys_motion_easing_legacy_control_y1 = 0x7f060204
com.iapp.leochen.apkinjector:animator/m3_extended_fab_change_size_collapse_motion_spec = 0x7f020010
com.iapp.leochen.apkinjector:macro/m3_comp_switch_unselected_focus_handle_color = 0x7f0c012f
com.iapp.leochen.apkinjector:attr/actionModeWebSearchDrawable = 0x7f030020
com.iapp.leochen.apkinjector:dimen/material_clock_face_margin_top = 0x7f060226
com.iapp.leochen.apkinjector:attr/trackColorActive = 0x7f0304b5
com.iapp.leochen.apkinjector:macro/m3_comp_switch_unselected_pressed_state_layer_color = 0x7f0c013d
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_secondary0 = 0x7f0500db
com.iapp.leochen.apkinjector:animator/m3_btn_state_list_anim = 0x7f02000b
com.iapp.leochen.apkinjector:attr/badgeTextAppearance = 0x7f030059
com.iapp.leochen.apkinjector:macro/m3_comp_filled_button_label_text_color = 0x7f0c0044
com.iapp.leochen.apkinjector:dimen/m3_card_hovered_z = 0x7f0600eb
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.TextInputEditText.OutlinedBox = 0x7f11044f
com.iapp.leochen.apkinjector:color/bright_foreground_disabled_material_dark = 0x7f050022
com.iapp.leochen.apkinjector:style/Widget.MaterialComponents.AutoCompleteTextView.FilledBox = 0x7f1103f3
com.iapp.leochen.apkinjector:attr/constraint_referenced_tags = 0x7f030136
com.iapp.leochen.apkinjector:style/Widget.Material3.Chip.Filter = 0x7f11036a
com.iapp.leochen.apkinjector:animator/fragment_fade_enter = 0x7f020005
com.iapp.leochen.apkinjector:drawable/abc_btn_colored_material = 0x7f070030
com.iapp.leochen.apkinjector:attr/actionModeShareDrawable = 0x7f03001c
com.iapp.leochen.apkinjector:color/abc_tint_btn_checkable = 0x7f050013
com.iapp.leochen.apkinjector:dimen/m3_comp_fab_primary_pressed_state_layer_opacity = 0x7f06011e
com.iapp.leochen.apkinjector:attr/colorSurfaceContainerLow = 0x7f030125
com.iapp.leochen.apkinjector:color/m3_dynamic_dark_default_color_primary_text = 0x7f05007c
com.iapp.leochen.apkinjector:dimen/m3_comp_filled_card_container_elevation = 0x7f060124
com.iapp.leochen.apkinjector:anim/slide_in_right = 0x7f01002e
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_secondary40 = 0x7f0500e0
com.iapp.leochen.apkinjector:color/m3_ref_palette_dynamic_neutral50 = 0x7f0500a9
com.iapp.leochen.apkinjector:color/m3_navigation_item_ripple_color = 0x7f050094
com.iapp.leochen.apkinjector:id/report_drawn = 0x7f080183
com.iapp.leochen.apkinjector:anim/m3_side_sheet_enter_from_right = 0x7f010026
com.iapp.leochen.apkinjector:id/accessibility_custom_action_10 = 0x7f080013
com.iapp.leochen.apkinjector:color/m3_dark_default_color_secondary_text = 0x7f050076
com.iapp.leochen.apkinjector:attr/actionBarStyle = 0x7f030007
com.iapp.leochen.apkinjector:macro/m3_comp_primary_navigation_tab_with_icon_inactive_icon_color = 0x7f0c00d0
com.iapp.leochen.apkinjector:dimen/material_clock_display_padding = 0x7f060223
com.iapp.leochen.apkinjector:dimen/notification_small_icon_background_padding = 0x7f060316
com.iapp.leochen.apkinjector:anim/btn_checkbox_to_unchecked_box_inner_merged_animation = 0x7f01000f
com.iapp.leochen.apkinjector:attr/colorOnSurfaceInverse = 0x7f03010b
com.iapp.leochen.apkinjector:color/material_personalized_color_text_secondary_and_tertiary_inverse = 0x7f0502a6
com.iapp.leochen.apkinjector:attr/dividerColor = 0x7f03017d
com.iapp.leochen.apkinjector:color/m3_ref_palette_error95 = 0x7f050100
com.iapp.leochen.apkinjector:macro/m3_comp_navigation_rail_active_indicator_color = 0x7f0c0096
com.iapp.leochen.apkinjector:color/abc_tint_seek_thumb = 0x7f050016
com.iapp.leochen.apkinjector:id/accessibility_custom_action_1 = 0x7f080012
com.iapp.leochen.apkinjector:style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Secondary = 0x7f1102a0
com.iapp.leochen.apkinjector:attr/progressBarPadding = 0x7f030391
com.iapp.leochen.apkinjector:color/m3_ref_palette_neutral_variant99 = 0x7f050126
com.iapp.leochen.apkinjector:anim/abc_slide_in_top = 0x7f010007
com.iapp.leochen.apkinjector:macro/m3_comp_switch_unselected_hover_handle_color = 0x7f0c0135
com.iapp.leochen.apkinjector:attr/buttonIconTint = 0x7f030093
com.iapp.leochen.apkinjector:style/Widget.Material3.ChipGroup = 0x7f110372
com.iapp.leochen.apkinjector:attr/textAppearanceDisplaySmall = 0x7f030443
com.iapp.leochen.apkinjector:drawable/$mtrl_checkbox_button_icon_indeterminate_checked__0 = 0x7f070014
com.iapp.leochen.apkinjector:color/abc_hint_foreground_material_light = 0x7f050008
com.iapp.leochen.apkinjector:color/m3_sys_color_dark_surface_bright = 0x7f050172
com.iapp.leochen.apkinjector:style/Widget.AppCompat.ListView = 0x7f11031a
com.iapp.leochen.apkinjector:color/m3_ref_palette_neutral30 = 0x7f05010a
com.iapp.leochen.apkinjector:id/sawtooth = 0x7f08018d
com.iapp.leochen.apkinjector:id/position = 0x7f08017b
com.iapp.leochen.apkinjector:attr/materialCalendarFullscreenTheme = 0x7f0302e8
com.iapp.leochen.apkinjector:color/mtrl_fab_bg_color_selector = 0x7f0502cb
com.iapp.leochen.apkinjector:style/Base.Widget.AppCompat.Button.Borderless.Colored = 0x7f1100d0
com.iapp.leochen.apkinjector:attr/ifTagSet = 0x7f03023a
com.iapp.leochen.apkinjector:attr/tabRippleColor = 0x7f03042d
com.iapp.leochen.apkinjector:anim/btn_checkbox_to_checked_box_outer_merged_animation = 0x7f01000d
com.iapp.leochen.apkinjector:attr/defaultDuration = 0x7f030170
com.iapp.leochen.apkinjector:attr/carousel_touchUp_velocityThreshold = 0x7f0300ac
com.iapp.leochen.apkinjector:color/m3_sys_color_dynamic_secondary_fixed_dim = 0x7f0501c9
com.iapp.leochen.apkinjector:style/Theme.MaterialComponents.CompactMenu = 0x7f11024a
com.iapp.leochen.apkinjector:attr/floatingActionButtonSecondaryStyle = 0x7f0301df
com.iapp.leochen.apkinjector:anim/m3_side_sheet_exit_to_right = 0x7f010028
com.iapp.leochen.apkinjector:style/ShapeAppearanceOverlay.Material3.FloatingActionButton = 0x7f110187
com.iapp.leochen.apkinjector:anim/linear_indeterminate_line2_head_interpolator = 0x7f01001f
com.iapp.leochen.apkinjector:dimen/m3_btn_text_btn_icon_padding_right = 0x7f0600df
com.iapp.leochen.apkinjector:styleable/MaterialButtonToggleGroup = 0x7f120051
com.iapp.leochen.apkinjector:color/design_default_color_on_surface = 0x7f050045
com.iapp.leochen.apkinjector:drawable/abc_ic_menu_cut_mtrl_alpha = 0x7f070044
com.iapp.leochen.apkinjector:attr/trackDecoration = 0x7f0304b8
