package com.iapp.leochen.apkinjector;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;

public class FileAdapter extends RecyclerView.Adapter<FileAdapter.FileViewHolder> {
    private List<File> fileList;
    private OnFileClickListener onFileClickListener;
    private SimpleDateFormat dateFormat;

    public interface OnFileClickListener {
        void onFileClick(File file);
    }

    public FileAdapter(List<File> fileList, OnFileClickListener listener) {
        this.fileList = fileList;
        this.onFileClickListener = listener;
        this.dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault());
    }

    @NonNull
    @Override
    public FileViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_file, parent, false);
        return new FileViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull FileViewHolder holder, int position) {
        File file = fileList.get(position);

        // 优化文本设置，避免不必要的更新
        String fileName = file.getName();
        if (!fileName.equals(holder.fileName.getText().toString())) {
            holder.fileName.setText(fileName);
        }

        String fileDate = dateFormat.format(new Date(file.lastModified()));
        if (!fileDate.equals(holder.fileDate.getText().toString())) {
            holder.fileDate.setText(fileDate);
        }

        // 简化图标设置 - 只使用默认图标，避免重复设置
        if (file.isDirectory()) {
            if (holder.currentType != ViewType.DIRECTORY) {
                holder.fileIcon.setImageResource(R.drawable.ic_folder);
                holder.arrowIcon.setVisibility(View.VISIBLE);
                holder.currentType = ViewType.DIRECTORY;
            }
        } else {
            if (holder.currentType != ViewType.FILE) {
                holder.fileIcon.setImageResource(R.drawable.ic_file);
                holder.arrowIcon.setVisibility(View.GONE);
                holder.currentType = ViewType.FILE;
            }
        }

        // 设置点击事件
        holder.itemView.setOnClickListener(v -> {
            if (onFileClickListener != null) {
                onFileClickListener.onFileClick(file);
            }
        });
    }

    enum ViewType {
        DIRECTORY, FILE
    }

    @Override
    public int getItemCount() {
        return fileList.size();
    }

    static class FileViewHolder extends RecyclerView.ViewHolder {
        ImageView fileIcon;
        TextView fileName;
        TextView fileDate;
        ImageView arrowIcon;
        ViewType currentType = null; // 跟踪当前类型，避免重复设置

        public FileViewHolder(@NonNull View itemView) {
            super(itemView);
            fileIcon = itemView.findViewById(R.id.fileIcon);
            fileName = itemView.findViewById(R.id.fileName);
            fileDate = itemView.findViewById(R.id.fileDate);
            arrowIcon = itemView.findViewById(R.id.arrowIcon);
        }
    }
}
