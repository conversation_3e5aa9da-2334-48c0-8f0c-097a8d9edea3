package com.iapp.leochen.apkinjector;

import android.graphics.drawable.Drawable;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;

public class FileAdapter extends RecyclerView.Adapter<FileAdapter.FileViewHolder> {
    private List<File> fileList;
    private OnFileClickListener onFileClickListener;
    private SimpleDateFormat dateFormat;

    public interface OnFileClickListener {
        void onFileClick(File file);
    }

    public FileAdapter(List<File> fileList, OnFileClickListener listener) {
        this.fileList = fileList;
        this.onFileClickListener = listener;
        this.dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault());
    }

    @NonNull
    @Override
    public FileViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_file, parent, false);
        return new FileViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull FileViewHolder holder, int position) {
        File file = fileList.get(position);

        // 优化文本设置，避免不必要的更新
        String fileName = file.getName();
        if (!fileName.equals(holder.fileName.getText().toString())) {
            holder.fileName.setText(fileName);
        }

        String fileDate = dateFormat.format(new Date(file.lastModified()));
        if (!fileDate.equals(holder.fileDate.getText().toString())) {
            holder.fileDate.setText(fileDate);
        }

        // 设置图标 - 支持APK图标提取
        if (file.isDirectory()) {
            if (holder.currentType != ViewType.DIRECTORY) {
                holder.fileIcon.setImageResource(R.drawable.ic_folder);
                holder.arrowIcon.setVisibility(View.VISIBLE);
                holder.currentType = ViewType.DIRECTORY;
            }
        } else {
            // 检查是否为APK文件
            if (ApkIconExtractor.isApkFile(file.getName())) {
                if (holder.currentType != ViewType.APK) {
                    // 先设置默认APK图标
                    holder.fileIcon.setImageResource(R.drawable.ic_apk);
                    // 异步加载APK图标
                    loadApkIcon(holder, file);
                    holder.arrowIcon.setVisibility(View.GONE);
                    holder.currentType = ViewType.APK;
                }
            } else {
                if (holder.currentType != ViewType.FILE) {
                    holder.fileIcon.setImageResource(R.drawable.ic_file);
                    holder.arrowIcon.setVisibility(View.GONE);
                    holder.currentType = ViewType.FILE;
                }
            }
        }

        // 设置点击事件
        holder.itemView.setOnClickListener(v -> {
            if (onFileClickListener != null) {
                onFileClickListener.onFileClick(file);
            }
        });
    }

    enum ViewType {
        DIRECTORY, FILE, APK
    }

    /**
     * 异步加载APK图标
     * @param holder ViewHolder
     * @param file APK文件
     */
    private void loadApkIcon(FileViewHolder holder, File file) {
        Log.d("FileAdapter", "开始加载APK图标: " + file.getName());

        // 先设置默认APK图标，避免显示空白
        holder.fileIcon.setImageResource(R.drawable.ic_apk);

        // 使用线程池异步加载APK图标
        new Thread(() -> {
            try {
                Drawable drawable = ApkIconExtractor.getApkIcon(holder.itemView.getContext(), file.getAbsolutePath());

                // 切换回主线程更新UI
                new Handler(Looper.getMainLooper()).post(() -> {
                    try {
                        if (drawable != null) {
                            // 确保ViewHolder还是对应同一个文件（防止RecyclerView复用导致的错乱）
                            if (holder.getAdapterPosition() != RecyclerView.NO_POSITION) {
                                int position = holder.getAdapterPosition();
                                if (position < fileList.size() && fileList.get(position).equals(file)) {
                                    Log.d("FileAdapter", "成功设置APK图标: " + file.getName());
                                    holder.fileIcon.setImageDrawable(drawable);
                                } else {
                                    Log.d("FileAdapter", "ViewHolder位置已变化，跳过设置图标: " + file.getName());
                                }
                            }
                        } else {
                            Log.w("FileAdapter", "APK图标为空，保持默认图标: " + file.getName());
                        }
                    } catch (Exception e) {
                        Log.e("FileAdapter", "设置APK图标失败: " + file.getName(), e);
                    }
                });
            } catch (Exception e) {
                Log.e("FileAdapter", "加载APK图标异常: " + file.getName(), e);
            }
        }).start();
    }

    @Override
    public int getItemCount() {
        return fileList.size();
    }

    static class FileViewHolder extends RecyclerView.ViewHolder {
        ImageView fileIcon;
        TextView fileName;
        TextView fileDate;
        ImageView arrowIcon;
        ViewType currentType = null; // 跟踪当前类型，避免重复设置

        public FileViewHolder(@NonNull View itemView) {
            super(itemView);
            fileIcon = itemView.findViewById(R.id.fileIcon);
            fileName = itemView.findViewById(R.id.fileName);
            fileDate = itemView.findViewById(R.id.fileDate);
            arrowIcon = itemView.findViewById(R.id.arrowIcon);
        }
    }
}
