<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="G:\Android Studio-java\ApkInjector\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="G:\Android Studio-java\ApkInjector\app\src\main\res"><file name="slide_in_left" path="G:\Android Studio-java\ApkInjector\app\src\main\res\anim\slide_in_left.xml" qualifiers="" type="anim"/><file name="slide_in_left_smooth" path="G:\Android Studio-java\ApkInjector\app\src\main\res\anim\slide_in_left_smooth.xml" qualifiers="" type="anim"/><file name="slide_in_right" path="G:\Android Studio-java\ApkInjector\app\src\main\res\anim\slide_in_right.xml" qualifiers="" type="anim"/><file name="slide_in_right_smooth" path="G:\Android Studio-java\ApkInjector\app\src\main\res\anim\slide_in_right_smooth.xml" qualifiers="" type="anim"/><file name="slide_out_left" path="G:\Android Studio-java\ApkInjector\app\src\main\res\anim\slide_out_left.xml" qualifiers="" type="anim"/><file name="slide_out_left_smooth" path="G:\Android Studio-java\ApkInjector\app\src\main\res\anim\slide_out_left_smooth.xml" qualifiers="" type="anim"/><file name="slide_out_right" path="G:\Android Studio-java\ApkInjector\app\src\main\res\anim\slide_out_right.xml" qualifiers="" type="anim"/><file name="slide_out_right_smooth" path="G:\Android Studio-java\ApkInjector\app\src\main\res\anim\slide_out_right_smooth.xml" qualifiers="" type="anim"/><file name="slide_parallel_left" path="G:\Android Studio-java\ApkInjector\app\src\main\res\anim\slide_parallel_left.xml" qualifiers="" type="anim"/><file name="slide_parallel_right" path="G:\Android Studio-java\ApkInjector\app\src\main\res\anim\slide_parallel_right.xml" qualifiers="" type="anim"/><file name="no_elevation" path="G:\Android Studio-java\ApkInjector\app\src\main\res\animator\no_elevation.xml" qualifiers="" type="animator"/><file name="gradient_separator" path="G:\Android Studio-java\ApkInjector\app\src\main\res\drawable\gradient_separator.xml" qualifiers="" type="drawable"/><file name="ic_arrow_upward" path="G:\Android Studio-java\ApkInjector\app\src\main\res\drawable\ic_arrow_upward.xml" qualifiers="" type="drawable"/><file name="ic_bookmark_border" path="G:\Android Studio-java\ApkInjector\app\src\main\res\drawable\ic_bookmark_border.xml" qualifiers="" type="drawable"/><file name="ic_chevron_right" path="G:\Android Studio-java\ApkInjector\app\src\main\res\drawable\ic_chevron_right.xml" qualifiers="" type="drawable"/><file name="ic_file" path="G:\Android Studio-java\ApkInjector\app\src\main\res\drawable\ic_file.xml" qualifiers="" type="drawable"/><file name="ic_folder" path="G:\Android Studio-java\ApkInjector\app\src\main\res\drawable\ic_folder.xml" qualifiers="" type="drawable"/><file name="ic_home" path="G:\Android Studio-java\ApkInjector\app\src\main\res\drawable\ic_home.xml" qualifiers="" type="drawable"/><file name="ic_info" path="G:\Android Studio-java\ApkInjector\app\src\main\res\drawable\ic_info.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="G:\Android Studio-java\ApkInjector\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="G:\Android Studio-java\ApkInjector\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="activity_main" path="G:\Android Studio-java\ApkInjector\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="fragment_about" path="G:\Android Studio-java\ApkInjector\app\src\main\res\layout\fragment_about.xml" qualifiers="" type="layout"/><file name="fragment_home" path="G:\Android Studio-java\ApkInjector\app\src\main\res\layout\fragment_home.xml" qualifiers="" type="layout"/><file name="item_file" path="G:\Android Studio-java\ApkInjector\app\src\main\res\layout\item_file.xml" qualifiers="" type="layout"/><file name="layout_permission_request" path="G:\Android Studio-java\ApkInjector\app\src\main\res\layout\layout_permission_request.xml" qualifiers="" type="layout"/><file name="bottom_navigation_menu" path="G:\Android Studio-java\ApkInjector\app\src\main\res\menu\bottom_navigation_menu.xml" qualifiers="" type="menu"/><file name="ic_launcher" path="G:\Android Studio-java\ApkInjector\app\src\main\res\mipmap-anydpi\ic_launcher.xml" qualifiers="anydpi-v4" type="mipmap"/><file name="ic_launcher_round" path="G:\Android Studio-java\ApkInjector\app\src\main\res\mipmap-anydpi\ic_launcher_round.xml" qualifiers="anydpi-v4" type="mipmap"/><file name="ic_launcher" path="G:\Android Studio-java\ApkInjector\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="G:\Android Studio-java\ApkInjector\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="G:\Android Studio-java\ApkInjector\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="G:\Android Studio-java\ApkInjector\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="G:\Android Studio-java\ApkInjector\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="G:\Android Studio-java\ApkInjector\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="G:\Android Studio-java\ApkInjector\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="G:\Android Studio-java\ApkInjector\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="G:\Android Studio-java\ApkInjector\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="G:\Android Studio-java\ApkInjector\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="G:\Android Studio-java\ApkInjector\app\src\main\res\values\colors.xml" qualifiers=""><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color></file><file path="G:\Android Studio-java\ApkInjector\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">ApkInjectors </string></file><file path="G:\Android Studio-java\ApkInjector\app\src\main\res\values\themes.xml" qualifiers=""><style name="Base.Theme.ApkInjector" parent="Theme.Material3.DayNight.NoActionBar">
        
        
        <item name="appBarLayoutStyle">@style/Widget.Material3.AppBarLayout.NoElevation</item>
        <item name="toolbarStyle">@style/Widget.Material3.Toolbar.NoElevation</item>
    </style><style name="Widget.Material3.AppBarLayout.NoElevation" parent="Widget.Material3.AppBarLayout">
        <item name="elevation">0dp</item>
        <item name="android:elevation">0dp</item>
        <item name="android:stateListAnimator">@animator/no_elevation</item>
    </style><style name="Widget.Material3.Toolbar.NoElevation" parent="Widget.Material3.Toolbar">
        <item name="elevation">0dp</item>
        <item name="android:elevation">0dp</item>
    </style><style name="Theme.ApkInjector" parent="Base.Theme.ApkInjector"/></file><file path="G:\Android Studio-java\ApkInjector\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Base.Theme.ApkInjector" parent="Theme.Material3.DayNight.NoActionBar">
        
        
        <item name="appBarLayoutStyle">@style/Widget.Material3.AppBarLayout.NoElevation</item>
        <item name="toolbarStyle">@style/Widget.Material3.Toolbar.NoElevation</item>
    </style></file><file name="backup_rules" path="G:\Android Studio-java\ApkInjector\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="G:\Android Studio-java\ApkInjector\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="ic_navigation" path="G:\Android Studio-java\ApkInjector\app\src\main\res\drawable\ic_navigation.xml" qualifiers="" type="drawable"/><file name="dialog_path_navigation" path="G:\Android Studio-java\ApkInjector\app\src\main\res\layout\dialog_path_navigation.xml" qualifiers="" type="layout"/><file name="ic_apk" path="G:\Android Studio-java\ApkInjector\app\src\main\res\drawable\ic_apk.xml" qualifiers="" type="drawable"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="G:\Android Studio-java\ApkInjector\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="G:\Android Studio-java\ApkInjector\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="G:\Android Studio-java\ApkInjector\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="G:\Android Studio-java\ApkInjector\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>