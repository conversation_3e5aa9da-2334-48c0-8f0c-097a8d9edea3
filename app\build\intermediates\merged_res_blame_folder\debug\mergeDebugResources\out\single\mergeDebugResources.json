[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-debug-33:\\mipmap-anydpi_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-main-35:\\mipmap-anydpi\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-debug-33:\\mipmap-mdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-main-35:\\mipmap-mdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-debug-33:\\drawable_ic_arrow_upward.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-main-35:\\drawable\\ic_arrow_upward.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-debug-33:\\anim_slide_in_right.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-main-35:\\anim\\slide_in_right.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-debug-33:\\drawable_ic_bookmark_border.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-main-35:\\drawable\\ic_bookmark_border.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-debug-33:\\anim_slide_in_left.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-main-35:\\anim\\slide_in_left.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-debug-33:\\layout_fragment_about.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-main-35:\\layout\\fragment_about.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-debug-33:\\anim_slide_out_left_smooth.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-main-35:\\anim\\slide_out_left_smooth.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-debug-33:\\drawable_ic_folder.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-main-35:\\drawable\\ic_folder.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-debug-33:\\layout_activity_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-main-35:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-debug-33:\\animator_no_elevation.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-main-35:\\animator\\no_elevation.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-debug-33:\\layout_dialog_path_navigation.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-main-35:\\layout\\dialog_path_navigation.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-debug-33:\\mipmap-xxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-main-35:\\mipmap-xxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-debug-33:\\mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-main-35:\\mipmap-xxxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-debug-33:\\drawable_ic_home.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-main-35:\\drawable\\ic_home.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-debug-33:\\mipmap-hdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-main-35:\\mipmap-hdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-debug-33:\\drawable_ic_info.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-main-35:\\drawable\\ic_info.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-debug-33:\\drawable_ic_launcher_foreground.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-main-35:\\drawable\\ic_launcher_foreground.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-debug-33:\\anim_slide_out_left.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-main-35:\\anim\\slide_out_left.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-debug-33:\\mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-main-35:\\mipmap-xhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-debug-33:\\drawable_ic_launcher_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-main-35:\\drawable\\ic_launcher_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-debug-33:\\drawable_ic_navigation.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-main-35:\\drawable\\ic_navigation.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-debug-33:\\anim_slide_out_right_smooth.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-main-35:\\anim\\slide_out_right_smooth.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-debug-33:\\mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-main-35:\\mipmap-xxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-debug-33:\\mipmap-mdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-main-35:\\mipmap-mdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-debug-33:\\drawable_path_display_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-main-35:\\drawable\\path_display_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-debug-33:\\mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-main-35:\\mipmap-xxxhdpi\\ic_launcher.webp"}, {"merged": "com.iapp.leochen.apkinjector.app-debug-33:/drawable_ic_apk.xml.flat", "source": "com.iapp.leochen.apkinjector.app-main-35:/drawable/ic_apk.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-debug-33:\\xml_backup_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-main-35:\\xml\\backup_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-debug-33:\\anim_slide_in_left_smooth.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-main-35:\\anim\\slide_in_left_smooth.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-debug-33:\\menu_bottom_navigation_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-main-35:\\menu\\bottom_navigation_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-debug-33:\\anim_slide_parallel_left.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-main-35:\\anim\\slide_parallel_left.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-debug-33:\\anim_slide_parallel_right.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-main-35:\\anim\\slide_parallel_right.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-debug-33:\\mipmap-xhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-main-35:\\mipmap-xhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-debug-33:\\mipmap-anydpi_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-main-35:\\mipmap-anydpi\\ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-debug-33:\\layout_item_file.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-main-35:\\layout\\item_file.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-debug-33:\\anim_slide_in_right_smooth.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-main-35:\\anim\\slide_in_right_smooth.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-debug-33:\\xml_data_extraction_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-main-35:\\xml\\data_extraction_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-debug-33:\\anim_slide_out_right.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-main-35:\\anim\\slide_out_right.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-debug-33:\\layout_layout_permission_request.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-main-35:\\layout\\layout_permission_request.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-debug-33:\\drawable_gradient_separator.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-main-35:\\drawable\\gradient_separator.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-debug-33:\\layout_fragment_home.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-main-35:\\layout\\fragment_home.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-debug-33:\\drawable_ic_chevron_right.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-main-35:\\drawable\\ic_chevron_right.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-debug-33:\\drawable_ic_file.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-main-35:\\drawable\\ic_file.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-debug-33:\\mipmap-hdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.iapp.leochen.apkinjector.app-main-35:\\mipmap-hdpi\\ic_launcher.webp"}]