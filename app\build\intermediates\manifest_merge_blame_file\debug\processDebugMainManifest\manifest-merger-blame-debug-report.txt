1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.iapp.leochen.apkinjector"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="29"
9        android:targetSdkVersion="33" />
10
11    <!-- 存储权限 -->
12    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
12-->G:\Android Studio-java\ApkInjector\app\src\main\AndroidManifest.xml:6:5-80
12-->G:\Android Studio-java\ApkInjector\app\src\main\AndroidManifest.xml:6:22-77
13    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
13-->G:\Android Studio-java\ApkInjector\app\src\main\AndroidManifest.xml:7:5-81
13-->G:\Android Studio-java\ApkInjector\app\src\main\AndroidManifest.xml:7:22-78
14    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
14-->G:\Android Studio-java\ApkInjector\app\src\main\AndroidManifest.xml:8:5-82
14-->G:\Android Studio-java\ApkInjector\app\src\main\AndroidManifest.xml:8:22-79
15
16    <permission
16-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ef9117de45a8cb31d7d2bc2336a6f9d\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
17        android:name="com.iapp.leochen.apkinjector.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
17-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ef9117de45a8cb31d7d2bc2336a6f9d\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
18        android:protectionLevel="signature" />
18-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ef9117de45a8cb31d7d2bc2336a6f9d\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
19
20    <uses-permission android:name="com.iapp.leochen.apkinjector.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
20-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ef9117de45a8cb31d7d2bc2336a6f9d\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
20-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ef9117de45a8cb31d7d2bc2336a6f9d\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
21
22    <application
22-->G:\Android Studio-java\ApkInjector\app\src\main\AndroidManifest.xml:10:5-29:19
23        android:allowBackup="true"
23-->G:\Android Studio-java\ApkInjector\app\src\main\AndroidManifest.xml:11:9-35
24        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
24-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ef9117de45a8cb31d7d2bc2336a6f9d\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
25        android:dataExtractionRules="@xml/data_extraction_rules"
25-->G:\Android Studio-java\ApkInjector\app\src\main\AndroidManifest.xml:12:9-65
26        android:debuggable="true"
27        android:extractNativeLibs="false"
28        android:fullBackupContent="@xml/backup_rules"
28-->G:\Android Studio-java\ApkInjector\app\src\main\AndroidManifest.xml:13:9-54
29        android:icon="@mipmap/ic_launcher"
29-->G:\Android Studio-java\ApkInjector\app\src\main\AndroidManifest.xml:14:9-43
30        android:label="@string/app_name"
30-->G:\Android Studio-java\ApkInjector\app\src\main\AndroidManifest.xml:15:9-41
31        android:roundIcon="@mipmap/ic_launcher_round"
31-->G:\Android Studio-java\ApkInjector\app\src\main\AndroidManifest.xml:16:9-54
32        android:supportsRtl="true"
32-->G:\Android Studio-java\ApkInjector\app\src\main\AndroidManifest.xml:17:9-35
33        android:testOnly="true"
34        android:theme="@style/Theme.ApkInjector" >
34-->G:\Android Studio-java\ApkInjector\app\src\main\AndroidManifest.xml:18:9-49
35        <activity
35-->G:\Android Studio-java\ApkInjector\app\src\main\AndroidManifest.xml:19:9-28:20
36            android:name="com.iapp.leochen.apkinjector.MainActivity"
36-->G:\Android Studio-java\ApkInjector\app\src\main\AndroidManifest.xml:20:13-41
37            android:exported="true"
37-->G:\Android Studio-java\ApkInjector\app\src\main\AndroidManifest.xml:21:13-36
38            android:windowSoftInputMode="adjustNothing" >
38-->G:\Android Studio-java\ApkInjector\app\src\main\AndroidManifest.xml:22:13-56
39            <intent-filter>
39-->G:\Android Studio-java\ApkInjector\app\src\main\AndroidManifest.xml:23:13-27:29
40                <action android:name="android.intent.action.MAIN" />
40-->G:\Android Studio-java\ApkInjector\app\src\main\AndroidManifest.xml:24:17-69
40-->G:\Android Studio-java\ApkInjector\app\src\main\AndroidManifest.xml:24:25-66
41
42                <category android:name="android.intent.category.LAUNCHER" />
42-->G:\Android Studio-java\ApkInjector\app\src\main\AndroidManifest.xml:26:17-77
42-->G:\Android Studio-java\ApkInjector\app\src\main\AndroidManifest.xml:26:27-74
43            </intent-filter>
44        </activity>
45
46        <provider
46-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a4d0770dc3b59b70e02bcf389b0ff922\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
47            android:name="androidx.startup.InitializationProvider"
47-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a4d0770dc3b59b70e02bcf389b0ff922\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
48            android:authorities="com.iapp.leochen.apkinjector.androidx-startup"
48-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a4d0770dc3b59b70e02bcf389b0ff922\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
49            android:exported="false" >
49-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a4d0770dc3b59b70e02bcf389b0ff922\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
50            <meta-data
50-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a4d0770dc3b59b70e02bcf389b0ff922\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
51                android:name="androidx.emoji2.text.EmojiCompatInitializer"
51-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a4d0770dc3b59b70e02bcf389b0ff922\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
52                android:value="androidx.startup" />
52-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a4d0770dc3b59b70e02bcf389b0ff922\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
53            <meta-data
53-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\446fdda5c7d9730889b7c98dc8d984aa\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
54                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
54-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\446fdda5c7d9730889b7c98dc8d984aa\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
55                android:value="androidx.startup" />
55-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\446fdda5c7d9730889b7c98dc8d984aa\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
56            <meta-data
56-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\535e8b5dcb54044a783a9038567689ba\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
57                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
57-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\535e8b5dcb54044a783a9038567689ba\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
58                android:value="androidx.startup" />
58-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\535e8b5dcb54044a783a9038567689ba\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
59        </provider>
60
61        <receiver
61-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\535e8b5dcb54044a783a9038567689ba\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
62            android:name="androidx.profileinstaller.ProfileInstallReceiver"
62-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\535e8b5dcb54044a783a9038567689ba\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
63            android:directBootAware="false"
63-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\535e8b5dcb54044a783a9038567689ba\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
64            android:enabled="true"
64-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\535e8b5dcb54044a783a9038567689ba\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
65            android:exported="true"
65-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\535e8b5dcb54044a783a9038567689ba\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
66            android:permission="android.permission.DUMP" >
66-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\535e8b5dcb54044a783a9038567689ba\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
67            <intent-filter>
67-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\535e8b5dcb54044a783a9038567689ba\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
68                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
68-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\535e8b5dcb54044a783a9038567689ba\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
68-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\535e8b5dcb54044a783a9038567689ba\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
69            </intent-filter>
70            <intent-filter>
70-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\535e8b5dcb54044a783a9038567689ba\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
71                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
71-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\535e8b5dcb54044a783a9038567689ba\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
71-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\535e8b5dcb54044a783a9038567689ba\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
72            </intent-filter>
73            <intent-filter>
73-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\535e8b5dcb54044a783a9038567689ba\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
74                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
74-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\535e8b5dcb54044a783a9038567689ba\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
74-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\535e8b5dcb54044a783a9038567689ba\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
75            </intent-filter>
76            <intent-filter>
76-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\535e8b5dcb54044a783a9038567689ba\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
77                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
77-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\535e8b5dcb54044a783a9038567689ba\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
77-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\535e8b5dcb54044a783a9038567689ba\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
78            </intent-filter>
79        </receiver>
80    </application>
81
82</manifest>
