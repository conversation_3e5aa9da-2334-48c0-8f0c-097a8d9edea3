1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.iapp.leochen.apkinjector"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="29"
9        android:targetSdkVersion="33" />
10
11    <!-- 存储权限 -->
12    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
12-->G:\Android Studio-java\ApkInjector\app\src\main\AndroidManifest.xml:6:5-80
12-->G:\Android Studio-java\ApkInjector\app\src\main\AndroidManifest.xml:6:22-77
13    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
13-->G:\Android Studio-java\ApkInjector\app\src\main\AndroidManifest.xml:7:5-81
13-->G:\Android Studio-java\ApkInjector\app\src\main\AndroidManifest.xml:7:22-78
14    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
14-->G:\Android Studio-java\ApkInjector\app\src\main\AndroidManifest.xml:8:5-82
14-->G:\Android Studio-java\ApkInjector\app\src\main\AndroidManifest.xml:8:22-79
15
16    <permission
16-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ef9117de45a8cb31d7d2bc2336a6f9d\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
17        android:name="com.iapp.leochen.apkinjector.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
17-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ef9117de45a8cb31d7d2bc2336a6f9d\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
18        android:protectionLevel="signature" />
18-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ef9117de45a8cb31d7d2bc2336a6f9d\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
19
20    <uses-permission android:name="com.iapp.leochen.apkinjector.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
20-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ef9117de45a8cb31d7d2bc2336a6f9d\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
20-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ef9117de45a8cb31d7d2bc2336a6f9d\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
21
22    <application
22-->G:\Android Studio-java\ApkInjector\app\src\main\AndroidManifest.xml:10:5-28:19
23        android:allowBackup="true"
23-->G:\Android Studio-java\ApkInjector\app\src\main\AndroidManifest.xml:11:9-35
24        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
24-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ef9117de45a8cb31d7d2bc2336a6f9d\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
25        android:dataExtractionRules="@xml/data_extraction_rules"
25-->G:\Android Studio-java\ApkInjector\app\src\main\AndroidManifest.xml:12:9-65
26        android:debuggable="true"
27        android:extractNativeLibs="false"
28        android:fullBackupContent="@xml/backup_rules"
28-->G:\Android Studio-java\ApkInjector\app\src\main\AndroidManifest.xml:13:9-54
29        android:icon="@mipmap/ic_launcher"
29-->G:\Android Studio-java\ApkInjector\app\src\main\AndroidManifest.xml:14:9-43
30        android:label="@string/app_name"
30-->G:\Android Studio-java\ApkInjector\app\src\main\AndroidManifest.xml:15:9-41
31        android:roundIcon="@mipmap/ic_launcher_round"
31-->G:\Android Studio-java\ApkInjector\app\src\main\AndroidManifest.xml:16:9-54
32        android:supportsRtl="true"
32-->G:\Android Studio-java\ApkInjector\app\src\main\AndroidManifest.xml:17:9-35
33        android:testOnly="true"
34        android:theme="@style/Theme.ApkInjector" >
34-->G:\Android Studio-java\ApkInjector\app\src\main\AndroidManifest.xml:18:9-49
35        <activity
35-->G:\Android Studio-java\ApkInjector\app\src\main\AndroidManifest.xml:19:9-27:20
36            android:name="com.iapp.leochen.apkinjector.MainActivity"
36-->G:\Android Studio-java\ApkInjector\app\src\main\AndroidManifest.xml:20:13-41
37            android:exported="true" >
37-->G:\Android Studio-java\ApkInjector\app\src\main\AndroidManifest.xml:21:13-36
38            <intent-filter>
38-->G:\Android Studio-java\ApkInjector\app\src\main\AndroidManifest.xml:22:13-26:29
39                <action android:name="android.intent.action.MAIN" />
39-->G:\Android Studio-java\ApkInjector\app\src\main\AndroidManifest.xml:23:17-69
39-->G:\Android Studio-java\ApkInjector\app\src\main\AndroidManifest.xml:23:25-66
40
41                <category android:name="android.intent.category.LAUNCHER" />
41-->G:\Android Studio-java\ApkInjector\app\src\main\AndroidManifest.xml:25:17-77
41-->G:\Android Studio-java\ApkInjector\app\src\main\AndroidManifest.xml:25:27-74
42            </intent-filter>
43        </activity>
44
45        <provider
45-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a4d0770dc3b59b70e02bcf389b0ff922\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
46            android:name="androidx.startup.InitializationProvider"
46-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a4d0770dc3b59b70e02bcf389b0ff922\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
47            android:authorities="com.iapp.leochen.apkinjector.androidx-startup"
47-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a4d0770dc3b59b70e02bcf389b0ff922\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
48            android:exported="false" >
48-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a4d0770dc3b59b70e02bcf389b0ff922\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
49            <meta-data
49-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a4d0770dc3b59b70e02bcf389b0ff922\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
50                android:name="androidx.emoji2.text.EmojiCompatInitializer"
50-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a4d0770dc3b59b70e02bcf389b0ff922\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
51                android:value="androidx.startup" />
51-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a4d0770dc3b59b70e02bcf389b0ff922\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
52            <meta-data
52-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\446fdda5c7d9730889b7c98dc8d984aa\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
53                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
53-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\446fdda5c7d9730889b7c98dc8d984aa\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
54                android:value="androidx.startup" />
54-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\446fdda5c7d9730889b7c98dc8d984aa\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
55            <meta-data
55-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\535e8b5dcb54044a783a9038567689ba\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
56                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
56-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\535e8b5dcb54044a783a9038567689ba\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
57                android:value="androidx.startup" />
57-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\535e8b5dcb54044a783a9038567689ba\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
58        </provider>
59
60        <receiver
60-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\535e8b5dcb54044a783a9038567689ba\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
61            android:name="androidx.profileinstaller.ProfileInstallReceiver"
61-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\535e8b5dcb54044a783a9038567689ba\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
62            android:directBootAware="false"
62-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\535e8b5dcb54044a783a9038567689ba\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
63            android:enabled="true"
63-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\535e8b5dcb54044a783a9038567689ba\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
64            android:exported="true"
64-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\535e8b5dcb54044a783a9038567689ba\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
65            android:permission="android.permission.DUMP" >
65-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\535e8b5dcb54044a783a9038567689ba\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
66            <intent-filter>
66-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\535e8b5dcb54044a783a9038567689ba\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
67                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
67-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\535e8b5dcb54044a783a9038567689ba\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
67-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\535e8b5dcb54044a783a9038567689ba\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
68            </intent-filter>
69            <intent-filter>
69-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\535e8b5dcb54044a783a9038567689ba\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
70                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
70-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\535e8b5dcb54044a783a9038567689ba\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
70-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\535e8b5dcb54044a783a9038567689ba\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
71            </intent-filter>
72            <intent-filter>
72-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\535e8b5dcb54044a783a9038567689ba\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
73                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
73-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\535e8b5dcb54044a783a9038567689ba\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
73-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\535e8b5dcb54044a783a9038567689ba\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
74            </intent-filter>
75            <intent-filter>
75-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\535e8b5dcb54044a783a9038567689ba\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
76                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
76-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\535e8b5dcb54044a783a9038567689ba\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
76-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\535e8b5dcb54044a783a9038567689ba\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
77            </intent-filter>
78        </receiver>
79    </application>
80
81</manifest>
