package com.iapp.leochen.apkinjector;

import android.Manifest;
import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ObjectAnimator;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.AsyncTask;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.os.Handler;
import android.os.Looper;
import android.provider.Settings;
import android.util.DisplayMetrics;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.DecelerateInterpolator;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.google.android.material.button.MaterialButton;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class  HomeFragment extends Fragment {
    private static final int PERMISSION_REQUEST_CODE = 100;
    private static final int MANAGE_EXTERNAL_STORAGE_REQUEST_CODE = 101;

    private RecyclerView fileRecyclerView;
    private RecyclerView fileRecyclerViewNext;
    private LinearLayout animationContainer;
    private FileAdapter fileAdapter;
    private FileAdapter fileAdapterNext;
    private TextView currentPathText;
    private MaterialButton favoriteButton;
    private MaterialButton upButton;
    private MaterialButton grantPermissionButton;
    private SwipeRefreshLayout swipeRefreshLayout;
    private View topSection;

    private View permissionRequestLayout;
    private View fileBrowserLayout;
    private View loadingIndicator;

    private File currentDirectory;
    private List<File> fileList;
    private List<File> fileListNext;

    // Animation related
    private boolean isAnimating = false;
    private int screenWidth;

    // File loading optimization
    private boolean isLoading = false;
    private Handler mainHandler;
    private boolean hasInitiallyLoaded = false;

    // 简单缓存机制
    private String lastLoadedPath = null;
    private List<File> lastLoadedFiles = null;
    private long lastLoadTime = 0;

    // Navigation direction
    private enum NavigationDirection {
        DOWN,  // 进入子目录 (向右滑动)
        UP     // 返回上级目录 (向左滑动)
    }

    // 文件过滤相关
    private static final boolean ENABLE_FILE_FILTER = true; // 全局变量，控制是否启用文件过滤
    private static final String[] ALLOWED_EXTENSIONS = {".apk", ".APK", ".apk.1", ".APK.1"};

    /**
     * 检查文件是否应该显示
     * @param file 要检查的文件
     * @return true表示应该显示，false表示应该过滤掉
     */
    private boolean shouldShowFile(File file) {
        // 目录总是显示
        if (file.isDirectory()) {
            return true;
        }

        // 如果未启用过滤，显示所有文件
        if (!ENABLE_FILE_FILTER) {
            return true;
        }

        // 检查文件扩展名
        String fileName = file.getName().toLowerCase();
        for (String extension : ALLOWED_EXTENSIONS) {
            if (fileName.endsWith(extension.toLowerCase())) {
                return true;
            }
        }

        return false; // 不匹配任何允许的扩展名，过滤掉
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_home, container, false);
        
        // 获取屏幕宽度用于动画计算
        DisplayMetrics displayMetrics = getResources().getDisplayMetrics();
        screenWidth = displayMetrics.widthPixels;

        // 初始化主线程Handler
        mainHandler = new Handler(Looper.getMainLooper());

        initViews(view);
        setupRecyclerView();
        setupClickListeners();

        // 初始化目录为 /storage/emulated/0/
        currentDirectory = new File("/storage/emulated/0/");

        // 检查权限并显示相应界面
        checkAndUpdateUI();
        
        return view;
    }
    
    private void initViews(View view) {
        // File browser views
        fileRecyclerView = view.findViewById(R.id.fileRecyclerView);
        fileRecyclerViewNext = view.findViewById(R.id.fileRecyclerViewNext);
        animationContainer = view.findViewById(R.id.animationContainer);
        currentPathText = view.findViewById(R.id.currentPathText);
        favoriteButton = view.findViewById(R.id.favoriteButton);
        upButton = view.findViewById(R.id.upButton);
        fileBrowserLayout = view.findViewById(R.id.fileBrowserLayout);
        loadingIndicator = view.findViewById(R.id.loadingIndicator);
        swipeRefreshLayout = view.findViewById(R.id.swipeRefreshLayout);
        topSection = view.findViewById(R.id.topSection);

        // Permission request views
        permissionRequestLayout = view.findViewById(R.id.permissionRequestLayout);
        grantPermissionButton = permissionRequestLayout.findViewById(R.id.grantPermissionButton);

        fileList = new ArrayList<>();
        fileListNext = new ArrayList<>();
    }
    
    private void setupRecyclerView() {
        // Setup primary RecyclerView with performance optimizations
        fileAdapter = new FileAdapter(fileList, this::onFileClick);
        LinearLayoutManager layoutManager = new LinearLayoutManager(getContext());
        fileRecyclerView.setLayoutManager(layoutManager);
        fileRecyclerView.setAdapter(fileAdapter);

        // 性能优化设置
        fileRecyclerView.setHasFixedSize(true);
        fileRecyclerView.setItemViewCacheSize(20);
        fileRecyclerView.setDrawingCacheEnabled(true);
        fileRecyclerView.setDrawingCacheQuality(View.DRAWING_CACHE_QUALITY_HIGH);

        // 使用默认动画，但优化性能
        if (fileRecyclerView.getItemAnimator() != null) {
            fileRecyclerView.getItemAnimator().setChangeDuration(150);
            fileRecyclerView.getItemAnimator().setMoveDuration(150);
        }

        // Setup next RecyclerView for parallel animations
        fileAdapterNext = new FileAdapter(fileListNext, this::onFileClick);
        LinearLayoutManager layoutManagerNext = new LinearLayoutManager(getContext());
        fileRecyclerViewNext.setLayoutManager(layoutManagerNext);
        fileRecyclerViewNext.setAdapter(fileAdapterNext);

        // 性能优化设置
        fileRecyclerViewNext.setHasFixedSize(true);
        fileRecyclerViewNext.setItemViewCacheSize(20);
        fileRecyclerViewNext.setDrawingCacheEnabled(true);
        fileRecyclerViewNext.setDrawingCacheQuality(View.DRAWING_CACHE_QUALITY_HIGH);

        // 使用默认动画，但优化性能
        if (fileRecyclerViewNext.getItemAnimator() != null) {
            fileRecyclerViewNext.getItemAnimator().setChangeDuration(150);
            fileRecyclerViewNext.getItemAnimator().setMoveDuration(150);
        }

        // 添加滚动监听器来控制顶部阴影和性能优化
        setupScrollListener();

        // 获取屏幕宽度用于动画计算
        DisplayMetrics displayMetrics = new DisplayMetrics();
        if (getActivity() != null) {
            getActivity().getWindowManager().getDefaultDisplay().getMetrics(displayMetrics);
            screenWidth = displayMetrics.widthPixels;
        }

        // 添加滚动状态监听器来优化性能
        fileRecyclerView.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
                super.onScrollStateChanged(recyclerView, newState);
                // 在滚动时禁用一些操作来提高性能
                if (newState == RecyclerView.SCROLL_STATE_DRAGGING ||
                    newState == RecyclerView.SCROLL_STATE_SETTLING) {
                    // 滚动时暂停文件加载
                    isLoading = true;
                } else if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                    // 滚动停止时恢复
                    isLoading = false;
                }
            }
        });
    }

    private void setupScrollListener() {
        // 监听SwipeRefreshLayout内的滚动事件
        fileRecyclerView.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);
                // 强制移除所有阴影效果
                forceRemoveElevation();
            }

            @Override
            public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
                super.onScrollStateChanged(recyclerView, newState);
                // 强制移除所有阴影效果
                forceRemoveElevation();
            }
        });

        // 添加全局布局监听器，确保布局变化时也移除阴影
        if (topSection != null) {
            topSection.getViewTreeObserver().addOnGlobalLayoutListener(() -> {
                forceRemoveElevation();
            });
        }
    }

    private void forceRemoveElevation() {
        if (topSection != null) {
            topSection.setElevation(0f);
            topSection.setTranslationZ(0f);
            // 强制设置StateListAnimator为null
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                topSection.setStateListAnimator(null);
            }
        }

        // 也确保父容器没有阴影
        if (fileBrowserLayout != null) {
            fileBrowserLayout.setElevation(0f);
            fileBrowserLayout.setTranslationZ(0f);
        }
    }


    
    private void setupClickListeners() {
        favoriteButton.setOnClickListener(v -> {
            // TODO: 实现收藏功能
            Toast.makeText(getContext(), "收藏功能待实现", Toast.LENGTH_SHORT).show();
        });

        upButton.setOnClickListener(v -> {
            if (!isAnimating) {
                navigateUp();
            }
        });

        grantPermissionButton.setOnClickListener(v -> requestStoragePermission());

        // 设置下拉刷新监听器
        swipeRefreshLayout.setOnRefreshListener(() -> {
            // 清除缓存并重新加载当前目录
            clearCache();
            loadFilesWithAnimation(null);

            // 延迟停止刷新动画，让用户看到刷新效果
            new Handler(Looper.getMainLooper()).postDelayed(() -> {
                if (swipeRefreshLayout != null) {
                    swipeRefreshLayout.setRefreshing(false);
                }
            }, 500);
        });

        // 使用原生样式 - 移除所有自定义设置，使用系统默认

        // 强制移除系统自动添加的阴影
        removeSystemElevation();
    }

    private void checkAndUpdateUI() {
        if (checkStoragePermission()) {
            showFileBrowser();
            loadFilesInitial();
            hasInitiallyLoaded = true;
        } else {
            showPermissionRequest();
        }
    }

    private void showFileBrowser() {
        fileBrowserLayout.setVisibility(View.VISIBLE);
        permissionRequestLayout.setVisibility(View.GONE);
    }

    private void showPermissionRequest() {
        fileBrowserLayout.setVisibility(View.GONE);
        permissionRequestLayout.setVisibility(View.VISIBLE);
    }

    private boolean checkStoragePermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            return Environment.isExternalStorageManager();
        } else {
            return ContextCompat.checkSelfPermission(requireContext(),
                    Manifest.permission.READ_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED;
        }
    }

    private void requestStoragePermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            // 对于Android 11+，跳转到系统设置页面
            try {
                Intent intent = new Intent(Settings.ACTION_MANAGE_APP_ALL_FILES_ACCESS_PERMISSION);
                intent.setData(Uri.parse("package:" + requireContext().getPackageName()));
                startActivityForResult(intent, MANAGE_EXTERNAL_STORAGE_REQUEST_CODE);
            } catch (Exception e) {
                // 如果上面的方式失败，使用通用设置页面
                Intent intent = new Intent(Settings.ACTION_MANAGE_ALL_FILES_ACCESS_PERMISSION);
                startActivityForResult(intent, MANAGE_EXTERNAL_STORAGE_REQUEST_CODE);
            }
        } else {
            ActivityCompat.requestPermissions(requireActivity(),
                    new String[]{Manifest.permission.READ_EXTERNAL_STORAGE},
                    PERMISSION_REQUEST_CODE);
        }
    }
    
    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == MANAGE_EXTERNAL_STORAGE_REQUEST_CODE) {
            // 检查权限状态并更新UI
            checkAndUpdateUI();
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == PERMISSION_REQUEST_CODE) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                checkAndUpdateUI();
            } else {
                Toast.makeText(getContext(), "需要存储权限才能浏览文件", Toast.LENGTH_LONG).show();
            }
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        // 只在首次加载或权限状态变化时重新加载
        if (!hasInitiallyLoaded) {
            checkAndUpdateUI();
        }
        // 确保移除系统阴影
        removeSystemElevation();
    }
    
    private void loadFiles() {
        loadFilesWithAnimation(null);
    }

    private void loadFilesInitial() {
        // 初始加载使用同步方式，避免线程切换开销
        if (currentDirectory == null || !currentDirectory.exists()) {
            return;
        }

        currentPathText.setText(currentDirectory.getAbsolutePath());

        try {
            long startTime = System.currentTimeMillis();
            File[] files = currentDirectory.listFiles();

            if (files == null || files.length == 0) {
                fileList.clear();
                fileAdapter.notifyDataSetChanged();
                return;
            }

            // 简化的文件分类和排序
            List<File> allFiles = new ArrayList<>(files.length);
            List<File> directories = new ArrayList<>(files.length / 4);
            List<File> regularFiles = new ArrayList<>(files.length);

            // 单次遍历分类，应用文件过滤
            for (File file : files) {
                if (!shouldShowFile(file)) {
                    continue; // 跳过被过滤的文件
                }

                if (file.isDirectory()) {
                    directories.add(file);
                } else {
                    regularFiles.add(file);
                }
            }

            // 快速排序
            if (directories.size() > 1) {
                directories.sort((f1, f2) -> f1.getName().compareToIgnoreCase(f2.getName()));
            }
            if (regularFiles.size() > 1) {
                regularFiles.sort((f1, f2) -> f1.getName().compareToIgnoreCase(f2.getName()));
            }

            // 构建最终列表
            allFiles.addAll(directories);
            allFiles.addAll(regularFiles);

            // 更新UI
            fileList.clear();
            fileList.addAll(allFiles);
            fileAdapter.notifyDataSetChanged();

            // 更新缓存
            lastLoadedPath = currentDirectory.getAbsolutePath();
            lastLoadedFiles = new ArrayList<>(allFiles);
            lastLoadTime = System.currentTimeMillis();

            long endTime = System.currentTimeMillis();
            System.out.println("初始文件加载耗时: " + (endTime - startTime) + "ms, 文件数量: " + files.length);

        } catch (Exception e) {
            Toast.makeText(getContext(), "加载文件失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
        }
    }

    private void loadFilesWithAnimation(NavigationDirection direction) {
        if (currentDirectory == null || !currentDirectory.exists() || isLoading) {
            return;
        }

        isLoading = true;

        // 立即更新路径显示
        currentPathText.setText(currentDirectory.getAbsolutePath());

        // 对于小目录，直接同步加载，避免异步开销
        File[] files = currentDirectory.listFiles();
        if (files != null && files.length < 100) {
            // 小目录同步加载
            loadFilesSyncFast(files, direction);
        } else {
            // 大目录异步加载
            if (direction == null) {
                showLoadingIndicator();
            }
            loadFilesAsync(currentDirectory, direction);
        }
    }

    private void loadFilesSyncFast(File[] files, NavigationDirection direction) {
        try {
            List<File> allFiles = new ArrayList<>(files.length);
            List<File> directories = new ArrayList<>();
            List<File> regularFiles = new ArrayList<>();

            // 快速分类，应用文件过滤
            for (File file : files) {
                if (!shouldShowFile(file)) {
                    continue; // 跳过被过滤的文件
                }

                if (file.isDirectory()) {
                    directories.add(file);
                } else {
                    regularFiles.add(file);
                }
            }

            // 快速排序
            if (directories.size() > 1) {
                directories.sort((f1, f2) -> f1.getName().compareToIgnoreCase(f2.getName()));
            }
            if (regularFiles.size() > 1) {
                regularFiles.sort((f1, f2) -> f1.getName().compareToIgnoreCase(f2.getName()));
            }

            allFiles.addAll(directories);
            allFiles.addAll(regularFiles);

            // 更新缓存
            lastLoadedPath = currentDirectory.getAbsolutePath();
            lastLoadedFiles = new ArrayList<>(allFiles);
            lastLoadTime = System.currentTimeMillis();

            isLoading = false;
            updateFileListUI(allFiles, direction);

        } catch (Exception e) {
            isLoading = false;
            Toast.makeText(getContext(), "加载文件失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
        }
    }

    private void showLoadingIndicator() {
        // 只在非刷新状态下显示加载指示器，避免与下拉刷新冲突
        if (!swipeRefreshLayout.isRefreshing()) {
            loadingIndicator.setVisibility(View.VISIBLE);
            fileRecyclerView.setVisibility(View.GONE);
        }
    }

    private void hideLoadingIndicator() {
        loadingIndicator.setVisibility(View.GONE);
        fileRecyclerView.setVisibility(View.VISIBLE);
    }

    private void clearCache() {
        lastLoadedPath = null;
        lastLoadedFiles = null;
        lastLoadTime = 0;
    }

    private void removeSystemElevation() {
        // 强制移除所有可能的系统阴影
        forceRemoveElevation();

        // 使用Handler确保在布局完成后再次移除阴影
        new Handler(Looper.getMainLooper()).post(() -> forceRemoveElevation());

        // 延迟再次确保
        new Handler(Looper.getMainLooper()).postDelayed(() -> forceRemoveElevation(), 100);
        new Handler(Looper.getMainLooper()).postDelayed(() -> forceRemoveElevation(), 500);
    }

    private void loadFilesAsync(File directory, NavigationDirection direction) {
        String currentPath = directory.getAbsolutePath();

        // 检查缓存 - 如果是最近访问的目录且时间不超过5秒，直接使用缓存
        if (currentPath.equals(lastLoadedPath) &&
            lastLoadedFiles != null &&
            (System.currentTimeMillis() - lastLoadTime) < 5000) {

            mainHandler.post(() -> {
                isLoading = false;
                hideLoadingIndicator();
                updateFileListUI(lastLoadedFiles, direction);
            });
            return;
        }

        // 在后台线程执行文件I/O操作
        new Thread(() -> {
            try {
                long startTime = System.currentTimeMillis();
                File[] files = directory.listFiles();

                if (files == null || files.length == 0) {
                    // 空目录，直接更新UI
                    mainHandler.post(() -> {
                        isLoading = false;
                        hideLoadingIndicator();
                        updateFileListUI(new ArrayList<File>(), direction);
                    });
                    return;
                }

                // 简化的文件分类和排序
                List<File> allFiles = new ArrayList<>(files.length);

                // 预分配容量，避免动态扩容
                List<File> directories = new ArrayList<>(files.length / 4); // 估算文件夹数量
                List<File> regularFiles = new ArrayList<>(files.length);

                // 单次遍历分类，应用文件过滤
                for (File file : files) {
                    if (!shouldShowFile(file)) {
                        continue; // 跳过被过滤的文件
                    }

                    if (file.isDirectory()) {
                        directories.add(file);
                    } else {
                        regularFiles.add(file);
                    }
                }

                // 使用最快的排序方法
                if (directories.size() > 1) {
                    directories.sort((f1, f2) -> f1.getName().compareToIgnoreCase(f2.getName()));
                }
                if (regularFiles.size() > 1) {
                    regularFiles.sort((f1, f2) -> f1.getName().compareToIgnoreCase(f2.getName()));
                }

                // 直接构建最终列表，避免多次复制
                allFiles.addAll(directories);
                allFiles.addAll(regularFiles);

                // 更新缓存
                lastLoadedPath = currentPath;
                lastLoadedFiles = new ArrayList<>(allFiles);
                lastLoadTime = System.currentTimeMillis();

                // 回到主线程更新UI
                mainHandler.post(() -> {
                    isLoading = false;
                    hideLoadingIndicator();
                    updateFileListUI(allFiles, direction);
                });

            } catch (Exception e) {
                // 错误处理
                mainHandler.post(() -> {
                    isLoading = false;
                    hideLoadingIndicator();
                    Toast.makeText(getContext(), "加载文件失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                });
            }
        }).start();
    }



    private void updateFileListUI(List<File> newFileList, NavigationDirection direction) {
        // 如果没有指定方向或者正在动画中，直接更新
        if (direction == null || isAnimating) {
            fileList.clear();
            fileList.addAll(newFileList);
            fileAdapter.notifyDataSetChanged();
            return;
        }

        // 执行动画
        animateListTransition(newFileList, direction);
    }

    private void animateListTransition(List<File> newFileList, NavigationDirection direction) {
        if (isAnimating) return;

        isAnimating = true;

        // 准备下一个列表
        fileListNext.clear();
        fileListNext.addAll(newFileList);
        fileAdapterNext.notifyDataSetChanged();

        // 获取屏幕宽度
        float moveDistance = screenWidth;

        // 设置初始位置 - 确保新列表在屏幕外准备好
        float startX = (direction == NavigationDirection.DOWN) ? moveDistance : -moveDistance;
        float endX = (direction == NavigationDirection.DOWN) ? -moveDistance : moveDistance;

        // 显示新列表并设置初始位置
        fileRecyclerViewNext.setTranslationX(startX);
        fileRecyclerViewNext.setVisibility(View.VISIBLE);
        fileRecyclerViewNext.setAlpha(1.0f);

        // 创建平滑的并行动画
        ObjectAnimator currentOut = ObjectAnimator.ofFloat(fileRecyclerView, "translationX", 0f, endX);
        ObjectAnimator nextIn = ObjectAnimator.ofFloat(fileRecyclerViewNext, "translationX", startX, 0f);

        // 设置动画时长 - 稍微延长以确保平滑过渡
        int duration = 300;
        currentOut.setDuration(duration);
        nextIn.setDuration(duration);

        // 使用更自然的插值器 - 稍微调整参数以获得更平滑的效果
        DecelerateInterpolator interpolator = new DecelerateInterpolator(1.0f);
        currentOut.setInterpolator(interpolator);
        nextIn.setInterpolator(interpolator);

        // 添加轻微的淡入淡出效果，进一步减少视觉跳跃
        ObjectAnimator currentFadeOut = ObjectAnimator.ofFloat(fileRecyclerView, "alpha", 1.0f, 0.8f);
        ObjectAnimator nextFadeIn = ObjectAnimator.ofFloat(fileRecyclerViewNext, "alpha", 0.8f, 1.0f);

        currentFadeOut.setDuration(duration / 2);
        nextFadeIn.setDuration(duration / 2);
        nextFadeIn.setStartDelay(duration / 2);

        // 添加动画监听器
        nextIn.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                // 动画完成后交换数据
                fileList.clear();
                fileList.addAll(newFileList);
                fileAdapter.notifyDataSetChanged();

                // 重置位置和状态
                fileRecyclerView.setTranslationX(0f);
                fileRecyclerView.setAlpha(1.0f);
                fileRecyclerViewNext.setTranslationX(0f);
                fileRecyclerViewNext.setAlpha(1.0f);
                fileRecyclerViewNext.setVisibility(View.GONE);

                isAnimating = false;
            }
        });

        // 同时启动所有动画，确保完全同步
        currentOut.start();
        nextIn.start();
        currentFadeOut.start();
        nextFadeIn.start();
    }



    private void onFileClick(File file) {
        if (file.isDirectory() && !isAnimating) {
            currentDirectory = file;
            loadFilesWithAnimation(NavigationDirection.DOWN);
        } else if (!file.isDirectory()) {
            // TODO: 处理文件点击
            Toast.makeText(getContext(), "点击了文件: " + file.getName(), Toast.LENGTH_SHORT).show();
        }
    }

    private void navigateUp() {
        if (currentDirectory != null && currentDirectory.getParent() != null && !isAnimating) {
            currentDirectory = currentDirectory.getParentFile();
            loadFilesWithAnimation(NavigationDirection.UP);
        }
    }

    // 处理返回键，返回true表示已处理，false表示未处理
    public boolean onBackPressed() {
        if (currentDirectory != null && !isAnimating) {
            // 检查是否已经在根目录
            if (currentDirectory.getAbsolutePath().equals("/")) {
                return false; // 在根目录，让Activity处理退出逻辑
            }

            // 不在根目录，导航到上级目录
            if (currentDirectory.getParent() != null) {
                currentDirectory = currentDirectory.getParentFile();
                loadFilesWithAnimation(NavigationDirection.UP);
                return true; // 已处理返回键
            }
        }
        return false; // 未处理，让Activity处理
    }
}
