<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="24dp">

    <!-- Dialog Title -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="转跳路径"
        android:textAppearance="@style/TextAppearance.Material3.HeadlineSmall"
        android:textColor="?attr/colorOnSurface"
        android:layout_marginBottom="16dp" />

    <!-- Current Path Display -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="当前路径:"
        android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
        android:textColor="?attr/colorOnSurfaceVariant"
        android:layout_marginBottom="4dp" />

    <TextView
        android:id="@+id/currentPathDisplay"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="/storage/emulated/0/"
        android:textAppearance="@style/TextAppearance.Material3.BodyLarge"
        android:textColor="?attr/colorOnSurface"
        android:background="@drawable/path_display_background"
        android:padding="12dp"
        android:layout_marginBottom="20dp"
        android:maxLines="1"
        android:ellipsize="end"
        android:textIsSelectable="true" />

    <!-- Target Path Input -->
    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/targetPathInputLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="转跳路径"
        android:layout_marginBottom="24dp"
        style="@style/Widget.Material3.TextInputLayout.OutlinedBox">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/targetPathEditText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="textUri"
            android:maxLines="3"
            android:scrollHorizontally="false" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- Action Buttons -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="end">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/cancelButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="取消"
            android:layout_marginEnd="8dp"
            style="@style/Widget.Material3.Button.TextButton" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/navigateButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="转跳"
            style="@style/Widget.Material3.Button.UnelevatedButton" />

    </LinearLayout>

</LinearLayout>
