<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="4dp"
    android:layout_marginVertical="2dp"
    style="@style/Widget.Material3.CardView.Filled">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:gravity="center_vertical"
        android:background="?attr/selectableItemBackground">

        <ImageView
            android:id="@+id/fileIcon"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_marginEnd="16dp"
            android:src="@drawable/ic_folder"
             />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/fileName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="文件名"
                android:textAppearance="@style/TextAppearance.Material3.BodyLarge"
                android:textColor="?attr/colorOnSurface"
                android:maxLines="1"
                android:ellipsize="end" />

            <TextView
                android:id="@+id/fileDate"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="2025-07-19 20:01"
                android:textAppearance="@style/TextAppearance.Material3.BodySmall"
                android:textColor="?attr/colorOnSurfaceVariant"
                android:layout_marginTop="2dp" />

        </LinearLayout>

        <ImageView
            android:id="@+id/arrowIcon"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_chevron_right"
            app:tint="?attr/colorOnSurfaceVariant" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
