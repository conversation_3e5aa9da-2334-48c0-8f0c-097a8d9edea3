#Wed Jul 23 18:32:55 HKT 2025
com.iapp.leochen.apkinjector.app-main-35\:/anim/slide_in_left.xml=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\anim_slide_in_left.xml.flat
com.iapp.leochen.apkinjector.app-main-35\:/anim/slide_in_left_smooth.xml=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\anim_slide_in_left_smooth.xml.flat
com.iapp.leochen.apkinjector.app-main-35\:/anim/slide_in_right.xml=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\anim_slide_in_right.xml.flat
com.iapp.leochen.apkinjector.app-main-35\:/anim/slide_in_right_smooth.xml=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\anim_slide_in_right_smooth.xml.flat
com.iapp.leochen.apkinjector.app-main-35\:/anim/slide_out_left.xml=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\anim_slide_out_left.xml.flat
com.iapp.leochen.apkinjector.app-main-35\:/anim/slide_out_left_smooth.xml=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\anim_slide_out_left_smooth.xml.flat
com.iapp.leochen.apkinjector.app-main-35\:/anim/slide_out_right.xml=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\anim_slide_out_right.xml.flat
com.iapp.leochen.apkinjector.app-main-35\:/anim/slide_out_right_smooth.xml=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\anim_slide_out_right_smooth.xml.flat
com.iapp.leochen.apkinjector.app-main-35\:/anim/slide_parallel_left.xml=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\anim_slide_parallel_left.xml.flat
com.iapp.leochen.apkinjector.app-main-35\:/anim/slide_parallel_right.xml=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\anim_slide_parallel_right.xml.flat
com.iapp.leochen.apkinjector.app-main-35\:/animator/no_elevation.xml=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\animator_no_elevation.xml.flat
com.iapp.leochen.apkinjector.app-main-35\:/drawable/gradient_separator.xml=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_gradient_separator.xml.flat
com.iapp.leochen.apkinjector.app-main-35\:/drawable/ic_arrow_upward.xml=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_arrow_upward.xml.flat
com.iapp.leochen.apkinjector.app-main-35\:/drawable/ic_bookmark_border.xml=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_bookmark_border.xml.flat
com.iapp.leochen.apkinjector.app-main-35\:/drawable/ic_chevron_right.xml=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_chevron_right.xml.flat
com.iapp.leochen.apkinjector.app-main-35\:/drawable/ic_file.xml=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_file.xml.flat
com.iapp.leochen.apkinjector.app-main-35\:/drawable/ic_folder.xml=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_folder.xml.flat
com.iapp.leochen.apkinjector.app-main-35\:/drawable/ic_home.xml=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_home.xml.flat
com.iapp.leochen.apkinjector.app-main-35\:/drawable/ic_info.xml=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_info.xml.flat
com.iapp.leochen.apkinjector.app-main-35\:/drawable/ic_launcher_background.xml=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher_background.xml.flat
com.iapp.leochen.apkinjector.app-main-35\:/drawable/ic_launcher_foreground.xml=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher_foreground.xml.flat
com.iapp.leochen.apkinjector.app-main-35\:/drawable/ic_navigation.xml=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_navigation.xml.flat
com.iapp.leochen.apkinjector.app-main-35\:/drawable/path_display_background.xml=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_path_display_background.xml.flat
com.iapp.leochen.apkinjector.app-main-35\:/layout/activity_main.xml=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_main.xml.flat
com.iapp.leochen.apkinjector.app-main-35\:/layout/dialog_path_navigation.xml=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_path_navigation.xml.flat
com.iapp.leochen.apkinjector.app-main-35\:/layout/fragment_about.xml=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_about.xml.flat
com.iapp.leochen.apkinjector.app-main-35\:/layout/fragment_home.xml=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_home.xml.flat
com.iapp.leochen.apkinjector.app-main-35\:/layout/item_file.xml=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_file.xml.flat
com.iapp.leochen.apkinjector.app-main-35\:/layout/layout_permission_request.xml=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_layout_permission_request.xml.flat
com.iapp.leochen.apkinjector.app-main-35\:/menu/bottom_navigation_menu.xml=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_bottom_navigation_menu.xml.flat
com.iapp.leochen.apkinjector.app-main-35\:/mipmap-anydpi/ic_launcher.xml=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi_ic_launcher.xml.flat
com.iapp.leochen.apkinjector.app-main-35\:/mipmap-anydpi/ic_launcher_round.xml=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi_ic_launcher_round.xml.flat
com.iapp.leochen.apkinjector.app-main-35\:/mipmap-hdpi/ic_launcher.webp=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher.webp.flat
com.iapp.leochen.apkinjector.app-main-35\:/mipmap-hdpi/ic_launcher_round.webp=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher_round.webp.flat
com.iapp.leochen.apkinjector.app-main-35\:/mipmap-mdpi/ic_launcher.webp=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher.webp.flat
com.iapp.leochen.apkinjector.app-main-35\:/mipmap-mdpi/ic_launcher_round.webp=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher_round.webp.flat
com.iapp.leochen.apkinjector.app-main-35\:/mipmap-xhdpi/ic_launcher.webp=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher.webp.flat
com.iapp.leochen.apkinjector.app-main-35\:/mipmap-xhdpi/ic_launcher_round.webp=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher_round.webp.flat
com.iapp.leochen.apkinjector.app-main-35\:/mipmap-xxhdpi/ic_launcher.webp=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher.webp.flat
com.iapp.leochen.apkinjector.app-main-35\:/mipmap-xxhdpi/ic_launcher_round.webp=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher_round.webp.flat
com.iapp.leochen.apkinjector.app-main-35\:/mipmap-xxxhdpi/ic_launcher.webp=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher.webp.flat
com.iapp.leochen.apkinjector.app-main-35\:/mipmap-xxxhdpi/ic_launcher_round.webp=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher_round.webp.flat
com.iapp.leochen.apkinjector.app-main-35\:/xml/backup_rules.xml=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_backup_rules.xml.flat
com.iapp.leochen.apkinjector.app-main-35\:/xml/data_extraction_rules.xml=G\:\\Android Studio-java\\ApkInjector\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_data_extraction_rules.xml.flat
